name: Deploy to Production

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Checkout the repository
      - name: Checkout code
        uses: actions/checkout@v4

      # Set up Python
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      # Install dependencies
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      # Authenticate with Google Cloud
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      # Set up Google Cloud SDK
      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      # Create Firebase credentials file
      - name: Create Firebase credentials file
        run: |
          mkdir -p firebase
          echo '${{ secrets.FIREBASE_CREDENTIALS_PROD }}' > firebase/serviceAccountKey.json

      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      # Login to Google Container Registry
      - name: <PERSON>gin to Google Container Registry
        uses: docker/login-action@v2
        with:
          registry: gcr.io
          username: _json_key
          password: ${{ secrets.GCP_SA_KEY }}

      # Build and push multi-platform Docker image
      - name: Build and push multi-platform Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: gcr.io/${{ secrets.GCP_PROJECT_ID }}/admesh-api:latest

      # Deploy to Cloud Run
      - name: Deploy to Cloud Run
        uses: google-github-actions/deploy-cloudrun@v2
        with:
          service: admesh-protocol
          region: us-central1
          source: .
          env_vars: |
            ENV=production
            ENVIRONMENT=production
            PORT=8080
            GOOGLE_APPLICATION_CREDENTIALS=firebase/serviceAccountKey.json
            LOG_LEVEL=WARNING
            DEBUG=false
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
            STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }}
            STRIPE_WEBHOOK_SECRET=${{ secrets.STRIPE_WEBHOOK_SECRET }}
            RESEND_API_KEY=${{ secrets.RESEND_API_KEY }}
