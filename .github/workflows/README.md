# AdMesh GitHub Actions Workflows

This directory contains GitHub Actions workflows for deploying the AdMesh Protocol to different environments.

## Workflows

### 1. Deploy to Staging (`deploy-staging.yml`)

This workflow deploys the application to the staging environment on Google Cloud Run when changes are pushed to the `staging` branch.

Key features:
- Deploys to `admesh-protocol-staging` Cloud Run service
- Uses staging Firebase credentials
- Sets environment variables for the staging environment

### 2. Deploy to Production (`deploy-production.yml`)

This workflow deploys the application to the production environment on Google Cloud Run when changes are pushed to the `main` branch.

Key features:
- Deploys to `admesh-protocol` Cloud Run service
- Uses production Firebase credentials
- Sets environment variables for the production environment

## Required Secrets

The following secrets must be configured in the GitHub repository:

- `GCP_PROJECT_ID`: Google Cloud project ID
- `GCP_SA_KEY`: Google Cloud service account key (JSON)
- `FIREBASE_CREDENTIALS_STAGING`: Firebase service account credentials for staging
- `FIREBASE_CREDENTIALS_PROD`: Firebase service account credentials for production

## Setup Instructions

For detailed setup instructions, see [docs/github-actions-setup.md](../../docs/github-actions-setup.md).
