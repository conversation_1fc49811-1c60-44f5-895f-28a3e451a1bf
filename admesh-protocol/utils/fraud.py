from google.cloud import firestore
from datetime import datetime, timedelta
from firebase.config import get_db

db = get_db()
def detect_fraud_for_offer(offer_id: str):
    now = datetime.utcnow()
    one_day_ago = now - timedelta(days=1)

    clicks = db.collection("clicks").where("offer_id", "==", offer_id).stream()
    conversions = db.collection("conversions").where("offer_id", "==", offer_id).stream()

    click_count = len([doc for doc in clicks])
    conv_count = len([doc for doc in conversions])

    if click_count > 100 and conv_count == 0:
        db.collection("flags").add({
            "type": "no_conversion_spam",
            "actor_id": offer_id,
            "offer_id": offer_id,
            "reason": "100+ clicks, 0 conversions",
            "score": 20,
            "detected_at": now.isoformat()
        })
        return True

    if conv_count > 0 and (conv_count / click_count) > 0.8 and click_count > 50:
        db.collection("flags").add({
            "type": "ctr_suspicious",
            "actor_id": offer_id,
            "offer_id": offer_id,
            "reason": "Conversion rate > 80%",
            "score": 10,
            "detected_at": now.isoformat()
        })
        return True

    return False
