# AdMesh Protocol

**AdMesh** is an open protocol for agent-native, intent-driven offer monetization.

It enables:
- Agents (GPTs, extensions, apps) to monetize user intent
- Brands to register structured offers with payout rules
- Attribution via click + conversion tracking
- Transparent earnings & trust scoring

Inspired by OpenRTB and Stripe.

## 🔁 Core Flow

1. Brands register offers (`POST /offers/register`)
2. Agents query offers (`GET /offers/discover`)
3. Users click (`POST /click`)
4. Users convert (`POST /conversion/log`)
5. Agents earn (`/earnings`)
6. Trust/fraud is calculated automatically

## 🔐 Auth

- Firebase Auth for Brands + Agents (Bearer tokens)
- Firestore backend (MVP)

## 📦 Offer Schema

See [`offer-schema.md`](./offer-schema.md)

## 🧠 Trust Score

See [`trust-score.md`](./trust-score.md)

## 💰 Earnings

See [`earnings-model.md`](./earnings-model.md)
