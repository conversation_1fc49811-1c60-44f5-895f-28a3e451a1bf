"""
Custom claims management for Firebase users
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from google.cloud import firestore
from auth.deps import verify_firebase_token, require_role
import logging

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

class UpdateCustomClaimsPayload(BaseModel):
    """Payload for updating custom claims"""
    user_id: str
    claims: Dict[str, Any]
    merge: bool = True  # Whether to merge with existing claims or replace them

class GetCustomClaimsResponse(BaseModel):
    """Response for getting custom claims"""
    user_id: str
    claims: Dict[str, Any]
    email: Optional[str] = None
    display_name: Optional[str] = None

@router.post("/auth/custom-claims/update")
async def update_custom_claims(
    payload: UpdateCustomClaimsPayload,
    user = Depends(require_role("admin"))
):
    """
    Update custom claims for a user
    
    This endpoint allows admins to update custom claims for any user.
    Claims can be merged with existing claims or replace them entirely.
    """
    try:
        target_uid = payload.user_id
        new_claims = payload.claims
        merge = payload.merge
        
        # Get the user's current custom claims
        try:
            user_record = firebase_auth.get_user(target_uid)
        except Exception as e:
            raise HTTPException(status_code=404, detail=f"User not found: {str(e)}")
            
        current_claims = dict(user_record.custom_claims or {})
        
        # Merge or replace claims
        if merge:
            # Merge new claims with existing claims
            updated_claims = {**current_claims, **new_claims}
        else:
            # Replace existing claims with new claims
            updated_claims = new_claims
        
        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(target_uid, updated_claims)
        
        # Log the update
        db.collection("custom_claims_updates").add({
            "user_id": target_uid,
            "updated_by": user["uid"],
            "previous_claims": current_claims,
            "new_claims": updated_claims,
            "timestamp": firestore.SERVER_TIMESTAMP
        })
        
        return {
            "status": "success",
            "message": "Custom claims updated successfully",
            "user_id": target_uid,
            "previous_claims": current_claims,
            "updated_claims": updated_claims
        }
    except Exception as e:
        logger.exception(f"Failed to update custom claims: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update custom claims: {str(e)}")

@router.get("/auth/custom-claims/{user_id}")
async def get_custom_claims(
    user_id: str,
    user = Depends(require_role("admin"))
):
    """
    Get custom claims for a user
    
    This endpoint allows admins to get the custom claims for any user.
    """
    try:
        # Get the user's current custom claims
        try:
            user_record = firebase_auth.get_user(user_id)
        except Exception as e:
            raise HTTPException(status_code=404, detail=f"User not found: {str(e)}")
            
        claims = dict(user_record.custom_claims or {})
        
        return GetCustomClaimsResponse(
            user_id=user_id,
            claims=claims,
            email=user_record.email,
            display_name=user_record.display_name
        )
    except Exception as e:
        logger.exception(f"Failed to get custom claims: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get custom claims: {str(e)}")

@router.post("/auth/custom-claims/verify-email")
async def verify_email(
    user = Depends(verify_firebase_token)
):
    """
    Mark a user's email as verified in custom claims
    
    This endpoint allows users to mark their email as verified in custom claims.
    """
    try:
        uid = user["uid"]
        
        # Get the user's current custom claims
        try:
            user_record = firebase_auth.get_user(uid)
        except Exception as e:
            raise HTTPException(status_code=404, detail=f"User not found: {str(e)}")
            
        current_claims = dict(user_record.custom_claims or {})
        
        # Update email_verified claim
        updated_claims = {**current_claims, "email_verified": True}
        
        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(uid, updated_claims)
        
        # Update user document in Firestore
        user_ref = db.collection("users").document(uid)
        if user_ref.get().exists:
            user_ref.update({
                "email_verified": True,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
        
        return {
            "status": "success",
            "message": "Email marked as verified in custom claims",
            "user_id": uid,
            "updated_claims": updated_claims
        }
    except Exception as e:
        logger.exception(f"Failed to verify email in custom claims: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify email in custom claims: {str(e)}")

@router.post("/auth/custom-claims/badge/{badge_id}")
async def add_badge_claim(
    badge_id: str,
    user = Depends(verify_firebase_token)
):
    """
    Add a badge to a user's custom claims
    
    This endpoint allows users to add a badge to their custom claims.
    """
    try:
        uid = user["uid"]
        
        # Get the user's current custom claims
        try:
            user_record = firebase_auth.get_user(uid)
        except Exception as e:
            raise HTTPException(status_code=404, detail=f"User not found: {str(e)}")
            
        current_claims = dict(user_record.custom_claims or {})
        
        # Get current badges or initialize empty list
        badges = current_claims.get("badges", [])
        
        # Add badge if not already present
        if badge_id not in badges:
            badges.append(badge_id)
        
        # Update badges claim
        updated_claims = {**current_claims, "badges": badges}
        
        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(uid, updated_claims)
        
        return {
            "status": "success",
            "message": f"Badge {badge_id} added to custom claims",
            "user_id": uid,
            "updated_claims": updated_claims
        }
    except Exception as e:
        logger.exception(f"Failed to add badge to custom claims: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add badge to custom claims: {str(e)}")
