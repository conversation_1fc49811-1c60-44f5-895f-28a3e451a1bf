from fastapi import HTTPException, Request
from firebase.config import get_db
import logging
from cryptography.fernet import <PERSON>rnet
import os
import time

# Load from env or GCP Secret Manager
FERNET_KEY = os.environ.get("FERNET_SECRET")
fernet = Fernet(FERNET_KEY) if FERNET_KEY else None

db = get_db()
logger = logging.getLogger(__name__)

def decrypt_api_key(encrypted_api_key: str) -> str:
    """Decrypt an API key using Fernet encryption"""
    if not fernet:
        logger.error("FERNET_SECRET environment variable not set")
        raise HTTPException(status_code=500, detail="Server configuration error")
    return fernet.decrypt(encrypted_api_key.encode()).decode()

def verify_api_key(api_key: str) -> dict:
    """
    Verify an API key and return agent information.
    
    Args:
        api_key: The API key to verify
        
    Returns:
        dict: A dictionary containing agent_id, type, and is_test flag
        
    Raises:
        HTTPException: If the API key is invalid or revoked
    """
    try:
        agents = db.collection("api_keys").stream()
        found = None
        found_agent_id = None
        found_type = None
        found_key_id = None
        
        for agent_doc in agents:
            agent_id = agent_doc.id
            for subcollection, key_type in [("test_keys", "test"), ("prod_keys", "production")]:
                keys_collection = db.collection("api_keys").document(agent_id).collection(subcollection)
                key_query = keys_collection.where("is_active", "==", True).limit(10).stream()
                for key_doc in key_query:
                    key_data = key_doc.to_dict()
                    try:
                        decrypted = decrypt_api_key(key_data["key"])
                    except Exception:
                        continue  # skip if decryption fails
                    if decrypted == api_key:
                        found = key_doc
                        found_agent_id = agent_id
                        found_type = key_type
                        found_key_id = key_doc.id
                        break
                if found:
                    break
            if found:
                break
                
        if not found:
            raise HTTPException(status_code=401, detail="Invalid or revoked API key")
            
        key_data = found.to_dict()
        now = int(time.time())
        
        # Update last_used timestamp
        db.collection("api_keys").document(found_agent_id).collection(
            "test_keys" if key_data.get("type") == "test" else "prod_keys"
        ).document(found_key_id).update({"last_used": now})
        
        return {
            "agent_id": key_data.get("agent_id"),
            "type": key_data.get("type"),
            "is_test": key_data.get("type") == "test"
        }
    except Exception as e:
        logger.exception(f"Failed to verify API key: {str(e)}")
        # Ensure a valid dictionary is returned in case of an error
        raise HTTPException(status_code=500, detail="Failed to verify API key")

def verify_api_key_from_request(request: Request) -> dict:
    """
    Extract and verify an API key from the Authorization header of a request.
    
    Args:
        request: The FastAPI request object
        
    Returns:
        dict: A dictionary containing agent_id, type, and is_test flag
        
    Raises:
        HTTPException: If the API key is missing, invalid, or revoked
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing or invalid API key")
        
    api_key = auth_header.replace("Bearer ", "").strip()
    try:
        return verify_api_key(api_key)
    except HTTPException as e:
        logger.error(f"API key verification failed: {e.detail}")
        raise
    except Exception as e:
        logger.exception(f"Unexpected error during API key verification: {str(e)}")
        raise HTTPException(status_code=500, detail="Unexpected server error")
