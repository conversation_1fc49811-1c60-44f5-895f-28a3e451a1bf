from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Security
from fastapi.security import HTTPAuthorizationCredentials, HTT<PERSON><PERSON>earer
from firebase_admin import auth, credentials, initialize_app
import firebase_admin

# Initialize Firebase Admin SDK
# TODO: Replace with your Firebase credentials file path
cred = credentials.Certificate("firebase/serviceAccountKey.json")
if not firebase_admin._apps:
    initialize_app(cred)

security = HTTPBearer()

def create_firebase_user(email: str, password: str):
    try:
        user = auth.create_user(
            email=email,
            password=password,
            email_verified=False
        )
        return user
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

async def get_current_user(credentials: HTTPAuthorizationCredentials = Security(security)):
    try:
        token = credentials.credentials
        decoded_token = auth.verify_id_token(token)
        return decoded_token
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication credentials"
        )

def verify_role(token: dict, required_role: str):
    if token.get("role") != required_role:
        raise HTTPException(
            status_code=403,
            detail=f"Only {required_role}s can access this endpoint"
        )
