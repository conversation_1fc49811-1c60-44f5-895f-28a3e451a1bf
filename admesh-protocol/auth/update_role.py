from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from firebase_admin import auth as firebase_auth
from auth.deps import verify_firebase_token

router = APIRouter()

ALLOWED_ROLES = {"agent", "brand", "user"}

class UpdateRolePayload(BaseModel):
    role: str  # "agent", "brand", "user"

@router.post("/auth/update-role")
async def update_role(
    payload: UpdateRolePayload,
    decoded_token: dict = Depends(verify_firebase_token)
):
    try:
        uid = decoded_token["uid"]
        role = payload.role.lower().strip()

        if role not in ALLOWED_ROLES:
            raise HTTPException(status_code=400, detail="Invalid role. Must be one of: agent, brand, user")

        # Update the user's custom claims
        firebase_auth.set_custom_user_claims(uid, {"role": role})

        return {
            "status": "success",
            "message": "Role updated successfully",
            "uid": uid,
            "role": role
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update role: {str(e)}")
