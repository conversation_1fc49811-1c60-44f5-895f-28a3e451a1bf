from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, EmailStr
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import verify_firebase_token
from google.cloud import firestore
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

ALLOWED_ROLES = {"agent", "brand", "user", "admin"}

class UpdateRolePayload(BaseModel):
    role: str  # "agent", "brand", "user"

class AdminUpdateRolePayload(BaseModel):
    user_id: str
    role: str  # "agent", "brand", "user", "admin"
    onboarding_status: str = None  # Only for brands
    is_admin: bool = False  # Whether to set admin status

@router.post("/auth/update-role")
async def update_role(
    payload: UpdateRolePayload,
    decoded_token = Depends(verify_firebase_token)
):
    """Update the role of the currently authenticated user"""
    try:
        uid = decoded_token["uid"]
        role = payload.role.lower().strip()

        if role not in ALLOWED_ROLES:
            raise HTTPException(status_code=400, detail=f"Invalid role. Must be one of: {', '.join(ALLOWED_ROLES)}")

        # Set custom claims based on role
        custom_claims = {"role": role}

        # Set onboarding_status for brands and agents
        if role == "brand":
            custom_claims["onboarding_status"] = "brand"
        elif role == "agent":
            custom_claims["onboarding_status"] = "pending"

            # Create or update brand document with onboarding status
            brand_ref = db.collection("brands").document(uid)
            if not brand_ref.get().exists:
                # Create a new brand document
                brand_data = {
                    "uid": uid,
                    "role": role,
                    "onboarding_status": "brand",
                    "onboarding_steps": {
                        "brand": False,
                        "product": False,
                        "offer": False,
                        "tracking": False
                    },
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                brand_ref.set(brand_data)
            else:
                # Update existing brand document
                brand_ref.update({
                    "role": role,
                    "onboarding_status": "brand",
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

        # For agents, create or update agent document
        elif role == "agent":
            agent_ref = db.collection("agents").document(uid)
            if not agent_ref.get().exists:
                # Create a new agent document
                agent_data = {
                    "uid": uid,
                    "role": role,
                    "trust_score": 50.0,  # Default trust score
                    "total_earnings": 0,
                    "conversions": 0,
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP,
                    "onboarding_status": "pending"
                }
                agent_ref.set(agent_data)
            else:
                # Update existing agent document
                agent_ref.update({
                    "role": role,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(uid, custom_claims)

        # Make sure user document exists
        user_ref = db.collection("users").document(uid)
        if not user_ref.get().exists:
            # Get user info from Firebase Auth
            user_record = firebase_auth.get_user(uid)

            # Create user document
            user_data = {
                "uid": uid,
                "email": user_record.email,
                "name": user_record.display_name or "",
                "role": role,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            }
            user_ref.set(user_data)
        else:
            # Update existing user document
            user_ref.update({
                "role": role,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

        return {
            "status": "success",
            "message": "Role updated successfully",
            "uid": uid,
            "role": role
        }
    except Exception as e:
        logger.exception(f"Failed to update role: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update role: {str(e)}")

@router.post("/admin/update-user-role")
async def admin_update_role(
    payload: AdminUpdateRolePayload,
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to update the role of any user (requires admin role)"""
    try:
        # Check if the current user is an admin
        admin_uid = decoded_token["uid"]
        admin_role = decoded_token.get("role")

        # For now, we'll check if the user has a special admin claim
        # In a real app, you might have a more sophisticated admin check
        is_admin = decoded_token.get("admin", False)

        # if not is_admin:
        #     raise HTTPException(status_code=403, detail="Admin access required")

        target_uid = payload.user_id
        role = payload.role.lower().strip()

        if role not in ALLOWED_ROLES:
            raise HTTPException(status_code=400, detail=f"Invalid role. Must be one of: {', '.join(ALLOWED_ROLES)}")

        # Set custom claims based on role
        custom_claims = {"role": role}

        # Set admin status if specified
        if payload.is_admin:
            custom_claims["admin"] = True

        # For brands, handle onboarding_status
        if role == "brand":
            if payload.onboarding_status is not None:
                custom_claims["onboarding_status"] = payload.onboarding_status
            else:
                custom_claims["onboarding_status"] = "brand"

            # Create or update brand document
            brand_ref = db.collection("brands").document(target_uid)
            if not brand_ref.get().exists:
                # Create a new brand document
                brand_data = {
                    "uid": target_uid,
                    "role": role,
                    "onboarding_status": custom_claims["onboarding_status"],
                    "onboarding_steps": {
                        "brand": False,
                        "product": False,
                        "offer": False,
                        "tracking": False
                    },
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                brand_ref.set(brand_data)
            else:
                # Update existing brand document
                brand_ref.update({
                    "role": role,
                    "onboarding_status": custom_claims["onboarding_status"],
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

        # For agents, create or update agent document
        elif role == "agent":
            agent_ref = db.collection("agents").document(target_uid)
            if not agent_ref.get().exists:
                # Create a new agent document
                agent_data = {
                    "uid": target_uid,
                    "role": role,
                    "trust_score": 50.0,  # Default trust score
                    "total_earnings": 0,
                    "conversions": 0,
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP,
                    "onboarding_status": "pending"
                }
                agent_ref.set(agent_data)
            else:
                # Update existing agent document
                agent_ref.update({
                    "role": role,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(target_uid, custom_claims)

        # Make sure user document exists
        user_ref = db.collection("users").document(target_uid)
        if not user_ref.get().exists:
            # Get user info from Firebase Auth
            user_record = firebase_auth.get_user(target_uid)

            # Create user document
            user_data = {
                "uid": target_uid,
                "email": user_record.email,
                "name": user_record.display_name or "",
                "role": role,
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            }
            user_ref.set(user_data)
        else:
            # Update existing user document
            user_ref.update({
                "role": role,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

        return {
            "status": "success",
            "message": "User role updated successfully by admin",
            "admin_uid": admin_uid,
            "target_uid": target_uid,
            "role": role
        }
    except Exception as e:
        logger.exception(f"Failed to update user role: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update user role: {str(e)}")

@router.post("/admin/set-admin-status")
async def set_admin_status(
    user_id: str = Query(..., description="User ID to set as admin"),
    is_admin: bool = Query(True, description="Whether to set or remove admin status"),
    decoded_token = Depends(verify_firebase_token)
):
    """Set or remove admin status for a user (requires existing admin)"""
    try:
        # Check if the current user is an admin
        admin_uid = decoded_token["uid"]
        current_is_admin = decoded_token.get("admin", False)

        if not current_is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")

        # Get the user's current custom claims
        user_record = firebase_auth.get_user(user_id)
        custom_claims = dict(user_record.custom_claims or {})

        # Update admin status
        custom_claims["admin"] = is_admin

        # Update Firebase custom claims
        firebase_auth.set_custom_user_claims(user_id, custom_claims)

        return {
            "status": "success",
            "message": f"Admin status {'set' if is_admin else 'removed'} successfully",
            "admin_uid": admin_uid,
            "target_uid": user_id,
            "is_admin": is_admin
        }
    except Exception as e:
        logger.exception(f"Failed to set admin status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to set admin status: {str(e)}")

@router.get("/admin/get-user-by-email")
async def get_user_by_email(
    email: EmailStr = Query(..., description="Email of the user to find"),
    decoded_token = Depends(verify_firebase_token)
):
    """Find a user by email (requires admin)"""
    try:
        # Check if the current user is an admin
        admin_uid = decoded_token["uid"]
        is_admin = decoded_token.get("admin", False)

        if not is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")

        # Find user by email
        try:
            user_record = firebase_auth.get_user_by_email(email)

            # Get custom claims
            custom_claims = dict(user_record.custom_claims or {})

            return {
                "status": "success",
                "uid": user_record.uid,
                "email": user_record.email,
                "display_name": user_record.display_name,
                "photo_url": user_record.photo_url,
                "role": custom_claims.get("role", "user"),
                "is_admin": custom_claims.get("admin", False),
                "onboarding_status": custom_claims.get("onboarding_status", None)
            }
        except firebase_auth.UserNotFoundError:
            raise HTTPException(status_code=404, detail="User not found")

    except Exception as e:
        logger.exception(f"Failed to get user by email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user by email: {str(e)}")

class WaitlistEntry(BaseModel):
    id: str
    email: str
    role: str
    ip: Optional[str] = None
    timestamp: str
    source: str = "web"

class WaitlistResponse(BaseModel):
    entries: List[WaitlistEntry]
    total: int
    limit: int
    offset: int

@router.get("/admin/waitlist", response_model=WaitlistResponse)
async def get_waitlist_entries(
    limit: int = Query(50, description="Maximum number of entries to return"),
    offset: int = Query(0, description="Number of entries to skip"),
    sort_by: str = Query("timestamp", description="Field to sort by"),
    sort_direction: str = Query("desc", description="Sort direction (asc or desc)"),
    role_filter: Optional[str] = Query(None, description="Filter by role (user, agent, brand)"),
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to get all waitlist entries"""
    # Check if the user is an admin
    is_admin = decoded_token.get("admin", False)
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    logger.info(f"Admin fetching waitlist entries (limit: {limit}, offset: {offset}, sort_by: {sort_by}, sort_direction: {sort_direction}, role_filter: {role_filter})")

    try:
        # Start with base query
        query = db.collection("waitlist")

        # Apply sorting if provided - do this first for better performance
        if sort_by in ["timestamp", "email", "role"]:
            direction = firestore.Query.DESCENDING if sort_direction == "desc" else firestore.Query.ASCENDING
            query = query.order_by(sort_by, direction=direction)

        # Execute query to get all entries
        all_entries = []
        for doc in query.stream():
            entry_data = doc.to_dict()
            # Add document ID
            entry_data["id"] = doc.id
            all_entries.append(entry_data)

        # Apply role filter in memory if provided
        filtered_entries = all_entries
        if role_filter and role_filter != "all":
            filtered_entries = [entry for entry in all_entries if entry.get("role", "").lower() == role_filter.lower()]

        # Get total count after filtering
        total_entries = len(filtered_entries)

        # Apply pagination in memory
        start_idx = offset
        end_idx = min(offset + limit, total_entries)
        entries = filtered_entries[start_idx:end_idx] if start_idx < total_entries else []

        return {
            "entries": entries,
            "total": total_entries,
            "limit": limit,
            "offset": offset
        }
    except Exception as e:
        logger.exception(f"Failed to get waitlist entries: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get waitlist entries: {str(e)}")

@router.delete("/admin/delete-user/{user_id}")
async def delete_user(
    user_id: str,
    decoded_token = Depends(verify_firebase_token)
):
    """Delete a user and their associated data (requires admin privileges)"""
    try:
        # Check if the current user is an admin
        admin_uid = decoded_token["uid"]
        is_admin = decoded_token.get("admin", False)

        if not is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")

        # Prevent admins from deleting themselves
        if user_id == admin_uid:
            raise HTTPException(status_code=400, detail="Admins cannot delete their own accounts")

        # Get user info before deletion
        try:
            user_record = firebase_auth.get_user(user_id)
            custom_claims = dict(user_record.custom_claims or {})
            user_role = custom_claims.get("role", "user")
        except firebase_auth.UserNotFoundError:
            raise HTTPException(status_code=404, detail="User not found")

        # Delete user data from Firestore based on role
        # 1. Delete from role-specific collection if applicable
        if user_role == "agent":
            agent_ref = db.collection("agents").document(user_id)
            if agent_ref.get().exists:
                agent_ref.delete()

        elif user_role == "brand":
            brand_ref = db.collection("brands").document(user_id)
            if brand_ref.get().exists:
                brand_ref.delete()

        # 2. Delete subcollections under the user document
        user_ref = db.collection("users").document(user_id)

        # Delete badges subcollection
        badges_ref = user_ref.collection("badges")
        delete_collection(badges_ref, 100)

        # Delete XP logs subcollection
        xp_logs_ref = user_ref.collection("xp_logs")
        delete_collection(xp_logs_ref, 100)

        # 3. Delete the main user document
        if user_ref.get().exists:
            user_ref.delete()

        # 4. Delete the user from Firebase Authentication
        firebase_auth.delete_user(user_id)

        return {
            "status": "success",
            "message": "User and associated data deleted successfully",
            "admin_uid": admin_uid,
            "deleted_uid": user_id
        }
    except Exception as e:
        logger.exception(f"Failed to delete user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete user: {str(e)}")

@router.delete("/admin/waitlist/{entry_id}")
async def delete_waitlist_entry(
    entry_id: str,
    decoded_token = Depends(verify_firebase_token)
):
    """Delete a waitlist entry (requires admin privileges)"""
    try:
        # Check if the current user is an admin
        admin_uid = decoded_token["uid"]
        is_admin = decoded_token.get("admin", False)

        if not is_admin:
            raise HTTPException(status_code=403, detail="Admin access required")

        # Get the waitlist entry
        entry_ref = db.collection("waitlist").document(entry_id)
        entry_doc = entry_ref.get()

        if not entry_doc.exists:
            raise HTTPException(status_code=404, detail="Waitlist entry not found")

        # Get entry data for logging
        entry_data = entry_doc.to_dict()
        email = entry_data.get("email", "unknown")
        role = entry_data.get("role", "unknown")

        # Delete the waitlist entry
        entry_ref.delete()

        logger.info(f"Admin {admin_uid} deleted waitlist entry {entry_id} (email: {email}, role: {role})")

        return {
            "status": "success",
            "message": "Waitlist entry deleted successfully",
            "admin_uid": admin_uid,
            "deleted_entry_id": entry_id,
            "email": email,
            "role": role
        }
    except Exception as e:
        logger.exception(f"Failed to delete waitlist entry: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete waitlist entry: {str(e)}")

def delete_collection(collection_ref, batch_size: int):
    """Helper function to delete a collection"""
    docs = collection_ref.limit(batch_size).stream()
    deleted = 0

    for doc in docs:
        doc.reference.delete()
        deleted += 1

    if deleted >= batch_size:
        return delete_collection(collection_ref, batch_size)
