#!/usr/bin/env python3
"""
Test script for the new payout distribution logic
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.routes.conversion import (
    get_agent_subscription_revenue_share,
    calculate_dynamic_reward_split,
    calculate_reward_distribution
)


async def test_agent_subscription_revenue_share():
    """Test agent subscription revenue share calculation"""
    print("Testing agent subscription revenue share calculation...")

    # Test with no agent_id
    revenue_share = await get_agent_subscription_revenue_share(None)
    print(f"No agent_id: {revenue_share}% (expected: 60%)")
    assert revenue_share == 60

    # Test with non-existent agent
    revenue_share = await get_agent_subscription_revenue_share("non_existent_agent")
    print(f"Non-existent agent: {revenue_share}% (expected: 60%)")
    assert revenue_share == 60

    print("✅ Agent subscription revenue share tests passed!")


async def test_dynamic_reward_split():
    """Test dynamic reward split calculation"""
    print("\nTesting dynamic reward split calculation...")

    # Test case 1: Agent with no subscription (60%), with user (10%)
    agent_id = "test_agent_no_subscription"
    user_id = "test_user"
    reward_split = await calculate_dynamic_reward_split(agent_id, user_id)
    print(f"Agent (60%) + User (10%): {reward_split}")
    expected = {"agent": 60, "user": 10, "admesh": 30}
    assert reward_split == expected, f"Expected {expected}, got {reward_split}"

    # Test case 2: Agent with no subscription (60%), no user (user's 10% goes to admesh)
    agent_id = "test_agent_no_subscription"
    user_id = None
    reward_split = await calculate_dynamic_reward_split(agent_id, user_id)
    print(f"Agent (60%) + No User (10% to admesh): {reward_split}")
    expected = {"agent": 60, "user": 0, "admesh": 40}
    assert reward_split == expected, f"Expected {expected}, got {reward_split}"

    print("✅ Dynamic reward split tests passed!")


def test_reward_distribution():
    """Test reward distribution calculation"""
    print("\nTesting reward distribution calculation...")

    # Test case 1: $10.00 conversion with 60/10/30 split (free agent + user)
    conversion_value = 1000  # $10.00 in cents
    reward_split = {"agent": 60, "user": 10, "admesh": 30}
    distribution = calculate_reward_distribution(conversion_value, reward_split)
    print(f"$10.00 with 60/10/30 split: {distribution}")
    expected_agent = 600  # 60% of $10.00
    expected_user = 100   # 10% of $10.00
    expected_admesh = 300 # 30% of $10.00

    # Verify total adds up to conversion value
    total = sum(distribution.values())
    assert total == conversion_value, f"Total {total} doesn't match conversion value {conversion_value}"

    # Test case 2: $10.00 conversion with 60/0/40 split (no user)
    conversion_value = 1000  # $10.00 in cents
    reward_split = {"agent": 60, "user": 0, "admesh": 40}
    distribution = calculate_reward_distribution(conversion_value, reward_split)
    print(f"$10.00 with 60/0/40 split: {distribution}")
    expected_agent = 600  # 60% of $10.00
    expected_user = 0     # 0% (no user)
    expected_admesh = 400 # 40% of $10.00 (30% + 10% from user)

    # Verify total adds up to conversion value
    total = sum(distribution.values())
    assert total == conversion_value, f"Total {total} doesn't match conversion value {conversion_value}"

    # Test case 3: $50.00 conversion with 70/10/20 split (pro agent + user)
    conversion_value = 5000  # $50.00 in cents
    reward_split = {"agent": 70, "user": 10, "admesh": 20}
    distribution = calculate_reward_distribution(conversion_value, reward_split)
    print(f"$50.00 with 70/10/20 split: {distribution}")
    expected_agent = 3500  # 70% of $50.00
    expected_user = 500    # 10% of $50.00
    expected_admesh = 1000 # 20% of $50.00

    # Verify total adds up to conversion value
    total = sum(distribution.values())
    assert total == conversion_value, f"Total {total} doesn't match conversion value {conversion_value}"

    print("✅ Reward distribution tests passed!")


def test_payout_object_structure():
    """Test that we handle the payout object structure correctly"""
    print("\nTesting payout object structure...")

    # Test payout object structure
    payout_obj = {
        "amount": 1000,
        "currency": "USD",
        "model": "CPA"
    }

    amount = payout_obj.get("amount", 1000)
    currency = payout_obj.get("currency", "USD")
    model = payout_obj.get("model", "CPA")

    print(f"Payout object: amount={amount}, currency={currency}, model={model}")

    assert amount == 1000
    assert currency == "USD"
    assert model == "CPA"

    print("✅ Payout object structure tests passed!")


async def main():
    """Run all tests"""
    print("🧪 Testing new payout distribution logic...\n")

    try:
        await test_agent_subscription_revenue_share()
        await test_dynamic_reward_split()
        test_reward_distribution()
        test_payout_object_structure()

        print("\n🎉 All tests passed! The new payout distribution logic is working correctly.")
        print("\n📋 Summary of changes:")
        print("✅ Get entire payout object from offers")
        print("✅ Calculate agent revenue share based on subscription (60% default, 70% pro, 80% enterprise)")
        print("✅ Handle user share distribution (10% if user exists, 0 if not)")
        print("✅ Add user share (10%) to AdMesh when user_id is null")
        print("✅ Store payout object in conversion and earnings data")
        print("✅ Total always equals 100%: Agent% + User(10% or 0%) + AdMesh(remainder)")
        print("✅ Update offer total_spent (test/production/total)")
        print("✅ Deduct from offer budget only for production conversions")
        print("✅ Create wallet transactions for brand, agent, and user")
        print("✅ Update conversion and click status (completed/pending)")
        print("✅ Comprehensive logging for all operations")
        print("✅ Atomic transaction handling with error recovery")

    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
