import os
import json
import logging

# Try to load .env file if it exists (for local development)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv might not be installed in production
    pass

import firebase_admin
from firebase_admin import credentials, firestore

# Import the new configuration manager
try:
    from config.config_manager import get_config, get_firebase_config
except ImportError:
    # Fallback for backward compatibility
    get_config = None
    get_firebase_config = None

logger = logging.getLogger(__name__)

firebase_app = None
db = None

def initialize_firebase():
    global firebase_app, db

    # Force clean initialization in development
    try:
        from config.config_manager import get_config
        config = get_config()
        if config.environment == 'development':
            # Delete existing apps to force re-initialization with correct credentials
            if firebase_admin._apps:
                logger.info("Development mode: Clearing existing Firebase apps for clean initialization")
                for app_name in list(firebase_admin._apps.keys()):
                    firebase_admin.delete_app(firebase_admin._apps[app_name])
                firebase_admin._apps.clear()
    except ImportError:
        pass

    if firebase_admin._apps:
        logger.info("Firebase already initialized, returning existing client")
        return firestore.client()

    try:
        # Use new configuration manager if available
        if get_config:
            config = get_config()
            firebase_config = get_firebase_config()
            credentials_path = config.get_firebase_credentials_path()
            environment = config.environment

            logger.info(f"📦 Initializing Firebase for environment: {environment}")
            logger.info(f"📦 Using project ID: {firebase_config.get('project_id')}")
        else:
            # Fallback to legacy configuration
            credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
            is_cloud_run = os.getenv("K_SERVICE") is not None
            environment = os.getenv("ENV", "production" if is_cloud_run else "development")

            logger.info(f"📦 Using legacy configuration for environment: {environment}")

        # Determine credentials source
        if not credentials_path:
            # Use default paths based on environment
            if environment == "development":
                credentials_path = os.path.join(os.path.dirname(__file__), "dev-serviceAccountKey.json")
            else:
                credentials_path = os.path.join(os.path.dirname(__file__), "serviceAccountKey.json")
            logger.info(f"Using default credentials file: {credentials_path}")

        # Load credentials based on environment and format
        if environment in ["production", "staging"]:
            # In cloud environments, credentials might be JSON string
            try:
                if os.path.exists(credentials_path):
                    # File exists, use as file path
                    cred = credentials.Certificate(credentials_path)
                    logger.info(f"Loaded credentials from file: {credentials_path}")
                else:
                    # Treat as JSON string (from environment variable)
                    cred_dict = json.loads(credentials_path)
                    cred = credentials.Certificate(cred_dict)
                    logger.info("Loaded credentials from JSON string")
            except json.JSONDecodeError:
                # Not JSON, treat as file path
                if os.path.exists(credentials_path):
                    cred = credentials.Certificate(credentials_path)
                    logger.info(f"Loaded credentials from file: {credentials_path}")
                else:
                    raise ValueError(f"Credentials file does not exist: {credentials_path}")
        else:
            # In development, always use file path
            if os.path.exists(credentials_path):
                cred = credentials.Certificate(credentials_path)
                logger.info(f"Loaded development credentials from: {credentials_path}")
            else:
                raise ValueError(f"Development credentials file does not exist: {credentials_path}")

        # Initialize Firebase app
        firebase_app = firebase_admin.initialize_app(cred)
        db = firestore.client()

        logger.info("✅ Firebase initialized successfully")
        return db

    except Exception as e:
        logger.error(f"🔥 Error initializing Firebase: {e}")
        raise e

def get_db():
    global db
    if db is None:
        return initialize_firebase()
    return db
