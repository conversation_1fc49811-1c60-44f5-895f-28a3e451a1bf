from pydantic import BaseModel
from typing import Optional, Literal
from datetime import datetime


class AgentLimits(BaseModel):
    """Model for agent subscription limits"""
    daily_query_limit: int  # Daily query limit (-1 for unlimited)
    monthly_query_limit: int  # Monthly query limit (-1 for unlimited)
    trust_score_threshold: float  # Minimum trust score required
    throttle_enabled: bool  # Whether throttling is enabled for low trust scores
    priority_routing: bool  # Whether agent gets priority LLM routing
    fraud_detection_enabled: bool  # Whether fraud detection sampling is enabled


class AgentUsage(BaseModel):
    """Model for tracking agent usage"""
    daily_queries_used: int = 0  # Queries used today
    monthly_queries_used: int = 0  # Queries used this month
    current_trust_score: float = 50.0  # Current trust score (0-100)
    last_daily_reset: Optional[datetime] = None  # Last daily reset timestamp
    last_monthly_reset: Optional[datetime] = None  # Last monthly reset timestamp
    consecutive_low_conversion_days: int = 0  # Days with <10% conversion rate
    fraud_flags: int = 0  # Number of fraud flags
    throttle_active: bool = False  # Whether agent is currently throttled


class AgentSubscriptionPlan(BaseModel):
    """Model for agent subscription plan details"""
    id: Literal["free", "pro", "enterprise"]
    name: str
    price_monthly_cents: int  # Price in cents
    revenue_share_percentage: int  # Revenue share percentage for the agent
    attribution_window: str
    offer_visibility_boost: str
    monetized_query_usage: str
    conversion_reports: str
    support_level: str
    custom_integrations: bool
    multi_agent_teams: bool
    features: list[str]
    limits: AgentLimits  # Query limits and trust conditions

    @classmethod
    def get_free_plan(cls):
        return cls(
            id="free",
            name="Free",
            price_monthly_cents=0,
            revenue_share_percentage=60,
            attribution_window="Session-only",
            offer_visibility_boost="Standard",
            monetized_query_usage="10,000/day, 100,000/month (tracked)",
            conversion_reports="Basic totals only",
            support_level="Community only",
            custom_integrations=False,
            multi_agent_teams=False,
            limits=AgentLimits(
                daily_query_limit=10000,
                monthly_query_limit=100000,
                trust_score_threshold=40.0,
                throttle_enabled=True,
                priority_routing=False,
                fraud_detection_enabled=True
            ),
            features=[
                "60% Revenue Share",
                "10,000 queries/day, 100,000/month",
                "Session-only Attribution Window",
                "Standard Offer Visibility Boost",
                "Trust Score monitoring (throttle at <40)",
                "Basic Conversion Reports (totals only)",
                "Community Support",
                "Fraud detection sampling"
            ]
        )

    @classmethod
    def get_pro_plan(cls):
        return cls(
            id="pro",
            name="Pro",
            price_monthly_cents=4900,  # $49/month
            revenue_share_percentage=70,
            attribution_window="24 hours",
            offer_visibility_boost="Moderate (CTR-weighted)",
            monetized_query_usage="50,000/day, 1,000,000/month (audited)",
            conversion_reports="Filter by product",
            support_level="Email (48h SLA)",
            custom_integrations=False,
            multi_agent_teams=False,
            limits=AgentLimits(
                daily_query_limit=50000,
                monthly_query_limit=1000000,
                trust_score_threshold=0.0,  # No trust score restrictions
                throttle_enabled=False,
                priority_routing=True,
                fraud_detection_enabled=False
            ),
            features=[
                "70% Revenue Share",
                "50,000 queries/day, 1,000,000/month",
                "24-hour Attribution Window",
                "Moderate Offer Visibility Boost (CTR-weighted)",
                "Priority LLM routing and RAG",
                "Conversion Reports with product filtering",
                "Email Support (48h SLA)"
            ]
        )

    @classmethod
    def get_enterprise_plan(cls):
        return cls(
            id="enterprise",
            name="Enterprise",
            price_monthly_cents=0,  # Custom pricing
            revenue_share_percentage=80,
            attribution_window="30-day cookie-based tracking",
            offer_visibility_boost="Priority slotting (CPA + ROI-weighted)",
            monetized_query_usage="Unlimited (whitelisted, soft cap)",
            conversion_reports="Full-funnel: by product, campaign, source",
            support_level="Dedicated account manager",
            custom_integrations=True,
            multi_agent_teams=True,
            limits=AgentLimits(
                daily_query_limit=-1,  # Unlimited (soft cap)
                monthly_query_limit=-1,  # Unlimited (soft cap)
                trust_score_threshold=0.0,  # No trust score restrictions
                throttle_enabled=False,
                priority_routing=True,
                fraud_detection_enabled=False
            ),
            features=[
                "80% Revenue Share",
                "Unlimited queries (soft cap monitoring)",
                "30-day cookie-based tracking",
                "Priority slotting (CPA + ROI-weighted)",
                "SLA-based monitoring via trust + conversions",
                "Full-funnel conversion reports",
                "Dedicated account manager",
                "Custom integrations",
                "Multi-agent teams"
            ]
        )

    @classmethod
    def get_all_plans(cls):
        return [
            cls.get_free_plan(),
            cls.get_pro_plan(),
            cls.get_enterprise_plan()
        ]

    @classmethod
    def get_plan_by_id(cls, plan_id: str):
        plans = {
            "free": cls.get_free_plan(),
            "pro": cls.get_pro_plan(),
            "enterprise": cls.get_enterprise_plan()
        }
        return plans.get(plan_id, cls.get_free_plan())


class AgentSubscription(BaseModel):
    """Model for agent subscription"""
    plan_id: str = "free"
    status: str = "active"  # active, canceled, past_due, etc.
    stripe_subscription_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    usage: AgentUsage = AgentUsage()  # Current usage tracking


class AgentSubscriptionRequest(BaseModel):
    """Request model for creating or updating an agent subscription"""
    plan_id: str  # "free", "pro", "enterprise"
    payment_method_id: Optional[str] = None  # Required for paid plans


class AgentThrottleCondition(BaseModel):
    """Model for agent throttling conditions"""
    condition_type: Literal["trust_score", "conversion_rate", "fraud_detection"]
    threshold_value: float
    action: Literal["soft_throttle", "hard_throttle", "degrade_model", "flag_review"]
    description: str


class AgentQueryLimitCheck(BaseModel):
    """Model for query limit check response"""
    allowed: bool
    reason: Optional[str] = None
    daily_remaining: Optional[int] = None
    monthly_remaining: Optional[int] = None
    throttle_active: bool = False
    trust_score_warning: bool = False


# Default throttling conditions for Free plan
FREE_PLAN_THROTTLE_CONDITIONS = [
    AgentThrottleCondition(
        condition_type="trust_score",
        threshold_value=40.0,
        action="soft_throttle",
        description="Trust Score < 40 → Soft throttle to 2K queries/day"
    ),
    AgentThrottleCondition(
        condition_type="conversion_rate",
        threshold_value=0.1,  # 10%
        action="degrade_model",
        description=">90% queries with no click/conversion → Degrade model access"
    ),
    AgentThrottleCondition(
        condition_type="fraud_detection",
        threshold_value=3.0,  # 3 fraud flags
        action="flag_review",
        description="Periodic sampling for fraud detection (same IP/device patterns)"
    )
]
