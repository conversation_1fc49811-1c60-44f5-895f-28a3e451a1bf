from google.cloud import firestore
from datetime import datetime
from firebase.config import get_db
class OfferModel:
    collection_name = "offers"

    @staticmethod
    async def create(offer_data):
        db = get_db()
        offer_ref = db.collection(OfferModel.collection_name).document()
        
        # Initialize budget tracking fields if not present
        if not hasattr(offer_data, 'offer_total_budget_allocated'):
            offer_data.offer_total_budget_allocated = 0.0
        if not hasattr(offer_data, 'offer_total_budget_spent'):
            offer_data.offer_total_budget_spent = 0.0
        if not hasattr(offer_data, 'offer_total_promo_spent'):
            offer_data.offer_total_promo_spent = 0.0
        if not hasattr(offer_data, 'offer_total_promo_available'):
            offer_data.offer_total_promo_available = 0.0
            
        offer_data_dict = {
            **offer_data.dict(),
            "created_at": firestore.SERVER_TIMESTAMP
        }
        offer_ref.set(offer_data_dict)
        return {**offer_data_dict, "id": offer_ref.id}
