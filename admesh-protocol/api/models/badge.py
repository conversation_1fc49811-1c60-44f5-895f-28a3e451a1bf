from datetime import datetime, timezone
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

class BadgeType(str, Enum):
    AGENT_PIONEER = "agent_pioneer"
    FIRST_DISCOVERY = "first_discovery"
    FIRST_REWARD = "first_reward"
    POWER_EXPLORER = "power_explorer"
    TOP_RECOMMENDER = "top_recommender"
    CONVERSION_MASTER = "conversion_master"
    REFERRAL_CHAMPION = "referral_champion"
    FEEDBACK_PROVIDER = "feedback_provider"
    FEEDBACK_CHAMPION = "feedback_champion"
    VERIFIED_EMAIL = "verified_email"
    PROFILE_COMPLETE = "profile_complete"
    MILESTONE_100 = "milestone_100"
    MILESTONE_500 = "milestone_500"
    MILESTONE_1000 = "milestone_1000"
    MILESTONE_5000 = "milestone_5000"
    DAILY_STREAK = "daily_streak"
    WEEKLY_ACTIVE = "weekly_active"
    NIGHT_OWL = "night_owl"
    EARLY_BIRD = "early_bird"
    WEEKEND_WARRIOR = "weekend_warrior"
    SOCIAL_BUTTERFLY = "social_butterfly"
    SUPER_AGENT = "super_agent"
    FIRST_FOLLOWUP = "first_followup"
    FOLLOWUP_50 = "followup_50"
    FOLLOWUP_100 = "followup_100"

class BadgeMetadata(BaseModel):
    name: str
    description: str
    icon: str
    color: str
    xp_bonus: int = 0
    earnings_boost_percent: float = 0
    required_value: int = 0

class Badge(BaseModel):
    badge_id: str
    user_id: str
    badge_type: BadgeType
    awarded_at: str = Field(default_factory=lambda: datetime.now(timezone.utc).isoformat())
    metadata: BadgeMetadata
    progress: Optional[int] = None
    progress_target: Optional[int] = None
    is_displayed: bool = True

# Badge definitions with metadata
BADGE_DEFINITIONS: Dict[BadgeType, BadgeMetadata] = {
    BadgeType.AGENT_PIONEER: BadgeMetadata(
        name="Agent Pioneer",
        description="Reached 1000 XP in the Agent Pioneer program",
        icon="sparkles",
        color="#FFD700",
        xp_bonus=50,
        required_value=1000,
    ),
    BadgeType.FIRST_DISCOVERY: BadgeMetadata(
        name="First Discovery",
        description="Made your first product discovery query",
        icon="search",
        color="#4F46E5",
        xp_bonus=10,
        required_value=1,
    ),
    BadgeType.FIRST_REWARD: BadgeMetadata(
        name="First Reward",
        description="Received your first reward",
        icon="gift",
        color="#84CC16",
        xp_bonus=20,
        required_value=1,
    ),
    BadgeType.POWER_EXPLORER: BadgeMetadata(
        name="Power Explorer",
        description="Made 50+ product discovery queries",
        icon="compass",
        color="#0EA5E9",
        xp_bonus=100,
        required_value=50,
    ),
    BadgeType.TOP_RECOMMENDER: BadgeMetadata(
        name="Top Recommender",
        description="Clicked on 10+ product recommendations",
        icon="thumbs-up",
        color="#10B981",
        xp_bonus=50,
        earnings_boost_percent=5,
        required_value=10,
    ),
    BadgeType.CONVERSION_MASTER: BadgeMetadata(
        name="Conversion Master",
        description="Completed 5+ conversions from recommendations",
        icon="check-circle",
        color="#EC4899",
        xp_bonus=200,
        earnings_boost_percent=15,
        required_value=5,
    ),
    BadgeType.REFERRAL_CHAMPION: BadgeMetadata(
        name="Referral Champion",
        description="Referred 5+ friends who joined AdMesh",
        icon="user-plus",
        color="#059669",
        xp_bonus=100,
        earnings_boost_percent=5,
        required_value=5,
    ),
    BadgeType.FEEDBACK_PROVIDER: BadgeMetadata(
        name="Feedback Provider",
        description="Provided detailed feedback on 5+ products",
        icon="message-square",
        color="#7C3AED",
        xp_bonus=50,
        required_value=5,
    ),
    BadgeType.FEEDBACK_CHAMPION: BadgeMetadata(
        name="Feedback Champion",
        description="Provided feedback on 10+ recommendations",
        icon="message-circle",
        color="#8B5CF6",
        xp_bonus=75,
        required_value=10,
    ),
    BadgeType.VERIFIED_EMAIL: BadgeMetadata(
        name="Verified Email",
        description="Verified your email address",
        icon="check",
        color="#22C55E",
        xp_bonus=10,
        required_value=1,
    ),
    BadgeType.PROFILE_COMPLETE: BadgeMetadata(
        name="Profile Complete",
        description="Completed all profile information",
        icon="user-check",
        color="#0EA5E9",
        xp_bonus=15,
        required_value=1,
    ),
    BadgeType.MILESTONE_100: BadgeMetadata(
        name="XP Milestone: 100",
        description="Reached 100 XP",
        icon="award",
        color="#6B7280",
        xp_bonus=10,
        required_value=100,
    ),
    BadgeType.MILESTONE_500: BadgeMetadata(
        name="XP Milestone: 500",
        description="Reached 500 XP",
        icon="award",
        color="#9CA3AF",
        xp_bonus=50,
        required_value=500,
    ),
    BadgeType.MILESTONE_1000: BadgeMetadata(
        name="XP Milestone: 1000",
        description="Reached 1000 XP",
        icon="award",
        color="#D1D5DB",
        xp_bonus=100,
        required_value=1000,
    ),
    BadgeType.DAILY_STREAK: BadgeMetadata(
        name="Daily Streak",
        description="Active for 30 consecutive days",
        icon="fire",
        color="#EF4444",
        xp_bonus=100,
        required_value=30,
    ),
    BadgeType.WEEKLY_ACTIVE: BadgeMetadata(
        name="Weekly Active",
        description="Used the agent for 7 consecutive days",
        icon="calendar",
        color="#3B82F6",
        xp_bonus=25,
        required_value=7,
    ),

    BadgeType.NIGHT_OWL: BadgeMetadata(
        name="Night Owl",
        description="Made 10+ queries between midnight and 5am",
        icon="moon",
        color="#4B5563",
        xp_bonus=25,
        required_value=10,
    ),
    BadgeType.EARLY_BIRD: BadgeMetadata(
        name="Early Bird",
        description="Made 10+ queries between 5am and 8am",
        icon="sunrise",
        color="#F59E0B",
        xp_bonus=25,
        required_value=10,
    ),
    BadgeType.WEEKEND_WARRIOR: BadgeMetadata(
        name="Weekend Warrior",
        description="Active on 5+ consecutive weekends",
        icon="calendar",
        color="#8B5CF6",
        xp_bonus=40,
        required_value=5,
    ),
    BadgeType.SOCIAL_BUTTERFLY: BadgeMetadata(
        name="Social Butterfly",
        description="Shared badges on 3+ social platforms",
        icon="share-2",
        color="#0EA5E9",
        xp_bonus=30,
        required_value=3,
    ),

    BadgeType.MILESTONE_5000: BadgeMetadata(
        name="XP Milestone: 5000",
        description="Reached 5000 XP",
        icon="award",
        color="#FCD34D",
        xp_bonus=500,
        required_value=5000,
    ),
    BadgeType.SUPER_AGENT: BadgeMetadata(
        name="Super Agent",
        description="Achieved exceptional performance across all metrics",
        icon="crown",
        color="#F59E0B",
        xp_bonus=1000,
        required_value=10000,
    ),
    BadgeType.FIRST_FOLLOWUP: BadgeMetadata(
        name="First Followup",
        description="Made your first followup query",
        icon="message-circle",
        color="#4F46E5",
        xp_bonus=10,
        required_value=1,
    ),
    BadgeType.FOLLOWUP_50: BadgeMetadata(
        name="Followup Master",
        description="Made 50+ followup queries",
        icon="message-square",
        color="#10B981",
        xp_bonus=50,
        required_value=50,
    ),
    BadgeType.FOLLOWUP_100: BadgeMetadata(
        name="Conversation Pro",
        description="Made 100+ followup queries",
        icon="message-circle-more",
        color="#F59E0B",
        xp_bonus=100,
        required_value=100,
    ),
}
