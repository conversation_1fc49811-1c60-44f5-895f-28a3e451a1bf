from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class PromoCredit(BaseModel):
    """Model for brand promo credit data stored in Firestore"""
    display_credit: int = 50  # Display credit amount in dollars
    max_conversions: int = 5  # Maximum number of conversions allowed
    used_conversions: int = 0  # Number of conversions used
    expires_at: Optional[datetime] = None  # Expiration date for promo credit
    plan_awarded_from: str = "free"  # Plan that awarded the promo credit
    is_active: bool = True  # Whether the promo credit is active

    @classmethod
    def create_free_plan_promo(cls, created_at: datetime = None) -> dict:
        """Create a promo credit object for the free plan"""
        # Set expiration date to May 7, 2025 (for all promo credits)
        # This is a fixed date for all promo credits regardless of when they are created
        expires_at = datetime(2025, 5, 7, 13, 19, 55)  # May 7, 2025 at 1:19:55 PM UTC

        return {
            "display_credit": 50,
            "max_conversions": 5,
            "used_conversions": 0,
            "expires_at": expires_at,
            "plan_awarded_from": "free",
            "is_active": True
        }

    @classmethod
    def update_for_starter_plan(cls, promo: dict, created_at: datetime) -> dict:
        """Update promo credit for upgrade to Starter plan"""
        # Check if upgrade is within 30 days of signup
        promo_eligibility_deadline = created_at + datetime.timedelta(days=30)

        if datetime.datetime.now() <= promo_eligibility_deadline:
            # Eligible for additional promo credit
            promo["display_credit"] = 100  # Increase to $100
            promo["max_conversions"] = 10  # Increase to 10 conversions
            promo["plan_awarded_from"] = "starter"

        return promo

    @classmethod
    def update_for_growth_plan(cls, promo: dict, created_at: datetime) -> dict:
        """Update promo credit for upgrade to Growth plan"""
        # Check if upgrade is within 30 days of signup
        promo_eligibility_deadline = created_at + datetime.timedelta(days=30)

        if datetime.datetime.now() <= promo_eligibility_deadline:
            # Eligible for additional promo credit
            promo["display_credit"] = 150  # Increase to $150
            promo["max_conversions"] = 20  # Increase to 20 conversions
            promo["plan_awarded_from"] = "growth"

        return promo

    @classmethod
    def is_eligible_for_conversion(cls, promo: dict) -> bool:
        """Check if promo credit is eligible for conversion"""
        # Check if promo is active
        if not promo.get("is_active", False):
            return False

        # Check if promo has expired
        expires_at = promo.get("expires_at")
        if expires_at and datetime.datetime.now() > expires_at:
            return False

        # Check if max conversions reached
        if promo.get("used_conversions", 0) >= promo.get("max_conversions", 0):
            return False

        return True

    @classmethod
    def increment_used_conversions(cls, promo: dict) -> dict:
        """Increment the used conversions count"""
        promo["used_conversions"] = promo.get("used_conversions", 0) + 1
        return promo
