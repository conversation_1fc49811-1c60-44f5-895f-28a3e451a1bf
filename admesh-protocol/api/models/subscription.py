from pydantic import BaseModel
from typing import Optional, Literal, List, Dict
from datetime import datetime

class SubscriptionPlan(BaseModel):
    """Model for subscription plan details"""
    id: Literal["free", "starter", "growth", "starter_annual", "growth_annual"]
    name: str
    price_monthly_cents: int  # Price in cents
    price_annual_cents: Optional[int] = None  # Annual price in cents
    billing_cycle: Literal["monthly", "annual", "free"] = "monthly"
    product_listings_limit: int
    active_offers_per_product_limit: int
    promo_credit_cents: int  # One-time promo credit in cents
    visibility_boost: str  # Basic, Boosted, or High priority
    analytics_level: str  # Basic, CTR+Conversions, or Full funnel
    conversion_reports: str  # Basic, Filtered, or Advanced
    agent_match_priority: str  # Normal, Medium, or Highest
    badge_type: Optional[str] = None  # None, Featured, or Top Pick
    support_level: str  # Community, Email, or Priority
    agent_outreach_tools: bool = False
    multi_user_access: bool = False
    multi_user_limit: int = 0
    cpa_optimization: bool = False
    features: List[str]

    @classmethod
    def get_free_plan(cls):
        return cls(
            id="free",
            name="Free",
            price_monthly_cents=0,
            price_annual_cents=0,
            billing_cycle="free",
            product_listings_limit=1,
            active_offers_per_product_limit=1,
            promo_credit_cents=5000,  # $50
            visibility_boost="Basic (relevance only)",
            analytics_level="Basic (clicks + conversions)",
            conversion_reports="Basic (no filters)",
            agent_match_priority="Normal",
            badge_type=None,
            support_level="Community",
            agent_outreach_tools=False,
            multi_user_access=False,
            multi_user_limit=0,
            cpa_optimization=False,
            features=[
                "1 Product Listing",
                "1 Active Offer per Product",
                "$50 one-time promo credit",
                "Basic analytics (clicks + conversions)",
                "Basic conversion reports (no filters)",
                "Normal agent match priority",
                "Community support"
            ]
        )

    @classmethod
    def get_starter_plan(cls, annual=False):
        # 20% discount for annual billing
        monthly_price = 2900  # $29/month
        annual_price = monthly_price * 12 * 0.8  # 20% discount

        return cls(
            id="starter_annual" if annual else "starter",
            name="Starter",
            price_monthly_cents=monthly_price,
            price_annual_cents=int(annual_price),
            billing_cycle="annual" if annual else "monthly",
            product_listings_limit=5,
            active_offers_per_product_limit=-1,  # Unlimited
            promo_credit_cents=10000,  # $100
            visibility_boost="Boosted (CPA-weighted placement)",
            analytics_level="CTR + Conversions",
            conversion_reports="Filter by product / offer",
            agent_match_priority="Medium",
            badge_type="Featured",
            support_level="Email (48hr SLA)",
            agent_outreach_tools=False,
            multi_user_access=False,
            multi_user_limit=0,
            cpa_optimization=False,
            features=[
                "5 Product Listings",
                "Unlimited Active Offers per Product",
                "$100 one-time promo credit",
                "Boosted offer visibility (CPA-weighted placement)",
                "CTR + Conversion analytics",
                "Conversion reports with product/offer filters",
                "Medium agent match priority",
                "Featured badge in UI/Chat",
                "Email support (48hr SLA)",
                f"Save 20% with annual billing" if annual else "Monthly billing flexibility"
            ]
        )

    @classmethod
    def get_growth_plan(cls, annual=False):
        # 20% discount for annual billing
        monthly_price = 9900  # $99/month
        annual_price = monthly_price * 12 * 0.8  # 20% discount

        return cls(
            id="growth_annual" if annual else "growth",
            name="Growth",
            price_monthly_cents=monthly_price,
            price_annual_cents=int(annual_price),
            billing_cycle="annual" if annual else "monthly",
            product_listings_limit=-1,  # Unlimited
            active_offers_per_product_limit=-1,  # Unlimited
            promo_credit_cents=15000,  # $150
            visibility_boost="High priority in agent results",
            analytics_level="Full funnel: Impressions → ROI",
            conversion_reports="Filter by time, intent, offer, ROI",
            agent_match_priority="Highest",
            badge_type="Top Pick",
            support_level="Priority (24hr SLA)",
            agent_outreach_tools=True,
            multi_user_access=True,
            multi_user_limit=3,
            cpa_optimization=True,
            features=[
                "Unlimited Product Listings",
                "Unlimited Active Offers per Product",
                "$150 one-time promo credit",
                "High priority in agent results",
                "Full funnel analytics (Impressions → ROI)",
                "Advanced conversion reports with time/intent/ROI filters",
                "Highest agent match priority",
                "Top Pick badge in UI/Chat",
                "Priority support (24hr SLA)",
                "Early access to Agent Outreach Tools (Beta)",
                "Multi-user access (up to 3 teammates)",
                "CPA Optimization Concierge",
                f"Save 20% with annual billing" if annual else "Monthly billing flexibility"
            ]
        )

    @classmethod
    def get_all_plans(cls):
        """Get all available subscription plans"""
        # First try to get from the database
        from firebase.config import get_db
        db = get_db()

        try:
            # Get plans from the subscriptionPlans collection
            plans_ref = db.collection("subscriptionPlans")
            plans_docs = plans_ref.stream()

            plans = []
            for doc in plans_docs:
                plan_data = doc.to_dict()
                plans.append(cls(**plan_data))

            # If plans were found in the database, return them
            if plans:
                return plans
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Error getting plans from database: {str(e)}. Using hardcoded plans.")

        # Fallback to hardcoded plans
        return [
            cls.get_free_plan(),
            cls.get_starter_plan(annual=False),
            cls.get_starter_plan(annual=True),
            cls.get_growth_plan(annual=False),
            cls.get_growth_plan(annual=True)
        ]

    @classmethod
    def get_plan_by_id(cls, plan_id: str):
        """Get a subscription plan by ID"""
        # First try to get from the database
        from firebase.config import get_db
        db = get_db()

        try:
            # Get plan from the subscriptionPlans collection
            plan_ref = db.collection("subscriptionPlans").document(plan_id)
            plan_doc = plan_ref.get()

            if plan_doc.exists:
                plan_data = plan_doc.to_dict()
                return cls(**plan_data)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Error getting plan {plan_id} from database: {str(e)}. Using hardcoded plan.")

        # Fallback to hardcoded plans
        if plan_id == "free":
            return cls.get_free_plan()
        elif plan_id == "starter":
            return cls.get_starter_plan(annual=False)
        elif plan_id == "starter_annual":
            return cls.get_starter_plan(annual=True)
        elif plan_id == "growth":
            return cls.get_growth_plan(annual=False)
        elif plan_id == "growth_annual":
            return cls.get_growth_plan(annual=True)
        else:
            raise ValueError(f"Invalid plan ID: {plan_id}")

class BrandSubscription(BaseModel):
    """Legacy model for brand subscription data stored in Firestore"""
    plan_id: str  # "free", "starter", "growth", "starter_annual", "growth_annual"
    billing_cycle: Literal["monthly", "annual", "free"] = "monthly"
    status: Literal["active", "canceled", "past_due", "trialing", "incomplete"]
    stripe_subscription_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    previous_plan_id: Optional[str] = None  # For tracking upgrades

    # Feature usage tracking
    product_listings_used: int = 0
    promo_credit_applied: bool = False
    promo_credit_amount_cents: int = 0

    # Feature access flags
    has_multi_user_access: bool = False
    multi_user_count: int = 0
    has_cpa_optimization: bool = False
    has_agent_outreach_tools: bool = False


class SubscriptionLimits(BaseModel):
    """Model for subscription limits"""
    product_listings: int = 1  # Number of products allowed
    active_offers_per_product: int = 1  # Number of active offers per product
    keyword_limit: int = 10  # Number of keywords allowed
    multi_user_limit: int = 0  # Number of users allowed
    # Add more limits as needed


class SubscriptionUsage(BaseModel):
    """Model for subscription usage tracking"""
    product_listings: int = 0  # Number of products used
    active_offers: Dict[str, int] = {}  # Product ID -> number of active offers
    keywords_used: int = 0  # Number of keywords used
    multi_users: int = 0  # Number of users added
    # Add more usage metrics as needed


class SubscriptionReferences(BaseModel):
    """Model for subscription external references"""
    stripe_subscription_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    stripe_price_id: Optional[str] = None
    # Add more external references as needed


class SubscriptionDocument(BaseModel):
    """New model for subscription document stored in brands/{brandId}/subscriptions/{subscriptionId}"""
    id: str  # Document ID (typically "current")
    plan_id: str  # "free", "starter", "growth", "starter_annual", "growth_annual"
    billing_cycle: Literal["monthly", "annual", "free"] = "monthly"
    status: Literal["active", "canceled", "past_due", "trialing", "incomplete"]
    current_period_start: Optional[datetime] = None
    current_period_end: Optional[datetime] = None
    cancel_at_period_end: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    previous_plan_id: Optional[str] = None  # For tracking upgrades

    # Maps for limits, usage, and references
    limits: SubscriptionLimits = SubscriptionLimits()
    usage: SubscriptionUsage = SubscriptionUsage()
    references: SubscriptionReferences = SubscriptionReferences()

    # Feature flags
    promo_credit_applied: bool = False
    promo_credit_amount_cents: int = 0
    has_multi_user_access: bool = False
    has_cpa_optimization: bool = False
    has_agent_outreach_tools: bool = False

class SubscriptionRequest(BaseModel):
    """Request model for creating or updating a subscription"""
    plan_id: str  # "free", "starter", "growth", "starter_annual", "growth_annual"
    billing_cycle: Optional[Literal["monthly", "annual", "free"]] = None
    payment_method_id: Optional[str] = None  # Required for paid plans

class SubscriptionResponse(BaseModel):
    """Response model for subscription operations"""
    success: bool
    message: str
    subscription: Optional[BrandSubscription] = None
    checkout_session_url: Optional[str] = None


def convert_plan_to_limits(plan: SubscriptionPlan) -> SubscriptionLimits:
    """Convert a SubscriptionPlan to SubscriptionLimits"""
    return SubscriptionLimits(
        product_listings=plan.product_listings_limit,
        active_offers_per_product=plan.active_offers_per_product_limit,
        keyword_limit=get_keyword_limit_by_plan_id(plan.id),
        multi_user_limit=plan.multi_user_limit
    )


def get_keyword_limit_by_plan_id(plan_id: str) -> int:
    """Get keyword limit based on plan ID"""
    # Handle annual plans by removing _annual suffix
    base_plan_id = plan_id.replace('_annual', '')

    if base_plan_id == 'free':
        return 10
    elif base_plan_id == 'starter':
        return 20
    elif base_plan_id == 'growth':
        return 30
    else:
        return 10  # Default to free plan limit


def convert_old_subscription_to_new(old_sub: BrandSubscription, brand_id: str) -> SubscriptionDocument:
    """Convert an old BrandSubscription to a new SubscriptionDocument"""
    # Get the plan details to set limits
    try:
        plan = SubscriptionPlan.get_plan_by_id(old_sub.plan_id)
        limits = convert_plan_to_limits(plan)
    except ValueError:
        # Fallback to default limits if plan_id is invalid
        limits = SubscriptionLimits()

    # Create references
    references = SubscriptionReferences(
        stripe_subscription_id=old_sub.stripe_subscription_id,
        stripe_customer_id=old_sub.stripe_customer_id
    )

    # Create usage (we'll need to populate this from the brand document)
    usage = SubscriptionUsage(
        product_listings=old_sub.product_listings_used,
        multi_users=old_sub.multi_user_count
    )

    # Create the new subscription document
    return SubscriptionDocument(
        id="current",
        plan_id=old_sub.plan_id,
        billing_cycle=old_sub.billing_cycle,
        status=old_sub.status,
        current_period_start=old_sub.current_period_start,
        current_period_end=old_sub.current_period_end,
        cancel_at_period_end=old_sub.cancel_at_period_end,
        created_at=old_sub.created_at or datetime.now(),
        updated_at=datetime.now(),
        previous_plan_id=old_sub.previous_plan_id,
        limits=limits,
        usage=usage,
        references=references,
        promo_credit_applied=old_sub.promo_credit_applied,
        promo_credit_amount_cents=old_sub.promo_credit_amount_cents,
        has_multi_user_access=old_sub.has_multi_user_access,
        has_cpa_optimization=old_sub.has_cpa_optimization,
        has_agent_outreach_tools=old_sub.has_agent_outreach_tools
    )
