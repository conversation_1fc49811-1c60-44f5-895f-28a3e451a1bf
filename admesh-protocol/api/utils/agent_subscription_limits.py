from firebase.config import get_db
from api.models.agent_subscription import (
    AgentSubscriptionPlan, AgentSubscription, AgentUsage, AgentQueryLimitCheck,
    FREE_PLAN_THROTTLE_CONDITIONS
)
import logging
from datetime import datetime, timedelta
from google.cloud import firestore

db = get_db()
logger = logging.getLogger(__name__)

async def get_agent_subscription(agent_id: str) -> AgentSubscription:
    """Get the agent's current subscription document"""
    # Get the agent document to check for subscription
    agent_ref = db.collection("agents").document(agent_id)
    agent_doc = agent_ref.get()

    if not agent_doc.exists:
        raise ValueError(f"Agent {agent_id} not found")

    agent_data = agent_doc.to_dict()
    
    # Check if agent has a subscription
    if "subscription" in agent_data and agent_data["subscription"]:
        subscription_data = agent_data["subscription"]
        logger.info(f"Found existing subscription for agent {agent_id}")
        return AgentSubscription(**subscription_data)
    else:
        # No subscription exists, create a default free subscription
        logger.info(f"Creating default free subscription for agent {agent_id}")
        subscription = AgentSubscription(
            plan_id="free",
            status="active",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            usage=AgentUsage()
        )
        
        # Save the subscription to the agent document
        agent_ref.update({
            "subscription": subscription.dict(),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        return subscription

async def get_agent_subscription_plan(agent_id: str) -> AgentSubscriptionPlan:
    """Get the agent's current subscription plan details"""
    subscription = await get_agent_subscription(agent_id)
    try:
        return AgentSubscriptionPlan.get_plan_by_id(subscription.plan_id)
    except ValueError:
        logger.error(f"Invalid plan ID: {subscription.plan_id}, defaulting to free plan")
        return AgentSubscriptionPlan.get_free_plan()

async def check_query_limit(agent_id: str) -> AgentQueryLimitCheck:
    """Check if the agent can make another query"""
    try:
        subscription = await get_agent_subscription(agent_id)
        plan = await get_agent_subscription_plan(agent_id)
        
        # Reset counters if needed
        now = datetime.now()
        usage = subscription.usage
        
        # Check if we need to reset daily counter
        if usage.last_daily_reset is None or usage.last_daily_reset.date() < now.date():
            usage.daily_queries_used = 0
            usage.last_daily_reset = now
            
        # Check if we need to reset monthly counter
        if (usage.last_monthly_reset is None or 
            usage.last_monthly_reset.month != now.month or 
            usage.last_monthly_reset.year != now.year):
            usage.monthly_queries_used = 0
            usage.last_monthly_reset = now
        
        # Check daily limit
        if plan.limits.daily_query_limit != -1:  # -1 means unlimited
            if usage.daily_queries_used >= plan.limits.daily_query_limit:
                return AgentQueryLimitCheck(
                    allowed=False,
                    reason="Daily query limit exceeded",
                    daily_remaining=0,
                    monthly_remaining=max(0, plan.limits.monthly_query_limit - usage.monthly_queries_used) if plan.limits.monthly_query_limit != -1 else None,
                    throttle_active=usage.throttle_active
                )
        
        # Check monthly limit
        if plan.limits.monthly_query_limit != -1:  # -1 means unlimited
            if usage.monthly_queries_used >= plan.limits.monthly_query_limit:
                return AgentQueryLimitCheck(
                    allowed=False,
                    reason="Monthly query limit exceeded",
                    daily_remaining=max(0, plan.limits.daily_query_limit - usage.daily_queries_used) if plan.limits.daily_query_limit != -1 else None,
                    monthly_remaining=0,
                    throttle_active=usage.throttle_active
                )
        
        # Check trust score throttling (for free plan)
        trust_score_warning = False
        if plan.limits.throttle_enabled and usage.current_trust_score < plan.limits.trust_score_threshold:
            # Apply soft throttle for low trust score
            throttled_daily_limit = 2000  # Reduced limit for low trust
            if usage.daily_queries_used >= throttled_daily_limit:
                return AgentQueryLimitCheck(
                    allowed=False,
                    reason=f"Trust score throttle active (score: {usage.current_trust_score})",
                    daily_remaining=0,
                    monthly_remaining=max(0, plan.limits.monthly_query_limit - usage.monthly_queries_used) if plan.limits.monthly_query_limit != -1 else None,
                    throttle_active=True,
                    trust_score_warning=True
                )
            trust_score_warning = True
        
        # Calculate remaining queries
        daily_remaining = None
        monthly_remaining = None
        
        if plan.limits.daily_query_limit != -1:
            if plan.limits.throttle_enabled and usage.current_trust_score < plan.limits.trust_score_threshold:
                daily_remaining = max(0, 2000 - usage.daily_queries_used)  # Throttled limit
            else:
                daily_remaining = max(0, plan.limits.daily_query_limit - usage.daily_queries_used)
        
        if plan.limits.monthly_query_limit != -1:
            monthly_remaining = max(0, plan.limits.monthly_query_limit - usage.monthly_queries_used)
        
        return AgentQueryLimitCheck(
            allowed=True,
            daily_remaining=daily_remaining,
            monthly_remaining=monthly_remaining,
            throttle_active=usage.throttle_active,
            trust_score_warning=trust_score_warning
        )
        
    except Exception as e:
        logger.error(f"Error checking query limit for agent {agent_id}: {str(e)}")
        # Default to allowing the query but log the error
        return AgentQueryLimitCheck(allowed=True, reason="Error checking limits")

async def increment_query_count(agent_id: str):
    """Increment the agent's query count"""
    try:
        agent_ref = db.collection("agents").document(agent_id)
        
        # Use a transaction to safely increment counters
        @firestore.transactional
        def update_in_transaction(transaction):
            agent_doc = agent_ref.get(transaction=transaction)
            if not agent_doc.exists:
                return
            
            agent_data = agent_doc.to_dict()
            subscription_data = agent_data.get("subscription", {})
            usage_data = subscription_data.get("usage", {})
            
            # Increment counters
            usage_data["daily_queries_used"] = usage_data.get("daily_queries_used", 0) + 1
            usage_data["monthly_queries_used"] = usage_data.get("monthly_queries_used", 0) + 1
            
            # Update the document
            transaction.update(agent_ref, {
                "subscription.usage": usage_data,
                "subscription.updated_at": firestore.SERVER_TIMESTAMP
            })
        
        transaction = db.transaction()
        update_in_transaction(transaction)
        
        logger.info(f"Incremented query count for agent {agent_id}")
        
    except Exception as e:
        logger.error(f"Error incrementing query count for agent {agent_id}: {str(e)}")

async def update_agent_trust_score(agent_id: str, new_trust_score: float):
    """Update the agent's trust score and check for throttling conditions"""
    try:
        agent_ref = db.collection("agents").document(agent_id)
        
        # Update trust score
        agent_ref.update({
            "subscription.usage.current_trust_score": new_trust_score,
            "subscription.updated_at": firestore.SERVER_TIMESTAMP
        })
        
        # Check if throttling should be activated/deactivated
        subscription = await get_agent_subscription(agent_id)
        plan = await get_agent_subscription_plan(agent_id)
        
        if plan.limits.throttle_enabled:
            should_throttle = new_trust_score < plan.limits.trust_score_threshold
            if should_throttle != subscription.usage.throttle_active:
                agent_ref.update({
                    "subscription.usage.throttle_active": should_throttle,
                    "subscription.updated_at": firestore.SERVER_TIMESTAMP
                })
                
                logger.info(f"Agent {agent_id} throttle status changed to: {should_throttle}")
        
        logger.info(f"Updated trust score for agent {agent_id} to {new_trust_score}")
        
    except Exception as e:
        logger.error(f"Error updating trust score for agent {agent_id}: {str(e)}")
