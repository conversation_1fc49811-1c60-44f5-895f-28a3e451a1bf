from firebase.config import get_db
from api.models.subscription import (
    SubscriptionPlan, SubscriptionDocument, convert_plan_to_limits
)
import logging
from datetime import datetime
from google.cloud import firestore

db = get_db()
logger = logging.getLogger(__name__)

async def get_subscription_document(brand_id: str) -> SubscriptionDocument:
    """Get the brand's current subscription document"""
    # Get the brand document to check for currentSubscriptionId
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise ValueError(f"Brand {brand_id} not found")

    brand_data = brand_doc.to_dict()

    # Check if the brand has a currentSubscriptionId
    if "currentSubscriptionId" in brand_data and brand_data["currentSubscriptionId"]:
        # Use the currentSubscriptionId to get the subscription document
        subscription_id = brand_data["currentSubscriptionId"]
        sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document(subscription_id)
        sub_doc = sub_ref.get()

        if sub_doc.exists:
            # Convert Firestore document to SubscriptionDocument
            sub_data = sub_doc.to_dict()
            return SubscriptionDocument(**sub_data)

    # Check if any subscriptions exist for this brand
    subscriptions = list(db.collection("brands").document(brand_id).collection("subscriptions").limit(1).stream())

    if subscriptions:
        # Use the first existing subscription
        existing_sub = subscriptions[0]
        subscription_id = existing_sub.id
        sub_data = existing_sub.to_dict()

        # Update the brand document with this subscription ID
        brand_ref.update({
            "currentSubscriptionId": subscription_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Found existing subscription {subscription_id} for brand {brand_id}")
        return SubscriptionDocument(**sub_data)
    else:
        # No subscriptions exist, create a default free subscription
        # Generate a unique ID for the subscription
        subscription_id = db.collection("brands").document(brand_id).collection("subscriptions").document().id

        plan = SubscriptionPlan.get_free_plan()
        sub_doc = SubscriptionDocument(
            id=subscription_id,
            plan_id="free",
            billing_cycle="free",
            status="active",
            created_at=datetime.now(),
            updated_at=datetime.now(),
            limits=convert_plan_to_limits(plan)
        )

        # Save the new subscription document
        sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document(subscription_id)
        sub_ref.set(sub_doc.model_dump(mode="json"))

        # Update the brand document with the current subscription ID
        brand_ref.update({
            "currentSubscriptionId": subscription_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Created new subscription {subscription_id} for brand {brand_id}")
        return sub_doc

async def get_subscription_plan(brand_id: str):
    """Get the brand's current subscription plan details"""
    sub_doc = await get_subscription_document(brand_id)
    try:
        return SubscriptionPlan.get_plan_by_id(sub_doc.plan_id)
    except ValueError:
        logger.error(f"Invalid plan ID: {sub_doc.plan_id}, defaulting to free plan")
        return SubscriptionPlan.get_free_plan()

async def check_product_limit(brand_id: str):
    """Check if the brand has reached their product limit"""
    sub_doc = await get_subscription_document(brand_id)

    # If unlimited products (represented by -1), no need to check
    if sub_doc.limits.product_listings == -1:
        return True

    # Check if limit reached
    if sub_doc.usage.product_listings >= sub_doc.limits.product_listings:
        return False

    return True

async def check_offer_limit(brand_id: str, product_id: str):
    """Check if the brand has reached their offer limit for a product"""
    sub_doc = await get_subscription_document(brand_id)

    # If unlimited offers per product (represented by -1), no need to check
    if sub_doc.limits.active_offers_per_product == -1:
        return True

    # Get current offers for this product
    product_offers = sub_doc.usage.active_offers.get(product_id, 0)

    # Check if limit reached
    if product_offers >= sub_doc.limits.active_offers_per_product:
        return False

    return True

async def update_product_count(brand_id: str):
    """Update the product count in the brand's subscription"""
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        return

    brand_data = brand_doc.to_dict()
    active_products = brand_data.get("active_products", [])
    product_count = len(active_products)

    # Update in the subscription document
    try:
        sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document("current")
        sub_doc = sub_ref.get()

        if sub_doc.exists:
            sub_ref.update({
                "usage.product_listings": product_count,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
        else:
            # If the document doesn't exist, create it
            await get_subscription_document(brand_id)
    except Exception as e:
        logger.error(f"Error updating product count in subscription: {str(e)}")

async def check_keyword_limit(brand_id: str, keywords: list[str]):
    """Check if the brand has reached their keyword limit"""
    sub_doc = await get_subscription_document(brand_id)

    # If unlimited keywords (represented by -1), no need to check
    if sub_doc.limits.keyword_limit == -1:
        return True

    # Check if the number of keywords exceeds the limit
    if len(keywords) > sub_doc.limits.keyword_limit:
        return False

    return True

async def update_offer_count(brand_id: str, product_id: str, count: int):
    """Update the offer count for a specific product in the subscription"""
    try:
        sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document("current")
        sub_doc = sub_ref.get()

        if sub_doc.exists:
            # Update the offer count for this product
            sub_ref.update({
                f"usage.active_offers.{product_id}": count,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
        else:
            # If the document doesn't exist, create it
            sub_doc = await get_subscription_document(brand_id)

            # Then update the offer count
            active_offers = sub_doc.usage.active_offers
            active_offers[product_id] = count

            sub_ref.update({
                "usage.active_offers": active_offers,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
    except Exception as e:
        logger.error(f"Error updating offer count in subscription: {str(e)}")
