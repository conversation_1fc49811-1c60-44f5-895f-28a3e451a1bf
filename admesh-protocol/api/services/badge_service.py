import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional
from firebase_admin import firestore as admin_firestore
from firebase.config import get_db
from api.models.badge import Badge, BadgeType, BADGE_DEFINITIONS

logger = logging.getLogger(__name__)
db = get_db()

def safe_timestamp():
    """Return a safe timestamp for use in Firestore arrays and nested structures.
    Use this instead of SERVER_TIMESTAMP when adding timestamps to arrays or nested objects.
    """
    # Return a datetime object that Firestore can convert automatically
    return datetime.now(timezone.utc)

class BadgeService:
    """Service for managing user badges"""

    @staticmethod
    async def get_user_badges(user_id: str) -> List[Dict[str, Any]]:
        """Get all badges for a user from their subcollection"""
        badges_ref = db.collection("users").document(user_id).collection("badges").stream()
        return [badge.to_dict() for badge in badges_ref]

    @staticmethod
    async def award_badge(user_id: str, badge_type: BadgeType, progress: Optional[int] = None) -> Optional[Badge]:
        """Award a badge to a user if they don't already have it"""
        # Check if user already has this badge in their subcollection
        existing_badge = db.collection("users").document(user_id).collection("badges").where(
            "badge_type", "==", badge_type
        ).limit(1).get()

        if len(existing_badge) > 0:
            # User already has this badge, update progress if provided
            if progress is not None:
                badge_doc = existing_badge[0]
                badge_ref = db.collection("users").document(user_id).collection("badges").document(badge_doc.id)
                badge_ref.update({
                    "progress": progress,
                    "updated_at": safe_timestamp()
                })
            return None

        # Get badge metadata
        metadata = BADGE_DEFINITIONS.get(badge_type)
        if not metadata:
            logger.error(f"Badge type {badge_type} not found in definitions")
            return None

        # Create new badge with user_id in the badge_id
        badge_id = f"{user_id}-{badge_type.value}"
        badge = Badge(
            badge_id=badge_id,
            user_id=user_id,
            badge_type=badge_type,
            awarded_at=safe_timestamp().isoformat(),
            metadata=metadata,
            progress=progress,
            progress_target=metadata.required_value if metadata.required_value > 0 else None
        )

        # Save to Firestore as a subcollection under the user
        badge_dict = badge.model_dump()
        # Use the safe_timestamp that's already in the badge object
        db.collection("users").document(user_id).collection("badges").document(badge_id).set(badge_dict)

        # Award XP bonus if applicable
        if metadata.xp_bonus > 0:
            await award_xp_bonus(user_id, metadata.xp_bonus, f"Badge earned: {metadata.name}")

        return badge

    @staticmethod
    async def check_and_award_badges(user_id: str) -> List[Badge]:
        """Check all badge criteria and award badges as appropriate"""
        awarded_badges = []

        # Get user data
        user_ref = db.collection("users").document(user_id)
        user_doc = user_ref.get()
        if not user_doc.exists:
            logger.error(f"User {user_id} not found")
            return []

        user_data = user_doc.to_dict()

        # Check XP-based badges
        xp = user_data.get("xp", 0)

        # Agent Pioneer Badge (1000 XP)
        if xp >= 1000:
            badge = await BadgeService.award_badge(user_id, BadgeType.AGENT_PIONEER, xp)
            if badge:
                awarded_badges.append(badge)

        # XP Milestone badges
        if xp >= 100:
            badge = await BadgeService.award_badge(user_id, BadgeType.MILESTONE_100, xp)
            if badge:
                awarded_badges.append(badge)

        if xp >= 500:
            badge = await BadgeService.award_badge(user_id, BadgeType.MILESTONE_500, xp)
            if badge:
                awarded_badges.append(badge)

        if xp >= 1000:
            badge = await BadgeService.award_badge(user_id, BadgeType.MILESTONE_1000, xp)
            if badge:
                awarded_badges.append(badge)

        if xp >= 5000:
            badge = await BadgeService.award_badge(user_id, BadgeType.MILESTONE_5000, xp)
            if badge:
                awarded_badges.append(badge)

        # Super Agent Badge - requires 10000 XP
        if xp >= 10000:
            badge = await BadgeService.award_badge(user_id, BadgeType.SUPER_AGENT, xp)
            if badge:
                awarded_badges.append(badge)

        # Check query-based badges
        query_count = await get_user_query_count(user_id)

        # First Discovery Badge
        if query_count >= 1:
            badge = await BadgeService.award_badge(user_id, BadgeType.FIRST_DISCOVERY, query_count)
            if badge:
                awarded_badges.append(badge)

        # Power Explorer Badge
        if query_count >= 50:
            badge = await BadgeService.award_badge(user_id, BadgeType.POWER_EXPLORER, query_count)
            if badge:
                awarded_badges.append(badge)

        # Check click-based badges
        click_count = user_data.get("clicks_made", 0)

        # Top Recommender Badge
        if click_count >= 10:
            badge = await BadgeService.award_badge(user_id, BadgeType.TOP_RECOMMENDER, click_count)
            if badge:
                awarded_badges.append(badge)

        # Check conversion-based badges
        conversion_count = user_data.get("conversions", 0)

        # Conversion Master Badge
        if conversion_count >= 5:
            badge = await BadgeService.award_badge(user_id, BadgeType.CONVERSION_MASTER, conversion_count)
            if badge:
                awarded_badges.append(badge)

        # Check feedback-based badges
        feedback_count = await get_user_feedback_count(user_id)

        # Feedback Provider Badge
        if feedback_count >= 5:
            badge = await BadgeService.award_badge(user_id, BadgeType.FEEDBACK_PROVIDER, feedback_count)
            if badge:
                awarded_badges.append(badge)

        # Feedback Champion Badge
        if feedback_count >= 10:
            badge = await BadgeService.award_badge(user_id, BadgeType.FEEDBACK_CHAMPION, feedback_count)
            if badge:
                awarded_badges.append(badge)

        # Check for verified email
        if user_data.get("emailVerified", False):
            badge = await BadgeService.award_badge(user_id, BadgeType.VERIFIED_EMAIL)
            if badge:
                awarded_badges.append(badge)

        # Check for profile completeness
        # This is a simplified check - you may want to define what "complete" means
        if user_data.get("displayName") and user_data.get("email") and user_data.get("photoURL"):
            badge = await BadgeService.award_badge(user_id, BadgeType.PROFILE_COMPLETE)
            if badge:
                awarded_badges.append(badge)

        # Check for first reward
        if user_data.get("rewards_earned", 0) > 0:
            badge = await BadgeService.award_badge(user_id, BadgeType.FIRST_REWARD)
            if badge:
                awarded_badges.append(badge)

        # Check for referrals
        if user_data.get("referrals", 0) >= 5:
            badge = await BadgeService.award_badge(user_id, BadgeType.REFERRAL_CHAMPION, user_data.get("referrals", 0))
            if badge:
                awarded_badges.append(badge)



        # Check for activity streaks
        login_history = await get_user_login_history(user_id)

        # Weekly Active Badge (7 consecutive days)
        if has_consecutive_days(login_history, 7):
            badge = await BadgeService.award_badge(user_id, BadgeType.WEEKLY_ACTIVE, 7)
            if badge:
                awarded_badges.append(badge)

        # Daily Streak Badge (30 consecutive days)
        if has_consecutive_days(login_history, 30):
            badge = await BadgeService.award_badge(user_id, BadgeType.DAILY_STREAK, 30)
            if badge:
                awarded_badges.append(badge)

        # Weekend Warrior Badge (5 consecutive weekends)
        if has_consecutive_weekends(login_history, 5):
            badge = await BadgeService.award_badge(user_id, BadgeType.WEEKEND_WARRIOR, 5)
            if badge:
                awarded_badges.append(badge)

        # Night Owl Badge (10+ queries between midnight and 5am)
        night_queries = await get_queries_in_timeframe(user_id, 0, 5)
        if night_queries >= 10:
            badge = await BadgeService.award_badge(user_id, BadgeType.NIGHT_OWL, night_queries)
            if badge:
                awarded_badges.append(badge)

        # Early Bird Badge (10+ queries between 5am and 8am)
        early_queries = await get_queries_in_timeframe(user_id, 5, 8)
        if early_queries >= 10:
            badge = await BadgeService.award_badge(user_id, BadgeType.EARLY_BIRD, early_queries)
            if badge:
                awarded_badges.append(badge)



        # Social Butterfly Badge (shared on 3+ platforms)
        if user_data.get("share_platforms", 0) >= 3:
            badge = await BadgeService.award_badge(user_id, BadgeType.SOCIAL_BUTTERFLY, user_data.get("share_platforms", 0))
            if badge:
                awarded_badges.append(badge)

        # Check followup-based badges
        followup_count = await get_user_followup_count(user_id)

        # First Followup Badge
        if followup_count >= 1:
            badge = await BadgeService.award_badge(user_id, BadgeType.FIRST_FOLLOWUP, followup_count)
            if badge:
                awarded_badges.append(badge)

        # Followup Master Badge (50+ followups)
        if followup_count >= 50:
            badge = await BadgeService.award_badge(user_id, BadgeType.FOLLOWUP_50, followup_count)
            if badge:
                awarded_badges.append(badge)

        # Conversation Pro Badge (100+ followups)
        if followup_count >= 100:
            badge = await BadgeService.award_badge(user_id, BadgeType.FOLLOWUP_100, followup_count)
            if badge:
                awarded_badges.append(badge)

        return awarded_badges

async def get_user_query_count(user_id: str) -> int:
    """Get the number of queries made by a user"""
    # Count sessions where this user is the user_id
    sessions_ref = db.collection("sessions").where("user_id", "==", user_id)
    sessions = sessions_ref.get()

    # Sum up the queries in each session
    total_queries = 0
    for session in sessions:
        session_data = session.to_dict()
        queries = session_data.get("queries", [])
        total_queries += len(queries)

    return total_queries

async def get_user_followup_count(user_id: str) -> int:
    """Get the total number of followups made by a user across all sessions"""
    # Get all sessions for this user
    sessions_ref = db.collection("sessions").where("user_id", "==", user_id)
    sessions = sessions_ref.get()

    # Sum up the followup counts from each session
    total_followups = 0
    for session in sessions:
        session_data = session.to_dict()
        followup_count = session_data.get("followup_count", 0)
        total_followups += followup_count

    return total_followups

async def get_user_feedback_count(user_id: str) -> int:
    """Get the number of feedback submissions made by a user"""
    feedback_ref = db.collection("feedback").where("user_id", "==", user_id)
    feedback = feedback_ref.get()
    return len(feedback)

async def get_user_login_history(user_id: str) -> List[datetime]:
    """Get the login history for a user"""
    try:
        # Modified query to avoid requiring a composite index
        login_ref = db.collection("user_logins").where("user_id", "==", user_id).get()

        login_dates = []
        for login in login_ref:
            login_data = login.to_dict()
            timestamp = login_data.get("timestamp")
            if timestamp:
                # Check if it's a Firestore Timestamp object
                if hasattr(timestamp, 'seconds') and hasattr(timestamp, 'nanoseconds'):
                    login_dates.append(timestamp.datetime)
                else:
                    try:
                        login_dates.append(datetime.fromisoformat(timestamp))
                    except (ValueError, TypeError):
                        pass

        # Sort the dates after retrieving them
        login_dates.sort()
        return login_dates
    except Exception as e:
        logger.error(f"Error getting user login history: {str(e)}")
        return []

def has_consecutive_days(login_dates: List[datetime], required_days: int) -> bool:
    """Check if the user has logged in for the required number of consecutive days"""
    if not login_dates or len(login_dates) < required_days:
        return False

    # Convert all dates to date objects (without time)
    date_set = set()
    for dt in login_dates:
        date_set.add(dt.date())

    # Sort dates
    dates = sorted(list(date_set))

    # Check for consecutive days
    max_streak = 1
    current_streak = 1

    for i in range(1, len(dates)):
        if (dates[i] - dates[i-1]).days == 1:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 1

    return max_streak >= required_days

def has_consecutive_weekends(login_dates: List[datetime], required_weekends: int) -> bool:
    """Check if the user has logged in for the required number of consecutive weekends"""
    if not login_dates:
        return False

    # Get all weekend dates (Saturday or Sunday)
    weekend_dates = set()
    for dt in login_dates:
        # 5 = Saturday, 6 = Sunday
        if dt.weekday() >= 5:
            # Get the start of the weekend (Saturday)
            weekend_start = dt.date()
            if dt.weekday() == 6:  # Sunday
                weekend_start = weekend_start - timedelta(days=1)
            weekend_dates.add(weekend_start)

    # Sort weekend dates
    weekend_list = sorted(list(weekend_dates))

    if len(weekend_list) < required_weekends:
        return False

    # Check for consecutive weekends
    max_streak = 1
    current_streak = 1

    for i in range(1, len(weekend_list)):
        if (weekend_list[i] - weekend_list[i-1]).days == 7:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 1

    return max_streak >= required_weekends

async def get_queries_in_timeframe(user_id: str, start_hour: int, end_hour: int) -> int:
    """Get the number of queries made by a user in a specific timeframe"""
    # Get all sessions for this user
    sessions_ref = db.collection("sessions").where("user_id", "==", user_id)
    sessions = sessions_ref.get()

    query_count = 0

    for session in sessions:
        session_data = session.to_dict()
        queries = session_data.get("queries", [])

        for query in queries:
            timestamp = query.get("timestamp")
            if timestamp:
                # Check if it's a Firestore Timestamp object
                if hasattr(timestamp, 'seconds') and hasattr(timestamp, 'nanoseconds'):
                    query_time = timestamp.datetime
                else:
                    try:
                        query_time = datetime.fromisoformat(timestamp)
                    except (ValueError, TypeError):
                        continue

                # Check if the query was made in the specified timeframe
                if start_hour <= query_time.hour < end_hour:
                    query_count += 1

    return query_count

async def award_xp_bonus(user_id: str, xp_amount: int, reason: str) -> None:
    """Award XP bonus to a user"""
    user_ref = db.collection("users").document(user_id)

    # Update XP and lifetime_xp
    user_ref.update({
        "xp": admin_firestore.Increment(xp_amount),
        "lifetime_xp": admin_firestore.Increment(xp_amount),  # Also increment lifetime XP
        "updated_at": admin_firestore.SERVER_TIMESTAMP
    })

    # Log XP bonus as a subcollection under the user
    db.collection("users").document(user_id).collection("xp_logs").add({
        "amount": xp_amount,
        "reason": reason,
        "timestamp": safe_timestamp(),  # Use safe_timestamp for nested documents
        "type": "bonus",
        "transaction_type": "credit",  # XP is being credited (added)
        "is_credit": True  # Explicitly mark as a credit transaction
    })

    logger.info(f"Awarded {xp_amount} XP to user {user_id} for {reason}")
