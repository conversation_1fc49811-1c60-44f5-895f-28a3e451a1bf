from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import resend
import os
import logging
from auth.deps import verify_firebase_token, require_role
from firebase.config import get_db
from firebase_admin import auth as firebase_auth
from google.cloud import firestore

# Configure logging
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")

# Initialize Firestore
db = get_db()

router = APIRouter()

class EmailRequest(BaseModel):
    """Model for email request data"""
    from_email: str = "AdMesh <<EMAIL>>"
    to: List[str]
    subject: str
    html: str

class AudienceCreateRequest(BaseModel):
    """Model for creating an audience"""
    name: str
    description: Optional[str] = None

class AudienceAddUserRequest(BaseModel):
    """Model for adding a user to an audience"""
    audience_id: str
    email: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

class BroadcastRequest(BaseModel):
    """Model for sending a broadcast email"""
    audience_id: str
    from_email: str = "AdMesh <<EMAIL>>"
    subject: str
    html: str
    text: Optional[str] = None

@router.post("/send")
async def send_email(
    email_data: EmailRequest,
    decoded_token = Depends(verify_firebase_token)
):
    """
    Send an email using Resend API
    """
    try:
        # Prepare email payload
        params = {
            "from": email_data.from_email,
            "to": email_data.to,
            "subject": email_data.subject,
            "html": email_data.html,
        }

        # Send email using Resend
        email = resend.Emails.send(params)

        logger.info(f"Email sent successfully to {email_data.to}")
        return email

    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send email: {str(e)}")



@router.post("/audience/create")
async def create_audience(
    audience_data: AudienceCreateRequest,
    user = Depends(require_role("admin"))
):
    """
    Create a new audience in Resend
    """
    try:
        # Create audience in Resend
        params = {
            "name": audience_data.name
        }

        if audience_data.description:
            params["description"] = audience_data.description

        audience = resend.Audiences.create(params)



        logger.info(f"Audience created: {audience['id']}")
        return audience

    except Exception as e:
        logger.error(f"Error creating audience: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create audience: {str(e)}")

@router.get("/audience/list")
async def list_audiences(
    user = Depends(require_role("admin"))
):
    """
    List all audiences in Resend
    """
    try:
        audiences = resend.Audiences.list()
        return audiences

    except Exception as e:
        logger.error(f"Error listing audiences: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list audiences: {str(e)}")

@router.post("/audience/add-user")
async def add_user_to_audience(
    user_data: AudienceAddUserRequest,
    user = Depends(require_role("admin"))
):
    """
    Add a user to an audience in Resend
    """
    try:
        # Prepare user data
        contact_data = {
            "email": user_data.email,
        }

        if user_data.first_name:
            contact_data["first_name"] = user_data.first_name

        if user_data.last_name:
            contact_data["last_name"] = user_data.last_name

        if user_data.data:
            contact_data["data"] = user_data.data

        # Add user to audience
        contact = resend.Contacts.create(user_data.audience_id, contact_data)

        logger.info(f"User {user_data.email} added to audience {user_data.audience_id}")
        return contact

    except Exception as e:
        logger.error(f"Error adding user to audience: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add user to audience: {str(e)}")

async def _get_all_users():
    """
    Get all registered users from Firebase
    """
    try:
        # Get all users from Firestore
        users_ref = db.collection("users").stream()
        users = []

        for user_doc in users_ref:
            user_data = user_doc.to_dict()
            if "email" in user_data:
                user_info = {
                    "email": user_data["email"],
                    "data": {
                        "uid": user_data["uid"],
                        "role": user_data.get("role", "user")
                    }
                }

                # Add name if available
                if "name" in user_data:
                    name_parts = user_data["name"].split(" ", 1)
                    user_info["first_name"] = name_parts[0]
                    if len(name_parts) > 1:
                        user_info["last_name"] = name_parts[1]

                # Add XP if available
                if "xp" in user_data:
                    user_info["data"]["xp"] = user_data["xp"]

                users.append(user_info)

        return users

    except Exception as e:
        logger.error(f"Error getting users: {str(e)}")
        raise e

async def _add_users_to_audience(audience_id: str, users: List[Dict[str, Any]]):
    """
    Add multiple users to an audience in Resend
    """
    try:
        added_count = 0
        failed_count = 0

        for user in users:
            try:
                resend.Contacts.create(audience_id, user)
                added_count += 1
            except Exception as e:
                logger.error(f"Error adding user {user['email']} to audience: {str(e)}")
                failed_count += 1

        return {
            "added": added_count,
            "failed": failed_count,
            "total": len(users)
        }

    except Exception as e:
        logger.error(f"Error adding users to audience: {str(e)}")
        raise e

@router.post("/audience/add-all-users")
async def add_all_users_to_audience(
    audience_id: str,
    background_tasks: BackgroundTasks,
    user = Depends(require_role("admin"))
):
    """
    Add all registered users to an audience in Resend
    This runs as a background task to avoid timeout
    """
    try:
        # Start the background task
        background_tasks.add_task(_add_all_users_to_audience_task, audience_id)

        return {
            "status": "started",
            "message": "Adding all users to audience in the background"
        }

    except Exception as e:
        logger.error(f"Error starting add all users task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start task: {str(e)}")

async def _add_all_users_to_audience_task(audience_id: str):
    """
    Background task to add all users to an audience
    """
    try:
        # Get all users
        users = await _get_all_users()

        # Add users to audience
        result = await _add_users_to_audience(audience_id, users)

        # Log the result
        db.collection("email_tasks").add({
            "task": "add_all_users",
            "audience_id": audience_id,
            "result": result,
            "completed_at": db.field_value.server_timestamp()
        })

        logger.info(f"Added {result['added']} users to audience {audience_id}")

    except Exception as e:
        logger.error(f"Error in add all users task: {str(e)}")
        db.collection("email_tasks").add({
            "task": "add_all_users",
            "audience_id": audience_id,
            "error": str(e),
            "completed_at": db.field_value.server_timestamp()
        })

@router.post("/broadcast/send")
async def send_broadcast(
    broadcast_data: BroadcastRequest,
    user = Depends(require_role("admin"))
):
    """
    Send a broadcast email to an audience
    """
    try:
        # Prepare broadcast data
        params = {
            "audience_id": broadcast_data.audience_id,
            "from": broadcast_data.from_email,
            "subject": broadcast_data.subject,
            "html": broadcast_data.html
        }

        if broadcast_data.text:
            params["text"] = broadcast_data.text

        # Send broadcast
        broadcast = resend.Broadcasts.create(params)

        # Log the broadcast
        db.collection("email_broadcasts").add({
            "broadcast_id": broadcast["id"],
            "audience_id": broadcast_data.audience_id,
            "subject": broadcast_data.subject,
            "sent_by": user["uid"],
            "sent_at": db.field_value.server_timestamp()
        })

        logger.info(f"Broadcast sent to audience {broadcast_data.audience_id}")
        return broadcast

    except Exception as e:
        logger.error(f"Error sending broadcast: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send broadcast: {str(e)}")

# Webhook to add new users to audience when they sign up
@router.post("/webhook/user-signup")
async def user_signup_webhook(
    user_id: str,
    background_tasks: BackgroundTasks,
    audience_name: str = "Registered Users"
):
    """
    Webhook to add a new user to an audience when they sign up
    This should be called from the user registration endpoint
    """
    try:
        # Start the background task
        background_tasks.add_task(_add_new_user_to_audience, user_id, audience_name)

        return {
            "status": "started",
            "message": f"Adding user {user_id} to audience {audience_name} in the background"
        }

    except Exception as e:
        logger.error(f"Error starting add new user task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start task: {str(e)}")

async def _add_new_user_to_audience(user_id: str, audience_name: str):
    """
    Background task to add a new user to an audience
    """
    try:
        # Get user from Firestore
        user_doc = db.collection("users").document(user_id).get()

        if not user_doc.exists:
            logger.error(f"User {user_id} not found")
            return

        user_data = user_doc.to_dict()

        if "email" not in user_data:
            logger.error(f"User {user_id} has no email")
            return

        # Prepare user data
        contact_data = {
            "email": user_data["email"],
            "data": {
                "uid": user_data["uid"],
                "role": user_data.get("role", "user")
            }
        }

        # Add name if available
        if "name" in user_data:
            name_parts = user_data["name"].split(" ", 1)
            contact_data["first_name"] = name_parts[0]
            if len(name_parts) > 1:
                contact_data["last_name"] = name_parts[1]



        # Get audience ID from Resend
        try:
            audiences = resend.Audiences.list()
            audience_id = None

            # Find the audience by name
            for audience in audiences:
                if isinstance(audience, dict) and "name" in audience and audience["name"].lower() == audience_name.lower():
                    audience_id = audience["id"]
                    break

            if not audience_id:
                # Create the audience if it doesn't exist
                params = {
                    "name": audience_name,  # Keep the exact case of the audience name
                    "description": f"Auto-created audience for {audience_name}"
                }
                new_audience = resend.Audiences.create(params)
                audience_id = new_audience["id"]

            # Add user to audience
            resend.Contacts.create(audience_id, contact_data)
            logger.info(f"User {user_id} added to audience {audience_name} (ID: {audience_id})")
        except Exception as e:
            logger.error(f"Error with Resend API: {str(e)}")

    except Exception as e:
        logger.error(f"Error adding new user to audience: {str(e)}")

# Welcome email endpoint
@router.post("/send-welcome")
async def send_welcome_email(
    email: str,
    background_tasks: BackgroundTasks,
    first_name: str = None
):
    """
    Send a welcome email to a newly registered user
    """
    try:
        # Start the background task
        background_tasks.add_task(_send_welcome_email_task, email, first_name)

        return {
            "status": "started",
            "message": f"Sending welcome email to {email} in the background"
        }

    except Exception as e:
        logger.error(f"Error starting welcome email task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start task: {str(e)}")

async def _send_welcome_email_task(email: str, first_name: str = None):
    """
    Background task to send a welcome email
    """
    try:
        # Read the welcome email template
        template_path = os.path.join(os.path.dirname(__file__), '..', '..', 'templates', 'email_welcome.html')
        with open(template_path, 'r') as file:
            html_content = file.read()

        # Replace the first_name placeholder if provided
        if first_name:
            html_content = html_content.replace('{{ first_name | default("there") }}', first_name)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Welcome to AdMesh!",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Welcome email sent to {email}")

        # Log the email
        db.collection("email_logs").add({
            "type": "welcome",
            "email": email,
            "sent_at": firestore.SERVER_TIMESTAMP
        })

    except Exception as e:
        logger.error(f"Error sending welcome email: {str(e)}")
        db.collection("email_logs").add({
            "type": "welcome",
            "email": email,
            "error": str(e),
            "timestamp": firestore.SERVER_TIMESTAMP
        })

# Weekly leaderboard emails
@router.post("/send-weekly-leaderboard")
async def send_weekly_leaderboard_emails(
    background_tasks: BackgroundTasks,
    user = Depends(require_role("admin"))
):
    """
    Send weekly leaderboard position emails to all users
    This runs as a background task to avoid timeout
    """
    try:
        # Start the background task
        background_tasks.add_task(_send_weekly_leaderboard_task)

        return {
            "status": "started",
            "message": "Sending weekly leaderboard emails in the background"
        }

    except Exception as e:
        logger.error(f"Error starting weekly leaderboard task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start task: {str(e)}")

async def _send_weekly_leaderboard_task():
    """
    Background task to send weekly leaderboard emails
    """
    try:
        # Get top users by XP
        users_ref = db.collection("users").order_by("xp", direction="DESCENDING").limit(100).stream()

        users = []
        for i, user_doc in enumerate(users_ref):
            user_data = user_doc.to_dict()
            if "email" in user_data:
                users.append({
                    "position": i + 1,
                    "uid": user_data["uid"],
                    "email": user_data["email"],
                    "name": user_data.get("name", "AdMesh User"),
                    "xp": user_data.get("xp", 0)
                })

        # Send emails to each user
        sent_count = 0
        for user in users:
            try:
                # Create email content
                html_content = f"""
                <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                            <h1 style="color: #000; text-align: center;">Your Weekly AdMesh Update</h1>
                            <p>Hello {user["name"]},</p>
                            <p>Here's your weekly AdMesh leaderboard update:</p>

                            <div style="background-color: #f9f9f9; border-radius: 5px; padding: 20px; margin: 20px 0; text-align: center;">
                                <h2 style="margin: 0; color: #000;">Your Position: #{user["position"]}</h2>
                                <p style="margin: 10px 0 0;">with {user["xp"]} XP</p>
                            </div>

                            <p>Keep up the good work and climb the leaderboard!</p>

                            <div style="margin: 30px 0; text-align: center;">
                                <a href="https://useadmesh.com/dashboard/leaderboard" style="background-color: #000; color: #fff; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Leaderboard</a>
                            </div>

                            <p>The AdMesh Team</p>

                            <div style="margin-top: 40px; font-size: 12px; color: #999; text-align: center;">
                                <p>You're receiving this email because you're a member of AdMesh.</p>
                                <p>You can unsubscribe from these emails by clicking <a href="{{{{ RESEND_UNSUBSCRIBE_URL }}}}" style="color: #999;">here</a>.</p>
                            </div>
                        </div>
                    </body>
                </html>
                """

                # Prepare email payload
                params = {
                    "from": "AdMesh <<EMAIL>>",
                    "to": [user["email"]],
                    "subject": "Your Weekly AdMesh Leaderboard Position",
                    "html": html_content,
                }

                # Send email
                resend.Emails.send(params)
                sent_count += 1

            except Exception as e:
                logger.error(f"Error sending leaderboard email to {user['email']}: {str(e)}")

        # Log the task execution
        db.collection("scheduled_tasks").add({
            "task": "weekly_leaderboard_emails",
            "sent_count": sent_count,
            "total_users": len(users),
            "completed_at": db.field_value.server_timestamp()
        })

        logger.info(f"Weekly leaderboard emails sent to {sent_count} users")

    except Exception as e:
        logger.error(f"Error in weekly leaderboard task: {str(e)}")
        db.collection("scheduled_tasks").add({
            "task": "weekly_leaderboard_emails",
            "error": str(e),
            "completed_at": db.field_value.server_timestamp()
        })
