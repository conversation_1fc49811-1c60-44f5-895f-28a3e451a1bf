from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Optional
import logging
import os
from auth.deps import verify_firebase_token
from api.agents.signup_agent import signup_agent
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

# Ensure screenshots directory exists
os.makedirs("screenshots", exist_ok=True)

class TrySignupRequest(BaseModel):
    url: str
    user_id: str
    session_id: Optional[str] = None

class SubmitInputsRequest(BaseModel):
    url: str
    inputs: Dict[str, str]
    user_id: str
    session_id: Optional[str] = None

class AgentResponse(BaseModel):
    status: str
    message: Optional[str] = None
    fields: Optional[List[str]] = None

class SignupAttempt(BaseModel):
    """Model to track signup attempts in the database"""
    user_id: str
    url: str
    timestamp: str
    status: str
    session_id: Optional[str] = None
    fields: Optional[List[str]] = None
    message: Optional[str] = None
    screenshot_path: Optional[str] = None

# In-memory store for tracking attempts (replace with Firestore in production)
signup_attempts = []

def log_signup_attempt(attempt: SignupAttempt):
    """Log a signup attempt to the in-memory store"""
    signup_attempts.append(attempt)
    logger.info(f"Logged signup attempt: {attempt.user_id} - {attempt.url} - {attempt.status}")

    # In a real implementation, you would save this to Firestore
    # db.collection("signup_attempts").add(attempt.dict())

@router.post("/agent/try-signup", response_model=AgentResponse)
async def try_signup(
    request: TrySignupRequest,
    background_tasks: BackgroundTasks,
    decoded_token = Depends(verify_firebase_token)
):
    """
    Attempt to sign up for a service using CrewAI agent.
    Uses Playwright to analyze the signup page and determine required fields.
    """
    try:
        user_id = decoded_token["uid"]

        # Log the request
        logger.info(f"Agent try-signup request for URL: {request.url} by user: {user_id}")

        # Use the CrewAI agent to detect signup fields
        result = await signup_agent.detect_signup_fields(request.url)

        # Log the attempt
        attempt = SignupAttempt(
            user_id=user_id,
            url=request.url,
            timestamp=datetime.now().isoformat(),
            status=result.status,
            session_id=request.session_id,
            fields=result.fields,
            message=result.message,
            screenshot_path=result.screenshot_path
        )

        background_tasks.add_task(log_signup_attempt, attempt)

        # Return the response
        return AgentResponse(
            status=result.status,
            message=result.message,
            fields=result.fields
        )

    except Exception as e:
        logger.exception(f"Error in agent try-signup: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/agent/submit-inputs", response_model=AgentResponse)
async def submit_inputs(
    request: SubmitInputsRequest,
    background_tasks: BackgroundTasks,
    decoded_token = Depends(verify_firebase_token)
):
    """
    Submit user inputs for agent to complete signup.
    Uses Playwright to fill in the form and submit it.
    """
    try:
        user_id = decoded_token["uid"]

        # Log the request (without sensitive data)
        logger.info(f"Agent submit-inputs for URL: {request.url} by user: {user_id}")
        logger.info(f"Input fields provided: {list(request.inputs.keys())}")

        # Use the CrewAI agent to submit the form
        result = await signup_agent.submit_signup_form(request.url, request.inputs)

        # Log the attempt
        attempt = SignupAttempt(
            user_id=user_id,
            url=request.url,
            timestamp=datetime.now().isoformat(),
            status=result.status,
            session_id=request.session_id,
            message=result.message,
            screenshot_path=result.screenshot_path
        )

        background_tasks.add_task(log_signup_attempt, attempt)

        # Return the response
        return AgentResponse(
            status=result.status,
            message=result.message
        )

    except Exception as e:
        logger.exception(f"Error in agent submit-inputs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/agent/signup-attempts/{user_id}", response_model=List[SignupAttempt])
async def get_signup_attempts(
    user_id: str,
    decoded_token = Depends(verify_firebase_token)
):
    """
    Get all signup attempts for a user.
    """
    try:
        # Verify that the user is requesting their own attempts
        token_user_id = decoded_token["uid"]
        if token_user_id != user_id:
            raise HTTPException(status_code=403, detail="You can only view your own signup attempts")

        # Filter attempts by user_id
        user_attempts = [attempt for attempt in signup_attempts if attempt.user_id == user_id]

        return user_attempts

    except Exception as e:
        logger.exception(f"Error getting signup attempts: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
