from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import Response
from datetime import timezone, datetime
import logging
from google.cloud import firestore
from firebase.config import get_db
from auth.deps import verify_firebase_token
from .earnings import record_agent_earning_from_conversion
from pydantic import BaseModel
from api.models.promo import PromoCredit

logger = logging.getLogger(__name__)

router = APIRouter()
db = get_db()

class ConversionEvent(BaseModel):
    ad_id: str
    event_type: str
    conversion_value: int
    currency: str
    timestamp: datetime

    # Optional metadata (you will attach these later)
    product_id: str | None = None
    offer_id: str | None = None
    agent_id: str | None = None
    user_id: str | None = None
    session_id: str | None = None
    recommendation_id: str | None = None
    reward_split: dict | None = None
    is_test: bool | None = None
    conversion_id: str | None = None

async def get_agent_subscription_revenue_share(agent_id: str) -> int:
    """Get agent revenue share percentage based on subscription"""
    if not agent_id:
        return 60  # Default for free agents

    try:
        # Check if agent has a subscription
        agent_doc = db.collection("agents").document(agent_id).get()
        if not agent_doc.exists:
            logger.warning(f"Agent not found: {agent_id}")
            raise ValueError("Agent ID is required")

        agent_data = agent_doc.to_dict()
        subscription = agent_data.get("subscription")

        if not subscription:
            logger.info(f"Agent {agent_id} has no subscription, using default 60% revenue share")
            return 60  # Default for free agents

        # Get revenue share based on subscription plan
        plan_id = subscription.get("plan_id", "free")

        # Revenue share mapping based on agent subscription plans
        revenue_share_map = {
            "free": 60,
            "pro": 70,
            "enterprise": 80
        }

        revenue_share = revenue_share_map.get(plan_id, 60)
        logger.info(f"Agent {agent_id} has {plan_id} subscription, revenue share: {revenue_share}%")
        return revenue_share

    except Exception as e:
        logger.warning(f"Error getting agent subscription for {agent_id}: {str(e)}")
        return 60  # Default for free agents


async def calculate_dynamic_reward_split(agent_id: str, user_id: str) -> dict:
    """Calculate reward split based on agent subscription and user presence"""

    # Get agent revenue share percentage
    agent_revenue_share = await get_agent_subscription_revenue_share(agent_id)

    # Fixed user share is 10%
    user_percentage = 10

    # Calculate shares
    if user_id:
        # User gets 10%, agent gets their percentage, admesh gets the rest
        user_share = user_percentage
        agent_share = agent_revenue_share
        admesh_share = 100 - agent_revenue_share - user_percentage

        # Ensure admesh share is not negative
        if admesh_share < 0:
            admesh_share = 0
            agent_share = 100 - user_percentage
    else:
        # No user, so user share (10%) goes to admesh
        user_share = 0
        agent_share = agent_revenue_share
        admesh_share = 100 - agent_revenue_share  # This will be 40% for free agents (100-60), 30% for pro (100-70), etc.

    reward_split = {
        "agent": agent_share,
        "user": user_share,
        "admesh": admesh_share
    }

    logger.info(f"Dynamic reward split for agent {agent_id}, user {user_id}: {reward_split}")
    return reward_split


def calculate_reward_distribution(conversion_value: int, reward_split: dict) -> dict:
    total_shares = sum(reward_split.values())
    if total_shares == 0:
        raise ValueError("Reward split total cannot be 0")

    distribution = {
        role: round(conversion_value * share / total_shares)
        for role, share in reward_split.items()
    }

    discrepancy = conversion_value - sum(distribution.values())
    if discrepancy != 0:
        target = "admesh" if "admesh" in distribution else max(distribution, key=distribution.get)
        distribution[target] += discrepancy

    return distribution



async def get_click_metadata(click_id: str) -> dict:
    doc = db.collection("clicks").document(click_id).get()
    if not doc.exists:
        raise ValueError("Click ID not found")
    return doc.to_dict()


async def check_promo_credit_eligibility(brand_id: str, is_test: bool = False) -> bool:
    """
    Check if a brand is eligible for promo credit conversion

    Args:
        brand_id: The ID of the brand
        is_test: Whether this is a test conversion

    Returns:
        bool: True if eligible, False otherwise
    """
    # Test conversions are always allowed
    if is_test:
        return True

    # Get brand data
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        logger.error(f"Brand not found: {brand_id}")
        return False

    brand_data = brand_doc.to_dict()

    # Check if brand has promo credit
    promo = brand_data.get("promo", {})
    if not promo:
        logger.info(f"Brand has no promo credit: {brand_id}")
        return True  # No promo credit restrictions

    # Check if promo credit is eligible for conversion
    if not PromoCredit.is_eligible_for_conversion(promo):
        logger.info(f"Brand promo credit not eligible: {brand_id}, used={promo.get('used_conversions', 0)}, max={promo.get('max_conversions', 0)}")
        return False

    # Update used conversions count
    promo = PromoCredit.increment_used_conversions(promo)

    # Update brand document with incremented used_conversions
    brand_ref.update({
        "promo.used_conversions": promo.get("used_conversions", 1),
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    logger.info(f"Brand promo credit used: {brand_id}, used={promo.get('used_conversions', 0)}, max={promo.get('max_conversions', 0)}")
    return True


@router.get("/pixel")
async def conversion_pixel(
    request: Request,
    utm_click_id: str = None,
    test: bool = None,  # Make test parameter truly optional
    verified: bool = None  # Make verified parameter truly optional
):
    try:
        # Log all query parameters for debugging
        logger.info(f"Conversion pixel request received with query params: {dict(request.query_params)}")
        logger.info(f"Function parameters: utm_click_id={utm_click_id}, test={test}, verified={verified}")

        # Use utm_click_id parameter
        effective_click_id = utm_click_id

        # Check if utm_click_id is in query params if not provided as a parameter
        if not effective_click_id and 'utm_click_id' in request.query_params:
            effective_click_id = request.query_params.get('utm_click_id')
            logger.info(f"Using utm_click_id from query params: {effective_click_id}")

        # Log the effective click ID
        logger.info(f"Effective click ID: {effective_click_id}")

        if not effective_click_id:
            logger.error("No utm_click_id found in request")
            raise HTTPException(400, detail="utm_click_id is required")

        # Handle test parameter - check both function parameter and query params
        is_test = False  # Default value
        if test is not None:  # If function parameter is provided
            is_test = test
        elif 'test' in request.query_params:  # Otherwise check query params
            test_param = request.query_params.get('test', '').lower()
            is_test = test_param == 'true' or test_param == '1'

        # Handle verified parameter - check both function parameter and query params
        is_verified = False  # Default value
        if verified is not None:  # If function parameter is provided
            is_verified = verified
        elif 'verified' in request.query_params:  # Otherwise check query params
            verified_param = request.query_params.get('verified', '').lower()
            is_verified = verified_param == 'true' or verified_param == '1'

        logger.info(f"Final parameter values: is_test={is_test}, is_verified={is_verified}")

        # 1. 🔍 Get all metadata from click document
        click_doc = db.collection("clicks").document(effective_click_id).get()
        # Initialize click data
        click = None

        if not click_doc.exists:
            # For test conversions, create a dummy click document
            if is_test:
                logger.info(f"Creating dummy test click for {effective_click_id}")
                # Use default values for a test click
                click = {
                    "ad_id": request.query_params.get('ad_id', effective_click_id),
                    "offer_id": request.query_params.get('offer_id', request.query_params.get('ad_id', effective_click_id)),
                    "product_id": request.query_params.get('product_id', "test_product"),
                    "agent_id": "test_agent",
                    "user_id": "test_user",
                    "session_id": "test_session",
                    "recommendation_id": "test_recommendation",
                    "is_test": True,
                    "source": "test",
                    "timestamp": firestore.SERVER_TIMESTAMP
                }
                # Store the click for future reference
                db.collection("clicks").document(effective_click_id).set(click)
            else:
                # For non-test conversions, require a valid click ID
                logger.warning(f"Click ID not found: {effective_click_id}")
                raise HTTPException(404, detail="utm_click_id not found. Please ensure a valid click ID is provided.")
        else:
            # Get click data from the document
            click = click_doc.to_dict()

        ad_id = click.get("ad_id")
        offer_id = click.get("offer_id") or ad_id
        product_id = click.get("product_id")
        agent_id = click.get("agent_id")
        user_id = click.get("user_id")
        session_id = click.get("session_id")
        recommendation_id = click.get("recommendation_id")

        # 2. 🧾 Get offer details (payout + reward split)
        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        # Initialize offer data
        offer = None

        if not offer_doc.exists:
            # For test conversions, create a dummy offer
            if is_test:
                logger.info(f"Creating dummy test offer for {offer_id}")
                # Use default values for a test offer
                offer = {
                    "payout": {"amount": 1000, "currency": "USD"},
                    "reward_split": {"agent": 6, "user": 1, "admesh": 3},
                    "brand_id": "test_brand",
                    "title": "Test Offer",
                    "is_test": True
                }
                # Store the offer for future reference
                db.collection("offers").document(offer_id).set(offer)
            else:
                # For non-test conversions, require a valid offer ID
                logger.warning(f"Offer not found: {offer_id}")
                raise HTTPException(404, detail="Offer not found. Please ensure a valid offer ID is provided.")
        else:
            # Get offer data from the document
            offer = offer_doc.to_dict()
        # Get entire payout object
        payout_obj = offer.get("payout", {"amount": 1000, "currency": "USD"})
        payout = payout_obj.get("amount", 1000)

        # Calculate dynamic reward split based on agent subscription and user presence
        reward_split = await calculate_dynamic_reward_split(agent_id, user_id)
        reward_distribution = calculate_reward_distribution(payout, reward_split)

        # 3. 🧠 Check promo credit eligibility for the brand
        brand_id = offer.get("brand_id")
        if not is_test and brand_id:
            # Check if brand is eligible for promo credit conversion
            is_eligible = await check_promo_credit_eligibility(brand_id, is_test)
            if not is_eligible:
                logger.warning(f"Brand {brand_id} has reached promo credit conversion limit")
                # Return a special pixel that indicates the conversion was not logged
                # but don't raise an error to avoid breaking the pixel flow
                return Response(
                    content=bytes([
                        71, 73, 70, 56, 57, 97, 1, 0, 1, 0, 128, 0, 0, 255,
                        255, 255, 0, 0, 0, 33, 249, 4, 1, 0, 0, 1, 0, 44,
                        0, 0, 0, 0, 1, 0, 1, 0, 0, 2, 2, 68, 1, 0, 59
                    ]),
                    media_type="image/gif",
                    headers={"X-AdMesh-Status": "promo-limit-reached"}
                )

        # 4. 🧠 Build conversion data
        conversion_data = {
            "click_id": effective_click_id,
            "ad_id": ad_id,
            "offer_id": offer_id,
            "product_id": product_id,
            "agent_id": agent_id,
            "user_id": user_id,
            "session_id": session_id,
            "recommendation_id": recommendation_id,
            "event_type": "conversion",
            "conversion_value": payout,
            "currency": payout_obj.get("currency", "USD"),
            "verified": is_verified,
            "is_test": is_test,
            "source": "pixel" if not is_test else "test",
            "timestamp": firestore.SERVER_TIMESTAMP,
            "payout": payout_obj,  # Store entire payout object
            "reward_split": reward_split,
            "reward_distribution": reward_distribution,
            "referrer": request.headers.get("referer"),
            "ip_address": request.client.host,
            "user_agent": request.headers.get("user-agent"),
            "status": "verified" if is_verified else "pending"
        }

        # 5. 💾 Store conversion
        conversion_ref = db.collection("conversions").add(conversion_data)
        conversion_id = conversion_ref[1].id

        # Update click with conversion status and conversion_id
        if effective_click_id:
            try:
                click_ref = db.collection("clicks").document(effective_click_id)
                click_ref.update({
                    "status": "converted",
                    "conversion_id": conversion_id,
                    "converted_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated click {effective_click_id} with conversion status and ID {conversion_id}")
            except Exception as e:
                logger.warning(f"Failed to update click with conversion status: {str(e)}")

        # 6. 📊 Update offer + product metrics
        offer_update = {
            "last_converted_at": firestore.SERVER_TIMESTAMP
        }
        if isinstance(offer.get("conversion_count"), dict):
            key = "test" if is_test else "production"
            offer_update[f"conversion_count.{key}"] = firestore.Increment(1)
            offer_update["conversion_count.total"] = firestore.Increment(1)
        else:
            offer_update["conversion_count"] = firestore.Increment(1)
        offer_ref.update(offer_update)

        # 7. 💸 Record rewards
        enriched_event = ConversionEvent(
            ad_id=ad_id,
            event_type="conversion",
            conversion_value=payout,
            currency=payout_obj.get("currency", "USD"),
            timestamp=datetime.now(timezone.utc)
        )
        enriched_event.product_id = product_id
        enriched_event.offer_id = offer_id
        enriched_event.agent_id = agent_id
        enriched_event.user_id = user_id
        enriched_event.session_id = session_id
        enriched_event.recommendation_id = recommendation_id
        enriched_event.reward_split = reward_split
        enriched_event.is_test = is_test
        enriched_event.conversion_id = conversion_id

        await record_agent_earning_from_conversion(enriched_event, click_id=effective_click_id)

        # 8. ✅ Return tracking pixel
        return Response(
            content=bytes([
                71, 73, 70, 56, 57, 97, 1, 0, 1, 0, 128, 0, 0, 255,
                255, 255, 0, 0, 0, 33, 249, 4, 1, 0, 0, 1, 0, 44,
                0, 0, 0, 0, 1, 0, 1, 0, 0, 2, 2, 68, 1, 0, 59
            ]),
            media_type="image/gif"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception("🔥 Error in conversion pixel")
        raise HTTPException(status_code=500, detail="Pixel logging failed")


class DeleteTestConversionsRequest(BaseModel):
    offer_id: str
    delete_clicks: bool = False  # Option to also delete test clicks


class DeleteSingleTestConversionRequest(BaseModel):
    conversion_id: str
    offer_id: str


class ConversionLogRequest(BaseModel):
    offer_id: str
    ad_id: str = None
    utm_click_id: str = None
    event_type: str = "conversion"
    conversion_value: int = 1000  # Default to $10.00 (1000 cents)
    currency: str = "USD"
    source: str = None
    is_test: bool = None  # Make test parameter truly optional
    agent_id: str = None
    user_id: str = None
    session_id: str = None
    product_id: str = None


@router.post("/log")
async def log_conversion(request: ConversionLogRequest):
    """Log a conversion from a server-side API call"""
    try:
        # Extract data from request
        offer_id = request.offer_id
        ad_id = request.ad_id or offer_id
        is_test = False if request.is_test is None else request.is_test  # Default to False if None

        # Get offer details
        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            if is_test:
                # For test conversions, create a dummy offer
                logger.info(f"Creating dummy test offer for {offer_id}")
                offer = {
                    "payout": {"amount": request.conversion_value, "currency": request.currency},
                    "reward_split": {"agent": 5, "user": 2, "admesh": 3},
                    "brand_id": "test_brand",
                    "title": "Test Offer",
                    "is_test": True
                }
                # Store the offer for future reference
                offer_ref.set(offer)
            else:
                # For non-test conversions, require a valid offer ID
                logger.warning(f"Offer not found: {offer_id}")
                raise HTTPException(status_code=404, detail="Offer not found. Please ensure a valid offer ID is provided.")
        else:
            # Get offer data from the document
            offer = offer_doc.to_dict()

        # Get brand ID from offer
        brand_id = offer.get("brand_id")

        # Check promo credit eligibility for the brand
        if not is_test and brand_id:
            # Check if brand is eligible for promo credit conversion
            is_eligible = await check_promo_credit_eligibility(brand_id, is_test)
            if not is_eligible:
                logger.warning(f"Brand {brand_id} has reached promo credit conversion limit")
                return {
                    "status": "error",
                    "message": "Brand has reached promo credit conversion limit",
                    "code": "promo_limit_reached"
                }

        # Get entire payout object and pass to agent
        payout_obj = offer.get("payout", {"amount": request.conversion_value, "currency": request.currency, "model": "CPA"})
        payout = payout_obj.get("amount", request.conversion_value)

        # Calculate dynamic reward split based on agent subscription and user presence
        reward_split = await calculate_dynamic_reward_split(request.agent_id, request.user_id)
        reward_distribution = calculate_reward_distribution(payout, reward_split)

        # Build conversion data
        conversion_data = {
            "ad_id": ad_id,
            "offer_id": offer_id,
            "product_id": request.product_id,
            "agent_id": request.agent_id,
            "user_id": request.user_id,
            "session_id": request.session_id,
            "event_type": request.event_type,
            "conversion_value": payout,
            "currency": payout_obj.get("currency", request.currency),
            "verified": True,  # Server-side conversions are considered verified
            "is_test": is_test,
            "source": request.source or ("test" if is_test else "api"),
            "timestamp": firestore.SERVER_TIMESTAMP,
            "payout": payout_obj,  # Store entire payout object
            "reward_split": reward_split,
            "reward_distribution": reward_distribution,
            "status": "verified"
        }

        # If utm_click_id is provided, add it to the conversion data and get click details
        if request.utm_click_id:
            conversion_data["click_id"] = request.utm_click_id

            # Try to get click data
            try:
                click_doc = db.collection("clicks").document(request.utm_click_id).get()
                if click_doc.exists:
                    click_data = click_doc.to_dict()
                    # Fill in missing fields from click data
                    if not request.agent_id and "agent_id" in click_data:
                        conversion_data["agent_id"] = click_data["agent_id"]
                    if not request.user_id and "user_id" in click_data:
                        conversion_data["user_id"] = click_data["user_id"]
                    if not request.session_id and "session_id" in click_data:
                        conversion_data["session_id"] = click_data["session_id"]
                else:
                    # Don't create a dummy click - utm_click_id should be valid
                    logger.warning(f"Click ID not found: {request.utm_click_id}")
                    if not is_test:
                        # For non-test conversions, require a valid click ID
                        raise HTTPException(404, detail="utm_click_id not found. Please ensure a valid click ID is provided.")
                    else:
                        logger.warning("Test conversion with invalid utm_click_id. Proceeding anyway but this should be fixed.")
            except Exception as e:
                logger.warning(f"Error getting click data: {str(e)}")

        # Store conversion
        conversion_ref = db.collection("conversions").add(conversion_data)
        conversion_id = conversion_ref[1].id

        # Update click with conversion status, conversion_id, and timestamps if utm_click_id is provided
        if request.utm_click_id:
            try:
                click_ref = db.collection("clicks").document(request.utm_click_id)
                click_ref.update({
                    "status": "converted",
                    "conversion_id": conversion_id,
                    "converted_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated click {request.utm_click_id} with conversion status and ID {conversion_id}")
            except Exception as e:
                logger.warning(f"Failed to update click with conversion status: {str(e)}")

        # Update offer metrics
        offer_update = {
            "last_converted_at": firestore.SERVER_TIMESTAMP
        }
        if isinstance(offer.get("conversion_count"), dict):
            key = "test" if is_test else "production"
            offer_update[f"conversion_count.{key}"] = firestore.Increment(1)
            offer_update["conversion_count.total"] = firestore.Increment(1)
        else:
            offer_update["conversion_count"] = firestore.Increment(1)
        offer_ref.update(offer_update)

        # Record rewards if agent_id is provided
        if request.agent_id:
            try:
                enriched_event = ConversionEvent(
                    ad_id=ad_id,
                    event_type=request.event_type,
                    conversion_value=payout,
                    currency=payout_obj.get("currency", request.currency),
                    timestamp=datetime.now(timezone.utc)
                )
                enriched_event.product_id = request.product_id
                enriched_event.offer_id = offer_id
                enriched_event.agent_id = request.agent_id
                enriched_event.user_id = request.user_id
                enriched_event.session_id = request.session_id
                enriched_event.reward_split = reward_split
                enriched_event.is_test = is_test
                enriched_event.conversion_id = conversion_id

                await record_agent_earning_from_conversion(enriched_event, click_id=request.utm_click_id)
            except Exception as e:
                logger.warning(f"Error recording agent earnings: {str(e)}")

        return {
            "status": "success",
            "message": "Conversion logged successfully",
            "conversion_id": conversion_ref[1].id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"🔥 Error in conversion log: {str(e)}")
        raise HTTPException(status_code=500, detail="Conversion logging failed")


@router.get("/offers/{offer_id}/test")
async def test_conversion(offer_id: str, user=Depends(verify_firebase_token)):
    """
    Create a test conversion for a specific offer.

    Args:
        offer_id: The ID of the offer to create a test conversion for
    """
    try:
        # Get the offer to verify ownership
        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()

        # Verify that the user is the owner of the offer
        if offer_data.get("brand_id") != user["uid"]:
            raise HTTPException(status_code=403, detail="You don't have permission to create test conversions for this offer")

        # Create a test click
        click_id = f"test_{offer_id}_{datetime.now().timestamp()}"
        click_ref = db.collection("clicks").document(click_id)

        click_data = {
            "ad_id": offer_id,
            "offer_id": offer_id,
            "product_id": offer_data.get("product_id", "test_product"),
            "agent_id": "test_agent",
            "user_id": "test_user",
            "session_id": "test_session",
            "recommendation_id": "test_recommendation",
            "is_test": True,
            "source": "test",
            "timestamp": firestore.SERVER_TIMESTAMP
        }

        click_ref.set(click_data)

        # Get payout object and calculate dynamic reward split for test
        payout_obj = offer_data.get("payout", {"amount": 1000, "currency": "USD", "model": "CPA"})
        payout = payout_obj.get("amount", 1000)

        # Calculate dynamic reward split for test agent and user
        test_agent_id = "test_agent"
        test_user_id = "test_user"
        reward_split = await calculate_dynamic_reward_split(test_agent_id, test_user_id)
        reward_distribution = calculate_reward_distribution(payout, reward_split)

        # Create a test conversion
        conversion_ref = db.collection("conversions").add({
            "click_id": click_id,
            "ad_id": offer_id,
            "offer_id": offer_id,
            "product_id": offer_data.get("product_id", "test_product"),
            "agent_id": test_agent_id,
            "user_id": test_user_id,
            "session_id": "test_session",
            "recommendation_id": "test_recommendation",
            "is_test": True,
            "source": "test",
            "timestamp": firestore.SERVER_TIMESTAMP,
            "conversion_value": payout,
            "currency": payout_obj.get("currency", "USD"),
            "payout": payout_obj,  # Store entire payout object
            "reward_split": reward_split,
            "reward_distribution": reward_distribution,
            "verified": True
        })

        # Update the offer's test conversion count
        offer_ref.update({
            "conversion_count.test": firestore.Increment(1),
            "conversion_count.total": firestore.Increment(1)
        })

        return {
            "status": "success",
            "message": "Test conversion created successfully",
            "offer_id": offer_id,
            "conversion_id": conversion_ref[1].id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error creating test conversion: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create test conversion")


@router.get("/offers/{offer_id}/conversions/test")
async def check_test_conversions(offer_id: str, user=Depends(verify_firebase_token)):
    """
    Check if there are any test conversions for a specific offer.

    Args:
        offer_id: The ID of the offer to check

    Returns:
        JSON response with test conversion count
    """
    try:
        # Get the offer to verify ownership
        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()

        # Verify that the user is the owner of the offer
        if offer_data.get("brand_id") != user["uid"]:
            raise HTTPException(status_code=403, detail="You don't have permission to check test conversions for this offer")

        # Query for test conversions for this offer using is_test flag
        query1 = db.collection("conversions").where("ad_id", "==", offer_id).where("is_test", "==", True).stream()

        # Query for test conversions with "test" in the source field
        query2 = db.collection("conversions").where("ad_id", "==", offer_id).where("source", "==", "test").stream()

        # Query for test conversions with "pixel_test" in the source field
        query3 = db.collection("conversions").where("ad_id", "==", offer_id).where("source", "==", "pixel_test").stream()

        # Track unique conversion IDs to avoid duplicates
        conversion_ids = set()

        # Process results from all queries
        for doc in query1:
            conversion_ids.add(doc.id)

        for doc in query2:
            conversion_ids.add(doc.id)

        for doc in query3:
            conversion_ids.add(doc.id)

        # Count unique test conversions
        test_conversions_count = len(conversion_ids)

        # Return the count of test conversions
        return {
            "status": "success",
            "test_conversions": test_conversions_count,
            "offer_id": offer_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error checking test conversions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to check test conversions")


@router.post("/delete-tests")
async def delete_test_conversions(request: DeleteTestConversionsRequest, user=Depends(verify_firebase_token)):
    try:
        # Get the offer to verify ownership
        offer_ref = db.collection("offers").document(request.offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()

        # Verify that the user is the owner of the offer
        if offer_data.get("brand_id") != user["uid"]:
            raise HTTPException(status_code=403, detail="You don't have permission to delete test conversions for this offer")

        # Query for test conversions for this offer (using is_test flag)
        query1 = db.collection("conversions").where("ad_id", "==", request.offer_id).where("is_test", "==", True).stream()

        # Query for test conversions with "test" in the source field
        query2 = db.collection("conversions").where("ad_id", "==", request.offer_id).where("source", "==", "test").stream()

        # Query for test conversions with "pixel_test" in the source field
        query3 = db.collection("conversions").where("ad_id", "==", request.offer_id).where("source", "==", "pixel_test").stream()

        # Track deleted IDs to avoid duplicates
        deleted_ids = set()
        deleted_count = 0
        total_value = 0.0

        # Process all queries
        for query in [query1, query2, query3]:
            for doc in query:
                doc_id = doc.id
                # Skip if already deleted (could be in multiple query results)
                if doc_id in deleted_ids:
                    continue

                # Get conversion value for budget adjustment
                conversion_data = doc.to_dict()
                conversion_value = conversion_data.get("conversion_value", 0.0)
                total_value += conversion_value

                # Delete the conversion
                db.collection("conversions").document(doc_id).delete()
                deleted_ids.add(doc_id)
                deleted_count += 1

        # Update the offer metrics for conversions
        if deleted_count > 0:
            # Check if we have the detailed conversion count structure
            if isinstance(offer_data.get("conversion_count"), dict):
                offer_ref.update({
                    "conversion_count.test": firestore.Increment(-deleted_count),
                    "conversion_count.total": firestore.Increment(-deleted_count)
                })
            else:
                # Old structure - just decrement the total count
                offer_ref.update({
                    "conversion_count": firestore.Increment(-deleted_count)
                })

            # Check if we have the detailed spending structure
            if isinstance(offer_data.get("total_spent"), dict):
                offer_ref.update({
                    "total_spent.test": firestore.Increment(-total_value),
                    "total_spent.all": firestore.Increment(-total_value)
                })
            else:
                # Old structure - just decrement the total spent
                offer_ref.update({
                    "total_spent": firestore.Increment(-total_value)
                })

            # If there's a brand_id, update the brand metrics too
            # Note: We don't need to update brand budget metrics for test conversions
            # since they shouldn't have affected the budget in the first place
            # But we'll keep this for backward compatibility
            if offer_data.get("brand_id"):
                # Use the actual total value of deleted conversions
                db.collection("brands").document(offer_data["brand_id"]).update({
                    "budget_used": firestore.Increment(-total_value),
                    "budget_remaining": firestore.Increment(total_value)
                })

        # Handle test clicks deletion if requested
        deleted_clicks_count = 0
        if request.delete_clicks:
            # We need to check both ad_id and offer_id fields since clicks might use either
            # Query for test clicks using is_test flag with ad_id
            click_query1 = db.collection("clicks").where("ad_id", "==", request.offer_id).where("is_test", "==", True).stream()

            # Query for test clicks using is_test flag with offer_id
            click_query2 = db.collection("clicks").where("offer_id", "==", request.offer_id).where("is_test", "==", True).stream()

            # Query for test clicks with "test" in the source field with ad_id
            click_query3 = db.collection("clicks").where("ad_id", "==", request.offer_id).where("source", "==", "test").stream()

            # Query for test clicks with "test" in the source field with offer_id
            click_query4 = db.collection("clicks").where("offer_id", "==", request.offer_id).where("source", "==", "test").stream()

            # Comprehensive query to find all clicks that might be test clicks for this offer
            # This will catch any test clicks that might have been missed by the specific queries
            all_clicks_query = db.collection("clicks").stream()
            additional_test_clicks = []

            logger.info(f"Scanning all clicks for test clicks related to offer {request.offer_id}")
            for doc in all_clicks_query:
                doc_id = doc.id
                click_data = doc.to_dict()

                # Check if this click is related to our offer
                is_related_to_offer = (click_data.get("ad_id") == request.offer_id or
                                      click_data.get("offer_id") == request.offer_id)

                if not is_related_to_offer:
                    continue

                # Check if it's a test click by any of our criteria
                is_test_click = (
                    # Check the is_test flag
                    click_data.get("is_test") == True or
                    # Check the source field
                    click_data.get("source") == "test" or
                    (click_data.get("source") and "test" in click_data.get("source").lower()) or
                    # Check if the document ID starts with "test_"
                    doc_id.startswith("test_")
                )

                if is_test_click:
                    logger.info(f"Found additional test click: {doc_id}, related to offer {request.offer_id}")
                    additional_test_clicks.append(doc)

            # Track deleted click IDs to avoid duplicates
            deleted_click_ids = set()

            # Process all click queries
            for query in [click_query1, click_query2, click_query3, click_query4, additional_test_clicks]:
                for doc in query:
                    doc_id = doc.id
                    # Skip if already deleted (could be in multiple query results)
                    if doc_id in deleted_click_ids:
                        continue

                    # Get click data for logging
                    click_data = doc.to_dict()
                    logger.info(f"Deleting test click: {doc_id}, source: {click_data.get('source')}, is_test: {click_data.get('is_test')}, ad_id: {click_data.get('ad_id')}, offer_id: {click_data.get('offer_id')}")

                    # Delete the click
                    db.collection("clicks").document(doc_id).delete()
                    deleted_click_ids.add(doc_id)
                    deleted_clicks_count += 1

            # Update the offer metrics for clicks
            if deleted_clicks_count > 0:
                logger.info(f"Deleted {deleted_clicks_count} test clicks for offer {request.offer_id}")

                # Check if click_count exists in the offer data
                if "click_count" not in offer_data:
                    logger.info(f"Offer has no click_count field, skipping update")
                # Check if we have the detailed click count structure
                elif isinstance(offer_data.get("click_count"), dict):
                    logger.info(f"Updating detailed click count structure: {offer_data.get('click_count')}")
                    offer_ref.update({
                        "click_count.test": firestore.Increment(-deleted_clicks_count),
                        "click_count.total": firestore.Increment(-deleted_clicks_count)
                    })
                else:
                    logger.info(f"Offer has old click_count structure: {offer_data.get('click_count')}")
                    # We don't need to update the old structure since test clicks weren't counted there

        # Return success message with counts
        return {
            "status": "success",
            "message": f"Deleted {deleted_count} test conversions and {deleted_clicks_count} test clicks"
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete test conversions: {str(e)}")


@router.post("/delete-single-test")
async def delete_single_test_conversion(request: DeleteSingleTestConversionRequest, user=Depends(verify_firebase_token)):
    try:
        # Get the offer to verify ownership
        offer_ref = db.collection("offers").document(request.offer_id)
        offer_doc = offer_ref.get()

        if not offer_doc.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer_data = offer_doc.to_dict()

        # Verify that the user is the owner of the offer
        if offer_data.get("brand_id") != user["uid"]:
            raise HTTPException(status_code=403, detail="You don't have permission to delete test conversions for this offer")

        # Get the conversion document
        conversion_ref = db.collection("conversions").document(request.conversion_id)
        conversion_doc = conversion_ref.get()

        if not conversion_doc.exists:
            raise HTTPException(status_code=404, detail="Conversion not found")

        conversion_data = conversion_doc.to_dict()

        # Verify this is a test conversion
        is_test = conversion_data.get("is_test", False)
        source = conversion_data.get("source", "").lower()
        if not is_test and "test" not in source:
            raise HTTPException(status_code=400, detail="Can only delete test conversions")

        # Verify the conversion belongs to the offer
        if conversion_data.get("offer_id") != request.offer_id:
            raise HTTPException(status_code=400, detail="Conversion does not belong to this offer")

        # Get conversion value for budget adjustment
        conversion_value = conversion_data.get("conversion_value", 0.0)

        # Delete the conversion
        conversion_ref.delete()

        # Update the offer metrics
        # Check if we have the detailed conversion count structure
        if isinstance(offer_data.get("conversion_count"), dict):
            offer_ref.update({
                "conversion_count.test": firestore.Increment(-1),
                "conversion_count.total": firestore.Increment(-1)
            })
        else:
            # Old structure - just decrement the total count
            offer_ref.update({
                "conversion_count": firestore.Increment(-1)
            })

        # Check if we have the detailed spending structure
        if isinstance(offer_data.get("total_spent"), dict):
            offer_ref.update({
                "total_spent.test": firestore.Increment(-conversion_value),
                "total_spent.all": firestore.Increment(-conversion_value)
            })
        else:
            # Old structure - just decrement the total spent
            offer_ref.update({
                "total_spent": firestore.Increment(-conversion_value)
            })

        return {
            "status": "success",
            "message": f"Deleted test conversion {request.conversion_id}"
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete test conversion: {str(e)}")
