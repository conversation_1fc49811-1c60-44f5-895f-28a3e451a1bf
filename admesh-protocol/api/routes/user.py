from fastapi import APIRouter, Depends, HTTPException, Query, Body
from firebase.config import get_db
from firebase_admin import firestore as admin_firestore
import logging
import traceback
import time
from pydantic import BaseModel
from auth.deps import require_role, verify_firebase_token
from datetime import datetime, timezone
from typing import Optional
from api.services.badge_service import BadgeService

def safe_timestamp():
    """Return a safe timestamp for use in Firestore arrays and nested structures.
    Use this instead of SERVER_TIMESTAMP when adding timestamps to arrays or nested objects.
    """
    # Return a datetime object that Firestore can convert automatically
    return datetime.now(timezone.utc)

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

# Set up more detailed logging
logging.basicConfig(level=logging.INFO)
logger.setLevel(logging.INFO)
@router.get("/chat/history")
async def get_user_history(uid: str, limit: int = 10, offset: int = 0):
    sessions_ref = db.collection("sessions")
    products_ref = db.collection("products")
    clicks_ref = db.collection("clicks")
    conversions_ref = db.collection("conversions")

    try:
        sessions_query = sessions_ref.where(
            "user_id", "==", uid
        ).order_by("created_at", direction=admin_firestore.Query.DESCENDING)

        all_sessions = list(sessions_query.stream())
        paginated_sessions = all_sessions[offset:offset + limit]

        history = []

        for doc in paginated_sessions:
            data = doc.to_dict()
            session_id = doc.id
            product_ids = data.get("product_ids", [])
            query_list = data.get("queries", [])
            # Calculate followup count for reference
            _ = len(query_list) - 1

            # Fetch clicks and conversions for this session
            user_clicks = clicks_ref.where("user_id", "==", uid).where("session_id", "==", session_id).stream()
            user_conversions = conversions_ref.where("user_id", "==", uid).where("session_id", "==", session_id).stream()

            # Collect clicked and converted product IDs
            clicked_by_pid = {}
            for c in user_clicks:
                c_data = c.to_dict()
                pid = c_data.get("product_id")
                clicked_by_pid[pid] = {
                    "click_type": c_data.get("click_type", "initial"),
                    "converted": False
                }

            for c in user_conversions:
                c_data = c.to_dict()
                pid = c_data.get("product_id")
                if pid in clicked_by_pid:
                    clicked_by_pid[pid]["converted"] = True
                else:
                    clicked_by_pid[pid] = {
                        "click_type": "initial",
                        "converted": True
                    }

            clicked_products = []
            for pid in product_ids:
                if pid not in clicked_by_pid:
                    continue
                product_doc = products_ref.document(pid).get()
                if not product_doc.exists:
                    continue
                p_data = product_doc.to_dict()
                clicked_products.append({
                    "title": p_data.get("title"),
                    "url": p_data.get("url"),
                    "converted": clicked_by_pid[pid]["converted"],
                    "click_type": clicked_by_pid[pid]["click_type"]
                })

            history.append({
                "id": session_id,
                "query": data.get("initial_query", ""),
                "createdAt": data.get("created_at", ""),
                "queries": query_list,
                "clickedProducts": clicked_products
            })

        return {
            "items": history,
            "total": len(all_sessions),
            "offset": offset,
            "limit": limit
        }

    except Exception as e:
        import logging
        logging.exception("🔥 Failed to fetch user history")
        return {"error": str(e)}


class FeedbackRequest(BaseModel):
    message_index: int
    feedback: str
    session_id: str | None = None
    user_id: str | None = None
    agent_id: str | None = None
    model_used: str | None = None
    recommendationId: str | None = None

@router.post("/feedback/submit")
async def submit_feedback(data: FeedbackRequest):
    try:
        db.collection("feedback").add({
            "message_index": data.message_index,
            "feedback": data.feedback.strip(),
            "session_id": data.session_id,
            "user_id": data.user_id,
            "agent_id": data.agent_id,
            "model_used": data.model_used,
            "recommendationId": data.recommendationId,
            "created_at": admin_firestore.SERVER_TIMESTAMP
        })
        return {"status": "success", "message": "Feedback submitted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/credit-management/credits")
async def get_user_credits(user_data=Depends(require_role("user"))):
    """Get the current user's credit balance and XP data"""
    user_id = user_data["uid"]

    # Get user document
    user_ref = db.collection("users").document(user_id)
    user_doc = user_ref.get()

    if not user_doc.exists:
        # If user document doesn't exist, create it with default credits
        default_credits = 10  # Default starting credits for new users
        user_ref.set({
            "uid": user_id,
            "credits": default_credits,
            "xp": 0,
            "lifetime_xp": 0,
            "created_at": admin_firestore.SERVER_TIMESTAMP
        })
        return {
            "credits": default_credits,
            "xp": 0,
            "lifetime_xp": 0,
            "available_xp": 0
        }

    # Return credits from existing user document
    user_data = user_doc.to_dict()

    # Get XP data
    current_xp = user_data.get("xp", 0)
    lifetime_xp = user_data.get("lifetime_xp", current_xp)  # Use current XP as fallback if lifetime_xp not set

    # Fetch credit spends from payments collection
    payments_ref = db.collection("payments").document(user_id)
    payments_doc = payments_ref.get()
    payments_data = payments_doc.to_dict() if payments_doc.exists else {}

    # For backward compatibility, return the first 10 credit spends
    credit_spends = payments_data.get("credit_spends", [])

    # Sort credit spends by created_at in descending order if available
    if credit_spends:
        credit_spends.sort(
            key=lambda x: x.get("created_at", ""),
            reverse=True
        )

    return {
        "uid": user_id,
        "credits": user_data.get("credits", 0),
        "xp": current_xp,
        "lifetime_xp": lifetime_xp,
        "available_xp": current_xp,  # Add available XP for UI to use directly
        "pending_total": user_data.get("pending_total", 0),
        "confirmed_total": user_data.get("confirmed_total", 0),
        "credit_spends": credit_spends[:10],  # Return only the first 10 for backward compatibility
        "total_credit_spends": len(credit_spends),
        "conversions": payments_data.get("conversions", [])
    }

@router.get("/credit-management/credit-spends")
async def get_user_credit_spends(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=50, description="Items per page"),
    user_data=Depends(require_role("user"))
):
    """Get paginated credit spends for the current user"""
    user_id = user_data["uid"]

    # Fetch credit spends from payments collection
    payments_ref = db.collection("payments").document(user_id)
    payments_doc = payments_ref.get()

    if not payments_doc.exists:
        return {
            "credit_spends": [],
            "total": 0,
            "page": page,
            "page_size": page_size,
            "total_pages": 0
        }

    payments_data = payments_doc.to_dict()
    credit_spends = payments_data.get("credit_spends", [])

    # Sort credit spends by created_at in descending order if available
    if credit_spends:
        credit_spends.sort(
            key=lambda x: x.get("created_at", ""),
            reverse=True
        )

    # Calculate pagination
    total = len(credit_spends)
    total_pages = (total + page_size - 1) // page_size  # Ceiling division

    # Get the requested page
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_spends = credit_spends[start_idx:end_idx]

    return {
        "credit_spends": paginated_spends,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages
    }

@router.post("/credit-management/credits/update")
async def update_user_credits(
    payload: dict,
    user_data=Depends(require_role("user"))
):
    """Update the user's credit balance (admin only)"""
    user_id = user_data["uid"]

    # Validate payload
    if "credits" not in payload:
        raise HTTPException(status_code=400, detail="Credits value is required")

    try:
        new_credits = int(payload["credits"])
        if new_credits < 0:
            raise ValueError("Credits cannot be negative")
    except (ValueError, TypeError):
        raise HTTPException(status_code=400, detail="Credits must be a positive integer")

    # Update user document
    user_ref = db.collection("users").document(user_id)
    user_ref.update({
        "credits": new_credits,
        "updated_at": admin_firestore.SERVER_TIMESTAMP
    })

    return {"credits": new_credits}

@router.post("/credit-management/credits/deduct")
async def deduct_user_credits(
    payload: dict,
    user_data=Depends(require_role("user"))
):
    """Deduct credits from the user's balance"""
    user_id = user_data["uid"]

    # Validate payload
    if "amount" not in payload:
        raise HTTPException(status_code=400, detail="Amount to deduct is required")

    try:
        amount = int(payload["amount"])
        if amount <= 0:
            raise ValueError("Amount must be positive")
    except (ValueError, TypeError):
        raise HTTPException(status_code=400, detail="Amount must be a positive integer")

    # Get current credits
    user_ref = db.collection("users").document(user_id)
    user_doc = user_ref.get()

    if not user_doc.exists:
        raise HTTPException(status_code=404, detail="User not found")

    user_data = user_doc.to_dict()
    current_credits = user_data.get("credits", 0)

    # Check if user has enough credits
    if current_credits < amount:
        raise HTTPException(status_code=400, detail="Insufficient credits")

    # Deduct credits
    new_credits = current_credits - amount
    user_ref.update({
        "credits": new_credits,
        "updated_at": admin_firestore.SERVER_TIMESTAMP
    })

    return {
        "credits": new_credits,
        "deducted": amount,
        "previous_credits": current_credits
    }

@router.post("/credit-management/credits/add")
async def add_user_credits(
    payload: dict,
    user_data=Depends(require_role("user"))
):
    """Add credits to the user's balance"""
    user_id = user_data["uid"]

    # Validate payload
    if "amount" not in payload:
        raise HTTPException(status_code=400, detail="Amount to add is required")

    try:
        amount = int(payload["amount"])
        if amount <= 0:
            raise ValueError("Amount must be positive")
    except (ValueError, TypeError):
        raise HTTPException(status_code=400, detail="Amount must be a positive integer")

    # Get current credits
    user_ref = db.collection("users").document(user_id)
    user_doc = user_ref.get()

    if not user_doc.exists:
        # Create user document if it doesn't exist
        current_credits = 0
        user_ref.set({
            "uid": user_id,
            "credits": amount,
            "created_at": admin_firestore.SERVER_TIMESTAMP
        })
    else:
        # Update existing document
        user_data = user_doc.to_dict()
        current_credits = user_data.get("credits", 0)
        new_credits = current_credits + amount
        user_ref.update({
            "credits": new_credits,
            "updated_at": admin_firestore.SERVER_TIMESTAMP
        })

    return {
        "credits": current_credits + amount,
        "added": amount,
        "previous_credits": current_credits
    }

# ----------------------------
# GET /user/payments
# ----------------------------
@router.get("/payments")
async def get_user_payments(uid: str = Query(...)):
    conversions_ref = db.collection("conversions").where("user_id", "==", uid)
    docs = conversions_ref.stream()

    conversions = []
    confirmed_total = 0
    pending_total = 0

    for doc in docs:
        data = doc.to_dict()
        conversions.append(data)
        if data["status"] == "confirmed":
            confirmed_total += data.get("reward", 0)
        elif data["status"] == "pending":
            pending_total += data.get("reward", 0)

    payout_doc = db.collection("payout_methods").document(uid).get()
    payout_method = payout_doc.to_dict() if payout_doc.exists else None

    return {
        "confirmed_total": confirmed_total,
        "pending_total": pending_total,
        "conversions": conversions,
        "payout_method": payout_method
    }

# ----------------------------
# POST /user/payments/update-method
# ----------------------------
class UpdatePayoutMethod(BaseModel):
    uid: str
    email: str
    provider: Optional[str] = "stripe"

@router.post("/payments/update-method")
async def update_payout_method(payload: UpdatePayoutMethod):
    db.collection("payout_methods").document(payload.uid).set({
        "email": payload.email,
        "provider": payload.provider,
        "updated_at": datetime.now(timezone.utc).isoformat()
    })
    return { "status": "success", "email": payload.email }

# ----------------------------
# POST /user/payments/withdraw
# ----------------------------
class WithdrawRequest(BaseModel):
    uid: str
    amount: int  # Amount in cents

@router.post("/payments/withdraw")
async def submit_withdrawal(req: WithdrawRequest):
    db.collection("withdraw_requests").add({
        "uid": req.uid,
        "amount": req.amount,
        "status": "pending",
        "requested_at": datetime.now(timezone.utc).isoformat()
    })
    return { "status": "submitted", "amount": req.amount }

# ----------------------------
# GET /user/pioneer/eligibility
# ----------------------------
@router.get("/pioneer/eligibility")
async def check_pioneer_eligibility(user_data=Depends(require_role("user"))):
    """Check if a user is eligible for the Agent Pioneer Program"""
    try:
        # Get the user's ID
        user_id = user_data["uid"]

        # Get the counter document that tracks users with XP
        counter_ref = db.collection("counters").document("pioneer_users")
        counter_doc = counter_ref.get()

        # If counter doesn't exist, create it
        if not counter_doc.exists:
            counter_ref.set({
                "count": 0,
                "updated_at": admin_firestore.SERVER_TIMESTAMP
            })
            current_count = 0
        else:
            current_count = counter_doc.to_dict().get("count", 0)

        # Check if the user is within the first 500 users
        is_eligible = current_count < 500

        # Get the user's document to check if they already have XP
        user_ref = db.collection("users").document(user_id)
        user_doc = user_ref.get()

        if user_doc.exists:
            user_data = user_doc.to_dict()
            has_xp = user_data.get("xp", 0) > 0

            # If the user already has XP, they're already counted
            # If not and they're eligible, we'll mark them as eligible
            if not has_xp and is_eligible:
                user_ref.update({
                    "isPioneerEligible": True,
                    "updated_at": admin_firestore.SERVER_TIMESTAMP
                })

        return {
            "isPioneerEligible": is_eligible,
            "currentCount": current_count,
            "maxPioneers": 500
        }

    except Exception as e:
        logger.error(f"Error checking pioneer eligibility: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check eligibility: {str(e)}")

# ----------------------------
# PATCH /user/update-onboarding
# ----------------------------
@router.patch("/update-onboarding")
async def update_onboarding(
    payload: dict,
    user_data=Depends(require_role("user"))
):
    """Update user onboarding status, pioneer welcome flag, and XP"""
    try:
        # Get the user's ID
        user_id = user_data["uid"]

        # Validate the payload
        valid_fields = ["onboardingStatus", "seenPioneerWelcome", "xp", "profession", "interests", "name", "agentName"]
        update_data = {k: v for k, v in payload.items() if k in valid_fields and v is not None}

        if not update_data:
            raise HTTPException(status_code=400, detail="No valid fields provided for update")

        # Update user document
        user_ref = db.collection("users").document(user_id)
        user_ref.update({
            **update_data,
            "updated_at": admin_firestore.SERVER_TIMESTAMP
        })

        return {
            "status": "success",
            "updated": update_data
        }

    except Exception as e:
        logger.error(f"Error updating onboarding status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update onboarding status: {str(e)}")

# ----------------------------
# GET /user/pioneer/xp-logs
# ----------------------------
@router.get("/pioneer/xp-logs", name="get_user_xp_logs")
async def get_user_xp_logs(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=50, description="Items per page"),
    user_data=Depends(require_role("user"))
):
    """Get paginated XP logs for the current user"""
    try:
        # Get the user's ID
        user_id = user_data["uid"]

        # Get XP logs from the user's subcollection
        xp_logs_ref = db.collection("users").document(user_id).collection("xp_logs")

        # Get total count first
        total_count_query = xp_logs_ref.count()
        total_count = total_count_query.get()[0][0].value

        # Calculate pagination
        total_pages = (total_count + page_size - 1) // page_size

        # Adjust page if it's out of bounds
        if page > total_pages and total_pages > 0:
            page = total_pages

        # Calculate offset
        offset = (page - 1) * page_size

        # Query with pagination
        xp_logs_query = xp_logs_ref.order_by("timestamp", direction=admin_firestore.Query.DESCENDING)

        # Apply pagination using limit and offset
        if offset > 0:
            # Get the last document from the previous page to use as a starting point
            prev_page_docs = list(xp_logs_query.limit(offset).stream())
            if prev_page_docs:
                last_doc = prev_page_docs[-1]
                xp_logs_query = xp_logs_query.start_after(last_doc)

        xp_logs_query = xp_logs_query.limit(page_size)
        xp_logs = xp_logs_query.stream()

        # Convert to list of dicts
        logs = []
        for log in xp_logs:
            log_data = log.to_dict()
            log_data["id"] = log.id

            # Convert timestamp to string if it's a Firestore timestamp
            if "timestamp" in log_data and hasattr(log_data["timestamp"], "seconds"):
                log_data["timestamp"] = log_data["timestamp"].strftime("%Y-%m-%dT%H:%M:%SZ")

            logs.append(log_data)

        return {
            "logs": logs,
            "pagination": {
                "total": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages
            }
        }
    except Exception as e:
        logger.error(f"Error getting XP logs: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get XP logs: {str(e)}")



# ----------------------------
# POST /user/pioneer/increment-xp
# ----------------------------
@router.post("/pioneer/increment-xp")
async def increment_user_xp(user_data=Depends(require_role("user"))):
    """Increment a user's XP and update the pioneer counter if needed"""
    try:
        # Get the user's ID
        user_id = user_data["uid"]

        # Get the user's document
        user_ref = db.collection("users").document(user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")

        user_data = user_doc.to_dict()
        current_xp = user_data.get("xp", 0)
        has_xp = current_xp > 0

        # Increment XP by 10
        new_xp = current_xp + 10

        # Update the user's XP and lifetime_xp
        user_ref.update({
            "xp": new_xp,
            "lifetime_xp": admin_firestore.Increment(10),  # Increment lifetime XP
            "updated_at": safe_timestamp()
        })

        # Log the XP gain in the user's XP logs
        db.collection("users").document(user_id).collection("xp_logs").add({
            "amount": 10,
            "reason": "Earned XP for using AdMesh",
            "timestamp": safe_timestamp(),  # Use safe_timestamp instead of SERVER_TIMESTAMP for nested documents
            "type": "gain",
            "transaction_type": "credit",  # XP is being credited (added)
            "is_credit": True  # Explicitly mark as a credit transaction
        })

        # If this is the first time the user is getting XP, increment the counter
        if not has_xp:
            counter_ref = db.collection("counters").document("pioneer_users")
            counter_ref.update({
                "count": admin_firestore.Increment(1),
                "updated_at": safe_timestamp()
            })

        # Check for badges after XP update
        awarded_badges = await BadgeService.check_and_award_badges(user_id)

        # Prepare badge notifications
        badge_notifications = []
        if awarded_badges:
            for badge in awarded_badges:
                badge_notifications.append({
                    "badge_type": badge.badge_type,
                    "name": badge.metadata.name,
                    "description": badge.metadata.description,
                    "xp_bonus": badge.metadata.xp_bonus
                })

        # Get the lifetime XP
        lifetime_xp = user_data.get("lifetime_xp", new_xp)

        return {
            "previousXp": current_xp,
            "newXp": new_xp,
            "xpGained": 10,
            "lifetime_xp": lifetime_xp,
            "badges_awarded": badge_notifications
        }

    except Exception as e:
        logger.error(f"Error incrementing user XP: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to increment XP: {str(e)}")

# ----------------------------
# POST /user/pioneer/convert-xp
# ----------------------------
@router.post("/pioneer/convert-xp")
async def convert_xp_to_credits(
    payload: dict = Body(..., example={"amount": 100}),
    user_data=Depends(require_role("user"))
):
    """Convert XP to credits at a rate of 10 XP = 1 credit"""
    try:
        # Get the user's ID
        user_id = user_data["uid"]

        # Validate the payload
        if "amount" not in payload:
            raise HTTPException(status_code=400, detail="Amount to convert is required")

        xp_amount = int(payload["amount"])

        # Validate the amount
        if xp_amount <= 0:
            raise HTTPException(status_code=400, detail="Amount must be positive")

        if xp_amount % 10 != 0:
            raise HTTPException(status_code=400, detail="Amount must be a multiple of 10")

        # Get the user's document
        user_ref = db.collection("users").document(user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            raise HTTPException(status_code=404, detail="User not found")

        user_data = user_doc.to_dict()
        current_xp = user_data.get("xp", 0)

        # Check if user has enough XP
        if current_xp < xp_amount:
            raise HTTPException(status_code=400, detail="Insufficient XP")

        # Calculate credits to add (10 XP = 1 credit)
        credits_to_add = xp_amount // 10

        # Update user document in a transaction to ensure atomicity
        transaction = db.transaction()

        @admin_firestore.transactional
        def update_user_in_transaction(transaction, user_ref, xp_amount, credits_to_add):
            # Get the latest user data
            user_snapshot = user_ref.get(transaction=transaction)
            if not user_snapshot.exists:
                raise HTTPException(status_code=404, detail="User not found")

            user_data = user_snapshot.to_dict()
            current_xp = user_data.get("xp", 0)
            current_credits = user_data.get("credits", 0)
            lifetime_xp = user_data.get("lifetime_xp", current_xp)

            # Check if user still has enough XP
            if current_xp < xp_amount:
                raise HTTPException(status_code=400, detail="Insufficient XP")

            # Update user document - reduce available XP but keep lifetime_xp unchanged
            transaction.update(user_ref, {
                "xp": current_xp - xp_amount,
                "credits": current_credits + credits_to_add,
                "updated_at": safe_timestamp()
            })

            return {
                "previous_xp": current_xp,
                "new_xp": current_xp - xp_amount,
                "previous_credits": current_credits,
                "new_credits": current_credits + credits_to_add,
                "xp_converted": xp_amount,
                "credits_added": credits_to_add,
                "lifetime_xp": lifetime_xp
            }

        # Execute the transaction
        result = update_user_in_transaction(transaction, user_ref, xp_amount, credits_to_add)

        # Log the XP conversion in the user's XP logs
        db.collection("users").document(user_id).collection("xp_logs").add({
            "amount": -xp_amount,
            "reason": f"Converted {xp_amount} XP to {credits_to_add} credits",
            "timestamp": admin_firestore.SERVER_TIMESTAMP,
            "type": "conversion",
            "transaction_type": "debit"  # XP is being debited (reduced)
        })

        # Log the credit addition in the user's credit spends (as a negative spend = addition)
        payments_ref = db.collection("payments").document(user_id)
        payments_doc = payments_ref.get()

        if not payments_doc.exists:
            # Create the payments document if it doesn't exist
            payments_ref.set({
                "uid": user_id,
                "credit_spends": [],
                "created_at": admin_firestore.SERVER_TIMESTAMP
            })

        # Add the credit conversion to the credit_spends array
        payments_ref.update({
            "credit_spends": admin_firestore.ArrayUnion([{
                "id": f"xp-conversion-{int(time.time())}",
                "credits": credits_to_add,
                "type": "xp_conversion",
                "created_at": safe_timestamp(),
                "status": "completed",
                "transaction_type": "credit",  # Credits are being credited (added)
                "is_credit": True  # Explicitly mark as a credit transaction
            }]),
            "updated_at": admin_firestore.SERVER_TIMESTAMP
        })

        return {
            "status": "success",
            **result
        }

    except Exception as e:
        logger.error(f"Error converting XP to credits: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to convert XP to credits: {str(e)}")

# Admin endpoint to get all users
@router.get("/admin/all")
async def admin_get_all_users(
    limit: int = Query(50, description="Maximum number of users to return"),
    offset: int = Query(0, description="Number of users to skip"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_direction: str = Query("desc", description="Sort direction (asc or desc)"),
    decoded_token = Depends(verify_firebase_token)
):
    """Admin endpoint to get all users"""
    # Check if the user is an admin
    is_admin = decoded_token.get("admin", False)
    if not is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    logger.info(f"Admin fetching all users (limit: {limit}, offset: {offset}, sort_by: {sort_by}, sort_direction: {sort_direction})")

    try:
        # Start with base query
        query = db.collection("users")

        # Apply sorting if provided
        if sort_by in ["created_at", "updated_at", "xp", "credits", "clicks_made", "conversions"]:
            direction = admin_firestore.Query.DESCENDING if sort_direction == "desc" else admin_firestore.Query.ASCENDING
            query = query.order_by(sort_by, direction=direction)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute query
        snapshot = query.stream()

        users = []
        for doc in snapshot:
            user_data = doc.to_dict()
            # Ensure id is included
            if "uid" not in user_data:
                user_data["uid"] = doc.id
            users.append(user_data)

        # Get total count for pagination
        # Note: This is inefficient for large collections, but works for now
        total_count = len(list(db.collection("users").select([]).stream()))

        return {
            "status": "success",
            "users": users,
            "pagination": {
                "total": total_count,
                "limit": limit,
                "offset": offset
            }
        }
    except Exception as e:
        logger.error(f"Error fetching users for admin: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch users: {str(e)}")

# Simple test endpoint that doesn't require authentication
@router.get("/test")
async def test_endpoint():
    """Test endpoint to verify the user router is working"""
    logger.info("User test endpoint called")
    try:
        # Test Firestore connection
        db_status = "OK" if db else "Not initialized"

        # Return diagnostic information
        return {
            "status": "ok",
            "message": "User API is working",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "db_status": db_status,
            "environment": {
                "python_version": "3.12",
                "dependencies": ["fastapi", "firebase-admin", "email-validator"]
            }
        }
    except Exception as e:
        logger.error(f"Error in test endpoint: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "traceback": traceback.format_exc()
        }
