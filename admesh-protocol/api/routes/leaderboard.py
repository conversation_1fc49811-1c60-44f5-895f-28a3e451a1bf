from fastapi import APIRouter, HTTPException, Query, Depends
from firebase.config import get_db
import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Optional, List
from pydantic import BaseModel
from auth.deps import require_role
import uuid

router = APIRouter()
logger = logging.getLogger(__name__)
db = get_db()

def safe_timestamp():
    """Return a safe timestamp for use in Firestore arrays and nested structures.
    Use this instead of firestore.SERVER_TIMESTAMP when adding timestamps to arrays or nested objects.
    """
    return datetime.now(timezone.utc)

class SortBy(str, Enum):
    XP = "xp"
    BADGES = "badges"
    JOINED = "joined"

class LeaderboardUser(BaseModel):
    id: str
    name: str
    avatar_url: str
    xp: int
    lifetime_xp: int
    badge_count: int
    badges: List[dict]
    agent_name: Optional[str] = None
    joined_at: str

class LeaderboardResponse(BaseModel):
    users: List[LeaderboardUser]
    total: int
    page: int
    limit: int

class CustomLeaderboardCreate(BaseModel):
    name: str
    description: str
    start_date: str
    end_date: str
    user_ids: Optional[List[str]] = None
    is_active: bool = True

class CustomLeaderboardUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    user_ids: Optional[List[str]] = None
    is_active: Optional[bool] = None

class CustomLeaderboard(BaseModel):
    id: str
    name: str
    description: str
    start_date: str
    end_date: str
    user_ids: Optional[List[str]] = None
    is_active: bool = True
    created_at: str
    created_by: str
    updated_at: Optional[str] = None
    updated_by: Optional[str] = None

@router.get("", response_model=LeaderboardResponse)
async def get_leaderboard(
    sort_by: SortBy = Query(SortBy.XP, description="Field to sort by"),
    limit: int = Query(10, ge=1, le=50, description="Number of users to return"),
    page: int = Query(1, ge=1, description="Page number"),
):
    """
    Get the public leaderboard data with pagination and sorting.

    - **sort_by**: Field to sort by (xp, badges, joined)
    - **limit**: Number of users to return per page (1-50)
    - **page**: Page number (starts at 1)
    """
    try:
        # Calculate offset
        offset = (page - 1) * limit

        # Get all users who have completed onboarding
        # Note: In a production environment with many users, we would need to implement
        # a more efficient query strategy, possibly with composite indexes or denormalization
        users_query = db.collection("users").where("onboardingStatus", "==", "completed")

        # Get total count for pagination
        total_users = len(list(users_query.stream()))

        # Get users for the current page
        users_ref = users_query.stream()

        # Process users and their badges
        leaderboard_users = []
        for user_doc in users_ref:
            user_data = user_doc.to_dict()

            # Get user badges from subcollection
            badges_ref = db.collection("users").document(user_doc.id).collection("badges").stream()
            badges = [badge.to_dict() for badge in badges_ref]

            # Only include users who have at least one badge or some XP
            if len(badges) == 0 and (user_data.get("xp", 0) <= 0):
                continue

            # Format created_at timestamp
            created_at = user_data.get("created_at")
            if created_at and hasattr(created_at, "timestamp"):
                # Handle Firestore timestamp objects
                joined_at = datetime.fromtimestamp(created_at.timestamp(), tz=timezone.utc).isoformat()
            elif isinstance(created_at, datetime):
                joined_at = created_at.isoformat()
            else:
                joined_at = safe_timestamp().isoformat()

            # Create leaderboard user entry
            leaderboard_user = LeaderboardUser(
                id=user_doc.id,
                name=user_data.get("displayName", "AdMesh User"),
                avatar_url=user_data.get("photoURL", ""),
                xp=user_data.get("xp", 0),
                lifetime_xp=user_data.get("lifetime_xp", user_data.get("xp", 0)),
                badge_count=len(badges),
                badges=badges,
                agent_name=user_data.get("agentName", user_data.get("name", "")),
                joined_at=joined_at
            )

            leaderboard_users.append(leaderboard_user)

        # Sort users based on the sort_by parameter
        if sort_by == SortBy.XP:
            leaderboard_users.sort(key=lambda user: user.xp, reverse=True)
        elif sort_by == SortBy.BADGES:
            leaderboard_users.sort(key=lambda user: user.badge_count, reverse=True)
        elif sort_by == SortBy.JOINED:
            leaderboard_users.sort(key=lambda user: user.joined_at, reverse=True)

        # Apply pagination
        paginated_users = leaderboard_users[offset:offset + limit]

        return LeaderboardResponse(
            users=paginated_users,
            total=total_users,
            page=page,
            limit=limit
        )
    except Exception as e:
        logger.error(f"Error getting leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get leaderboard: {str(e)}")

@router.post("/custom", response_model=CustomLeaderboard)
async def create_custom_leaderboard(
    leaderboard: CustomLeaderboardCreate,
    user=Depends(require_role("admin"))
):
    """
    Create a custom leaderboard (admin only).

    This endpoint allows admins to create custom leaderboards for special events or time periods.
    """
    try:
        leaderboard_id = str(uuid.uuid4())

        # Create the leaderboard document
        created_at = safe_timestamp()
        leaderboard_data = {
            "id": leaderboard_id,
            "name": leaderboard.name,
            "description": leaderboard.description,
            "start_date": leaderboard.start_date,
            "end_date": leaderboard.end_date,
            "user_ids": leaderboard.user_ids or [],
            "is_active": leaderboard.is_active,
            "created_at": created_at,
            "created_by": user["uid"]
        }

        # Save to Firestore
        db.collection("custom_leaderboards").document(leaderboard_id).set(leaderboard_data)

        # Convert timestamp to string for response
        leaderboard_data["created_at"] = created_at.isoformat()

        return CustomLeaderboard(**leaderboard_data)
    except Exception as e:
        logger.error(f"Error creating custom leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create custom leaderboard: {str(e)}")

@router.get("/custom/{leaderboard_id}", response_model=LeaderboardResponse)
async def get_custom_leaderboard(
    leaderboard_id: str,
    sort_by: SortBy = Query(SortBy.XP, description="Field to sort by"),
    limit: int = Query(10, ge=1, le=50, description="Number of users to return"),
    page: int = Query(1, ge=1, description="Page number"),
):
    """
    Get a custom leaderboard by ID with pagination and sorting.

    - **leaderboard_id**: ID of the custom leaderboard
    - **sort_by**: Field to sort by (xp, badges, joined)
    - **limit**: Number of users to return per page (1-50)
    - **page**: Page number (starts at 1)
    """
    try:
        # Calculate offset
        offset = (page - 1) * limit

        # Get the custom leaderboard
        leaderboard_ref = db.collection("custom_leaderboards").document(leaderboard_id)
        leaderboard_doc = leaderboard_ref.get()

        if not leaderboard_doc.exists:
            raise HTTPException(status_code=404, detail="Custom leaderboard not found")

        leaderboard_data = leaderboard_doc.to_dict()

        # Check if leaderboard is active
        if not leaderboard_data.get("is_active", False):
            raise HTTPException(status_code=403, detail="This leaderboard is not active")

        # Get users from the leaderboard
        user_ids = leaderboard_data.get("user_ids", [])

        # If no specific users are defined, get all users
        if not user_ids:
            users_query = db.collection("users").where("onboardingStatus", "==", "completed")
        else:
            # Get only the specified users
            # Note: Firestore has a limit on 'in' queries (usually 10 items)
            # For larger lists, we would need to use batching
            if len(user_ids) > 10:
                # For simplicity, we'll just get all users and filter in memory
                # In production, this should be implemented with batching
                users_query = db.collection("users").where("onboardingStatus", "==", "completed")
            else:
                users_query = db.collection("users").where("__name__", "in",
                    [db.collection("users").document(uid).path for uid in user_ids])

        # Get total count for pagination
        total_users = len(list(users_query.stream()))

        # Get users for the current page
        users_ref = users_query.stream()

        # Process users and their badges
        leaderboard_users = []
        for user_doc in users_ref:
            # Skip if not in user_ids when specified
            if user_ids and user_doc.id not in user_ids:
                continue

            user_data = user_doc.to_dict()

            # Skip users who haven't completed onboarding
            if user_data.get("onboardingStatus") != "completed":
                continue

            # Get user badges from subcollection
            badges_ref = db.collection("users").document(user_doc.id).collection("badges").stream()
            badges = [badge.to_dict() for badge in badges_ref]

            # Only include users who have at least one badge or some XP
            if len(badges) == 0 and (user_data.get("xp", 0) <= 0):
                continue

            # Format created_at timestamp
            created_at = user_data.get("created_at")
            if created_at and hasattr(created_at, "timestamp"):
                # Handle Firestore timestamp objects
                joined_at = datetime.fromtimestamp(created_at.timestamp(), tz=timezone.utc).isoformat()
            elif isinstance(created_at, datetime):
                joined_at = created_at.isoformat()
            else:
                joined_at = safe_timestamp().isoformat()

            # Create leaderboard user entry
            leaderboard_user = LeaderboardUser(
                id=user_doc.id,
                name=user_data.get("displayName", "AdMesh User"),
                avatar_url=user_data.get("photoURL", ""),
                xp=user_data.get("xp", 0),
                lifetime_xp=user_data.get("lifetime_xp", user_data.get("xp", 0)),
                badge_count=len(badges),
                badges=badges,
                agent_name=user_data.get("agentName", user_data.get("name", "")),
                joined_at=joined_at
            )

            leaderboard_users.append(leaderboard_user)

        # Sort users based on the sort_by parameter
        if sort_by == SortBy.XP:
            leaderboard_users.sort(key=lambda user: user.xp, reverse=True)
        elif sort_by == SortBy.BADGES:
            leaderboard_users.sort(key=lambda user: user.badge_count, reverse=True)
        elif sort_by == SortBy.JOINED:
            leaderboard_users.sort(key=lambda user: user.joined_at, reverse=True)

        # Apply pagination
        paginated_users = leaderboard_users[offset:offset + limit]

        return LeaderboardResponse(
            users=paginated_users,
            total=total_users,
            page=page,
            limit=limit
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting custom leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get custom leaderboard: {str(e)}")

@router.get("/custom", response_model=List[CustomLeaderboard])
async def list_custom_leaderboards():
    """
    List all active custom leaderboards.
    """
    try:
        # Get all active custom leaderboards
        leaderboards_ref = db.collection("custom_leaderboards").where("is_active", "==", True).stream()

        leaderboards = []
        for doc in leaderboards_ref:
            leaderboard_data = doc.to_dict()

            # Format created_at timestamp
            created_at = leaderboard_data.get("created_at")
            if created_at and hasattr(created_at, "timestamp"):
                # Handle Firestore timestamp objects
                leaderboard_data["created_at"] = datetime.fromtimestamp(created_at.timestamp(), tz=timezone.utc).isoformat()
            elif isinstance(created_at, datetime):
                leaderboard_data["created_at"] = created_at.isoformat()
            else:
                leaderboard_data["created_at"] = safe_timestamp().isoformat()

            # Format updated_at timestamp if it exists
            updated_at = leaderboard_data.get("updated_at")
            if updated_at and hasattr(updated_at, "timestamp"):
                leaderboard_data["updated_at"] = datetime.fromtimestamp(updated_at.timestamp(), tz=timezone.utc).isoformat()
            elif isinstance(updated_at, datetime):
                leaderboard_data["updated_at"] = updated_at.isoformat()

            leaderboards.append(CustomLeaderboard(**leaderboard_data))

        return leaderboards
    except Exception as e:
        logger.error(f"Error listing custom leaderboards: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list custom leaderboards: {str(e)}")

@router.patch("/custom/{leaderboard_id}", response_model=CustomLeaderboard)
async def update_custom_leaderboard(
    leaderboard_id: str,
    update_data: CustomLeaderboardUpdate,
    user=Depends(require_role("admin"))
):
    """
    Update a custom leaderboard (admin only).

    This endpoint allows admins to update an existing custom leaderboard.
    """
    try:
        # Get the leaderboard
        leaderboard_ref = db.collection("custom_leaderboards").document(leaderboard_id)
        leaderboard_doc = leaderboard_ref.get()

        if not leaderboard_doc.exists:
            raise HTTPException(status_code=404, detail="Custom leaderboard not found")

        # Prepare update data
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}

        if not update_dict:
            raise HTTPException(status_code=400, detail="No update data provided")

        # Add updated_at timestamp
        updated_at = safe_timestamp()
        update_dict["updated_at"] = updated_at
        update_dict["updated_by"] = user["uid"]

        # Update the document
        leaderboard_ref.update(update_dict)

        # Get the updated document
        updated_doc = leaderboard_ref.get().to_dict()

        # Set the updated_at field in the response
        updated_doc["updated_at"] = updated_at.isoformat()

        # Format created_at timestamp if it exists
        if "created_at" in updated_doc:
            timestamp = updated_doc.get("created_at")
            if timestamp and hasattr(timestamp, "timestamp"):
                # Handle Firestore timestamp objects
                updated_doc["created_at"] = datetime.fromtimestamp(timestamp.timestamp(), tz=timezone.utc).isoformat()
            elif isinstance(timestamp, datetime):
                updated_doc["created_at"] = timestamp.isoformat()

        return CustomLeaderboard(**updated_doc)
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating custom leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update custom leaderboard: {str(e)}")

@router.delete("/custom/{leaderboard_id}")
async def delete_custom_leaderboard(
    leaderboard_id: str,
    _=Depends(require_role("admin"))  # Using _ to indicate we only care about the auth check
):
    """
    Delete a custom leaderboard (admin only).

    This endpoint allows admins to delete an existing custom leaderboard.
    """
    try:
        # Get the leaderboard
        leaderboard_ref = db.collection("custom_leaderboards").document(leaderboard_id)
        leaderboard_doc = leaderboard_ref.get()

        if not leaderboard_doc.exists:
            raise HTTPException(status_code=404, detail="Custom leaderboard not found")

        # Delete the document
        leaderboard_ref.delete()

        return {"status": "success", "message": "Leaderboard deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting custom leaderboard: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete custom leaderboard: {str(e)}")
