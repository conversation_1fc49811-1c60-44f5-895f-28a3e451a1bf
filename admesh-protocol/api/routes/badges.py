from fastapi import APIRouter, Depends, HTTPException, Request
from typing import List, Dict, Any, Optional
from auth.deps import require_role, verify_firebase_token
from api.services.badge_service import BadgeService, get_user_query_count, get_user_feedback_count, get_user_followup_count
from api.models.badge import BadgeType, BADGE_DEFINITIONS
from google.cloud import firestore
from firebase.config import get_db
import logging
from datetime import datetime, timezone

router = APIRouter()
logger = logging.getLogger(__name__)
db = get_db()

def safe_timestamp():
    """Return a safe timestamp for use in Firestore arrays and nested structures.
    Use this instead of firestore.SERVER_TIMESTAMP when adding timestamps to arrays or nested objects.
    """
    return datetime.now(timezone.utc)

async def _get_badge_data(badge_id: str, badge_type: str, user_id: str) -> Optional[Dict[str, Any]]:
    """Get detailed badge data including metadata"""
    try:
        # Get the badge from user's subcollection if it exists
        badge_ref = db.collection("users").document(user_id).collection("badges").document(badge_id)
        badge_doc = badge_ref.get()

        if badge_doc.exists:
            badge_data = badge_doc.to_dict()

            # Get metadata from badge definitions
            for badge_enum, metadata in BADGE_DEFINITIONS.items():
                if badge_enum.value == badge_type:
                    badge_data["metadata"] = {
                        "name": metadata.name,
                        "description": metadata.description,
                        "icon": metadata.icon,
                        "color": metadata.color,
                        "xp_bonus": metadata.xp_bonus,
                        "earnings_boost_percent": metadata.earnings_boost_percent
                    }
                    badge_data["status"] = "earned"
                    return badge_data

        # If badge doesn't exist in user collection or metadata not found,
        # try to create a basic badge object with metadata
        for badge_enum, metadata in BADGE_DEFINITIONS.items():
            if badge_enum.value == badge_type:
                return {
                    "badge_id": badge_id,
                    "badge_type": badge_type,
                    "metadata": {
                        "name": metadata.name,
                        "description": metadata.description,
                        "icon": metadata.icon,
                        "color": metadata.color,
                        "xp_bonus": metadata.xp_bonus,
                        "earnings_boost_percent": metadata.earnings_boost_percent
                    },
                    "status": "earned",
                    "awarded_at": safe_timestamp()
                }

        return None
    except Exception as e:
        logger.error(f"Error getting badge data: {str(e)}")
        return None

@router.get("/user/all")
async def get_user_all_badges(user_data=Depends(require_role("user"))):
    """Get all badges for the current user"""
    return await _get_user_badges_internal(user_data)

async def _get_user_badges_internal(user_data):
    """Internal function to get user badges"""
    try:
        user_id = user_data["uid"]
        badges = await BadgeService.get_user_badges(user_id)
        return {"badges": badges}
    except Exception as e:
        logger.error(f"Error getting badges: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get badges: {str(e)}")

@router.get("/user/available")
async def get_user_available_badges(user_data=Depends(require_role("user"))):
    """Get all available badges with definitions and user progress"""
    return await _get_all_badges_internal(user_data)

async def _get_all_badges_internal(user_data):
    try:
        from api.models.badge import BADGE_DEFINITIONS, BadgeType

        # Get user's earned badges
        user_id = user_data["uid"]
        user_badges = await BadgeService.get_user_badges(user_id)
        user_badge_types = {badge["badge_type"]: badge for badge in user_badges}

        # Get user data for progress tracking
        user_ref = db.collection("users").document(user_id).get()
        user_data = user_ref.to_dict() or {}
        xp = user_data.get("xp", 0)

        # Get query count for progress tracking
        query_count = await get_user_query_count(user_id)

        # Get followup count for progress tracking
        followup_count = await get_user_followup_count(user_id)

        # Get click count for progress tracking
        click_count = user_data.get("clicks_made", 0)

        # Get conversion count for progress tracking
        conversion_count = user_data.get("conversions", 0)

        # Get feedback count for progress tracking
        feedback_count = await get_user_feedback_count(user_id)

        # Prepare all badges with status and progress
        all_badges = []
        for badge_type, metadata in BADGE_DEFINITIONS.items():
            badge_type_str = badge_type.value

            # Check if user has already earned this badge
            if badge_type_str in user_badge_types:
                badge = user_badge_types[badge_type_str]
                badge["status"] = "earned"
                all_badges.append(badge)
                continue

            # Create badge with progress information
            progress = 0
            progress_target = metadata.required_value

            # Calculate progress based on badge type
            if badge_type in [BadgeType.AGENT_PIONEER, BadgeType.MILESTONE_100, BadgeType.MILESTONE_500, BadgeType.MILESTONE_1000]:
                progress = xp
            elif badge_type == BadgeType.POWER_EXPLORER:
                progress = query_count
            elif badge_type == BadgeType.TOP_RECOMMENDER:
                progress = click_count
            elif badge_type == BadgeType.CONVERSION_MASTER:
                progress = conversion_count
            elif badge_type == BadgeType.FEEDBACK_CHAMPION:
                progress = feedback_count
            elif badge_type == BadgeType.FIRST_DISCOVERY:
                progress = min(query_count, 1)
            elif badge_type == BadgeType.FIRST_FOLLOWUP:
                progress = min(followup_count, 1)
            elif badge_type == BadgeType.FOLLOWUP_50:
                progress = followup_count
            elif badge_type == BadgeType.FOLLOWUP_100:
                progress = followup_count

            # Create badge object
            badge = {
                "badge_id": f"preview_{badge_type_str}",
                "badge_type": badge_type_str,
                "metadata": {
                    "name": metadata.name,
                    "description": metadata.description,
                    "icon": metadata.icon,
                    "color": metadata.color,
                    "xp_bonus": metadata.xp_bonus,
                    "earnings_boost_percent": metadata.earnings_boost_percent
                },
                "progress": progress,
                "progress_target": progress_target,
                "status": "locked"
            }

            all_badges.append(badge)

        # Sort badges: earned first, then by progress percentage
        def sort_key(badge):
            if badge.get("status") == "earned":
                return (0, 0)  # Earned badges first

            progress = badge.get("progress", 0)
            target = badge.get("progress_target", 1)
            if target == 0:
                target = 1  # Avoid division by zero

            progress_percent = progress / target
            return (1, -progress_percent)  # Then by progress percentage (descending)

        all_badges.sort(key=sort_key)

        return {"badges": all_badges}
    except Exception as e:
        logger.error(f"Error getting all badges: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get all badges: {str(e)}")

@router.get("/share/{badge_id}")
async def get_shareable_badge(badge_id: str):
    """Get a badge by ID for public sharing (no auth required)"""
    try:
        # Extract user_id from badge_id format (assuming format like "user_id-badge_type")
        parts = badge_id.split("-", 1)
        if len(parts) != 2:
            raise HTTPException(status_code=400, detail="Invalid badge ID format")

        user_id = parts[0]

        # Get the badge from user's subcollection
        badge_ref = db.collection("users").document(user_id).collection("badges").document(badge_id)
        badge_doc = badge_ref.get()

        if not badge_doc.exists:
            raise HTTPException(status_code=404, detail="Badge not found")

        badge_data = badge_doc.to_dict()
        user_ref = db.collection("users").document(user_id)
        user_doc = user_ref.get()

        user_name = "AdMesh User"
        if user_doc.exists:
            user_data = user_doc.to_dict()
            user_name = user_data.get("name", user_data.get("displayName", "AdMesh User"))

        # Return badge with user name
        return {
            "badge": badge_data,
            "user_name": user_name
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting shareable badge: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get shareable badge: {str(e)}")

@router.post("/user/check")
async def check_and_award_user_badges(decoded_token: dict = Depends(verify_firebase_token)):
    """Check all badge criteria and award badges as appropriate"""
    print("Checking badges...", decoded_token)
    return await _check_badges_internal(decoded_token)

async def _check_badges_internal(user_data):
    """Internal function to check and award badges"""
    try:
        user_id = user_data["uid"]
        awarded_badges = await BadgeService.check_and_award_badges(user_id)

        # Convert badges to dict with proper handling of timestamps and add full badge data
        badge_dicts = []
        for badge in awarded_badges:
            # Get the full badge data with metadata
            badge_data = await _get_badge_data(badge.badge_id, badge.badge_type, user_id)
            if badge_data:
                badge_dicts.append(badge_data)
            else:
                # Fallback to basic badge info if full data can't be retrieved
                badge_dict = badge.model_dump()
                badge_dicts.append(badge_dict)

        return {
            "awarded_badges": badge_dicts,
            "count": len(awarded_badges)
        }
    except Exception as e:
        logger.error(f"Error checking badges: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check badges: {str(e)}")

@router.patch("/user/{badge_id}/toggle-display")
async def toggle_user_badge_display(badge_id: str, user_data=Depends(require_role("user"))):
    """Toggle whether a badge is displayed on the user's profile"""
    return await _toggle_badge_display_internal(badge_id, user_data)

async def _toggle_badge_display_internal(badge_id: str, user_data):
    """Internal function to toggle badge display"""
    try:
        user_id = user_data["uid"]

        # Get the badge from user's subcollection
        badge_ref = db.collection("users").document(user_id).collection("badges").document(badge_id)
        badge_doc = badge_ref.get()

        if not badge_doc.exists:
            raise HTTPException(status_code=404, detail="Badge not found")

        badge_data = badge_doc.to_dict()

        # Toggle display status
        new_status = not badge_data.get("is_displayed", True)
        badge_ref.update({
            "is_displayed": new_status,
            "updated_at": safe_timestamp()
        })

        return {"badge_id": badge_id, "is_displayed": new_status}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling badge display: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to toggle badge display: {str(e)}")

# Admin routes for badge management
@router.post("/admin/award", tags=["Admin"])
async def admin_award_badge(
    user_id: str,
    badge_type: BadgeType,
    decoded_token: dict = Depends(verify_firebase_token)
):
    """Admin route to manually award a badge to a user"""
    # Check if admin
    if decoded_token.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        badge = await BadgeService.award_badge(user_id, badge_type)
        if badge:
            return {"status": "success", "badge": badge.model_dump()}
        else:
            return {"status": "already_awarded", "message": "User already has this badge"}
    except Exception as e:
        logger.error(f"Error awarding badge: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to award badge: {str(e)}")
