from fastapi import APIRouter, Request, Depends, HTTPException
from pydantic import BaseModel
from typing import Optional
import stripe
import os
from firebase_admin import firestore
from firebase.config import get_db
from auth.deps import require_role
import logging
from datetime import datetime
from api.models.subscription import SubscriptionPlan, SubscriptionDocument
from api.utils.subscription_limits import get_subscription_document

router = APIRouter()
db = get_db()

stripe.api_key = os.environ.get("STRIPE_SECRET_KEY")
DOMAIN = os.environ.get("STRIPE_DOMAIN")  # e.g. https://admesh.com

logger = logging.getLogger("stripe_webhook")
logging.basicConfig(level=logging.INFO)

class CheckoutRequest(BaseModel):
    # All monetary values must be integers in cents
    amount: int  # Total amount in cents (including fees)
    credit_amount: int = None  # Actual credit amount in cents (without fees)

@router.post("/create-checkout-session")
async def create_checkout_session(
    data: CheckoutRequest,
    user=Depends(require_role("brand"))
):
    # Validate the amount (in cents)
    if data.amount < 100:  # 100 cents = $1.00
        raise HTTPException(status_code=400, detail="Minimum amount is $1.00 (100 cents)")

    try:
        session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": data.amount,  # Already in cents
                    "product_data": {
                        "name": "AdMesh Wallet Top-up",
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url=f"{DOMAIN}/dashboard/brand/billing?success=true",
            cancel_url=f"{DOMAIN}/dashboard/brand/billing?canceled=true",
            metadata={
                "brand_id": user["uid"],
                "amount": str(data.credit_amount or data.amount)
            }
        )
        return { "session_url": session.url }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/webhook")
async def stripe_webhook(request: Request):
    payload = await request.body()
    sig_header = request.headers.get("stripe-signature")
    endpoint_secret = os.environ.get("STRIPE_WEBHOOK_SECRET")

    logger.info("Received webhook request")

    try:
        event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        logger.info(f"Webhook event type: {event['type']}")
    except stripe.error.SignatureVerificationError:
        logger.error("Invalid signature for webhook")
        raise HTTPException(status_code=400, detail="Invalid signature")

    # Handle one-time payment checkout completion
    if event["type"] == "checkout.session.completed":
        session = event["data"]["object"]
        metadata = session.get("metadata", {})
        logger.info(f"Session metadata: {metadata}")

        # Check if this is a subscription checkout
        if session.get("mode") == "subscription":
            await _handle_subscription_checkout(session)
        else:
            # Handle regular one-time payment
            await _handle_payment_checkout(session)

    # Handle subscription events
    elif event["type"] == "customer.subscription.created":
        subscription = event["data"]["object"]
        await _handle_subscription_created(subscription)

    elif event["type"] == "customer.subscription.updated":
        subscription = event["data"]["object"]
        await _handle_subscription_updated(subscription)

    elif event["type"] == "customer.subscription.deleted":
        subscription = event["data"]["object"]
        await _handle_subscription_deleted(subscription)

    elif event["type"] == "invoice.payment_succeeded":
        invoice = event["data"]["object"]
        if invoice.get("subscription"):
            await _handle_subscription_payment_succeeded(invoice)

    elif event["type"] == "invoice.payment_failed":
        invoice = event["data"]["object"]
        if invoice.get("subscription"):
            await _handle_subscription_payment_failed(invoice)

    return {"status": "success"}

# Helper function to handle one-time payment checkout
async def _handle_payment_checkout(session):
    metadata = session.get("metadata", {})

    try:
        brand_id = metadata["brand_id"]
        # The amount is already in cents from the metadata
        # Make sure it's an integer to avoid validation errors
        amount = int(float(metadata["amount"]))
        logger.info(f"Parsed amount in cents: {amount}")
    except (KeyError, ValueError, TypeError) as e:
        logger.error(f"Invalid metadata: {e}")
        raise HTTPException(status_code=400, detail="Invalid metadata in webhook")

    payments_ref = db.collection("payments").document(brand_id)
    payments_doc = payments_ref.get()

    # Add transaction entry
    # All monetary values are stored in cents in the database
    tx_data = {
        "type": "add_funds",
        "amount": amount,  # Amount in cents
        "timestamp": firestore.SERVER_TIMESTAMP,
    }

    db.collection("payments").document(brand_id).collection("transactions").add(tx_data)

    # Update or create payments summary
    # All monetary values are stored in cents in the database
    if payments_doc.exists:
        try:
            payments_ref.update({
                "total_balance": firestore.Increment(amount),  # Increment balance by amount in cents
                "updated_at": firestore.SERVER_TIMESTAMP,
            })
        except Exception as e:
            logger.error(f"Update failed: {e}")
            raise HTTPException(status_code=500, detail="Update failed")
    else:
        payments_ref.set({
            "uid": brand_id,
            "total_balance": amount,  # Amount in cents
            "total_spent": 0,  # In cents
            "current_total_budget": 0,  # In cents
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    logger.info(f"Transaction completed for brand_id {brand_id}, amount: {amount}")

# Helper function to handle subscription checkout
async def _handle_subscription_checkout(session):
    metadata = session.get("metadata", {})

    try:
        brand_id = metadata["brand_id"]
        plan_id = metadata["plan_id"]
        billing_cycle = metadata.get("billing_cycle", "monthly")
        logger.info(f"Subscription checkout for brand_id {brand_id}, plan_id: {plan_id}, billing_cycle: {billing_cycle}")
    except (KeyError, ValueError, TypeError) as e:
        logger.error(f"Invalid metadata: {e}")
        raise HTTPException(status_code=400, detail="Invalid metadata in webhook")

    # Get the subscription ID from the session
    subscription_id = session.get("subscription")
    if not subscription_id:
        logger.error("No subscription ID found in session")
        return

    # Get the customer ID from the session
    customer_id = session.get("customer")
    if not customer_id:
        logger.error("No customer ID found in session")
        return

    # Get the subscription details from Stripe
    try:
        subscription = stripe.Subscription.retrieve(subscription_id)
    except Exception as e:
        logger.error(f"Error retrieving subscription: {str(e)}")
        return

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        logger.error(f"Brand not found: {brand_id}")
        return

    # Get the plan
    try:
        plan = SubscriptionPlan.get_plan_by_id(plan_id)
    except ValueError:
        logger.error(f"Invalid plan ID: {plan_id}")
        return

    # Get the current subscription if it exists
    brand_data = brand_doc.to_dict()
    previous_plan_id = None
    if "subscription" in brand_data:
        previous_plan_id = brand_data["subscription"].get("plan_id")

    # Create the subscription object
    subscription_data = BrandSubscription(
        plan_id=plan_id,
        billing_cycle=billing_cycle,
        status=subscription.status,
        stripe_subscription_id=subscription_id,
        stripe_customer_id=customer_id,
        current_period_start=datetime.fromtimestamp(subscription.current_period_start),
        current_period_end=datetime.fromtimestamp(subscription.current_period_end),
        cancel_at_period_end=subscription.cancel_at_period_end,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        previous_plan_id=previous_plan_id,
        promo_credit_applied=False
    )

    # Update promo credit based on plan upgrade
    if previous_plan_id == "free" and (plan_id == "starter" or plan_id == "growth"):
        # Get brand creation date
        created_at = brand_data.get("created_at")
        if isinstance(created_at, firestore.SERVER_TIMESTAMP):
            # Handle SERVER_TIMESTAMP by using current time (this should be rare)
            created_at = datetime.now()

        # Get or initialize promo credit
        promo = brand_data.get("promo", {})
        if not promo:
            # Initialize promo credit for free plan if it doesn't exist
            promo = PromoCredit.create_free_plan_promo(created_at)

        # Update promo credit based on plan
        if plan_id == "starter":
            promo = PromoCredit.update_for_starter_plan(promo, created_at)
        elif plan_id == "growth":
            promo = PromoCredit.update_for_growth_plan(promo, created_at)

        # Update the brand document with updated promo credit
        brand_ref.update({
            "subscription": subscription_data.model_dump(),
            "updated_at": firestore.SERVER_TIMESTAMP,
            "promo": promo
        })
    else:
        # Just update the subscription without changing promo credit
        brand_ref.update({
            "subscription": subscription_data.model_dump(),
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    logger.info(f"Subscription created for brand_id {brand_id}, plan_id: {plan_id}")

# Helper function to handle subscription created event
async def _handle_subscription_created(subscription):
    # This is handled by the checkout.session.completed event
    pass

# Helper function to handle subscription updated event
async def _handle_subscription_updated(subscription):
    # Get the customer ID
    customer_id = subscription.get("customer")
    if not customer_id:
        logger.error("No customer ID found in subscription")
        return

    # Find the brand with this customer ID
    brands_ref = db.collection("brands")
    query = brands_ref.where("subscription.stripe_customer_id", "==", customer_id)
    brands = query.stream()

    brand_doc = None
    for doc in brands:
        brand_doc = doc
        break

    if not brand_doc:
        logger.error(f"No brand found with customer ID: {customer_id}")
        return

    brand_id = brand_doc.id
    brand_data = brand_doc.to_dict()

    # Update the subscription data
    if "subscription" not in brand_data:
        logger.error(f"No subscription found for brand: {brand_id}")
        return

    current_subscription = brand_data["subscription"]

    # Update the subscription fields
    updated_subscription = {
        **current_subscription,
        "status": subscription.status,
        "current_period_start": datetime.fromtimestamp(subscription.current_period_start).isoformat(),
        "current_period_end": datetime.fromtimestamp(subscription.current_period_end).isoformat(),
        "cancel_at_period_end": subscription.cancel_at_period_end,
        "updated_at": datetime.now().isoformat()
    }

    # Update the brand document
    db.collection("brands").document(brand_id).update({
        "subscription": updated_subscription,
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    logger.info(f"Subscription updated for brand_id {brand_id}")

# Helper function to handle subscription deleted event
async def _handle_subscription_deleted(subscription):
    # Get the customer ID
    customer_id = subscription.get("customer")
    if not customer_id:
        logger.error("No customer ID found in subscription")
        return

    # Find the brand with this customer ID
    brands_ref = db.collection("brands")
    query = brands_ref.where("subscription.stripe_customer_id", "==", customer_id)
    brands = query.stream()

    brand_doc = None
    for doc in brands:
        brand_doc = doc
        break

    if not brand_doc:
        logger.error(f"No brand found with customer ID: {customer_id}")
        return

    brand_id = brand_doc.id

    # Create a free subscription
    free_subscription = BrandSubscription(
        plan_id="free",
        status="active",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

    # Update the brand document
    db.collection("brands").document(brand_id).update({
        "subscription": free_subscription.model_dump(),
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    logger.info(f"Subscription deleted for brand_id {brand_id}, downgraded to free plan")

# Helper function to handle subscription payment succeeded event
async def _handle_subscription_payment_succeeded(invoice):
    # Get the customer ID
    customer_id = invoice.get("customer")
    if not customer_id:
        logger.error("No customer ID found in invoice")
        return

    # Find the brand with this customer ID
    brands_ref = db.collection("brands")
    query = brands_ref.where("subscription.stripe_customer_id", "==", customer_id)
    brands = query.stream()

    brand_doc = None
    for doc in brands:
        brand_doc = doc
        break

    if not brand_doc:
        logger.error(f"No brand found with customer ID: {customer_id}")
        return

    brand_id = brand_doc.id

    # Add transaction entry for the payment
    tx_data = {
        "type": "subscription_payment",
        "amount": invoice.amount_paid,  # Amount in cents
        "timestamp": firestore.SERVER_TIMESTAMP,
        "description": f"Monthly subscription payment"
    }

    db.collection("payments").document(brand_id).collection("transactions").add(tx_data)
    logger.info(f"Subscription payment recorded for brand_id {brand_id}, amount: {invoice.amount_paid}")

# Helper function to handle subscription payment failed event
async def _handle_subscription_payment_failed(invoice):
    # Get the customer ID
    customer_id = invoice.get("customer")
    if not customer_id:
        logger.error("No customer ID found in invoice")
        return

    # Find the brand with this customer ID
    brands_ref = db.collection("brands")
    query = brands_ref.where("subscription.stripe_customer_id", "==", customer_id)
    brands = query.stream()

    brand_doc = None
    for doc in brands:
        brand_doc = doc
        break

    if not brand_doc:
        logger.error(f"No brand found with customer ID: {customer_id}")
        return

    brand_id = brand_doc.id
    brand_data = brand_doc.to_dict()

    # Update the subscription status
    if "subscription" not in brand_data:
        logger.error(f"No subscription found for brand: {brand_id}")
        return

    current_subscription = brand_data["subscription"]

    # Update the subscription status to past_due
    updated_subscription = {
        **current_subscription,
        "status": "past_due",
        "updated_at": datetime.now().isoformat()
    }

    # Update the brand document
    db.collection("brands").document(brand_id).update({
        "subscription": updated_subscription,
        "updated_at": firestore.SERVER_TIMESTAMP
    })

    logger.info(f"Subscription payment failed for brand_id {brand_id}, status updated to past_due")
@router.get("/wallet")
async def get_wallet(user=Depends(require_role("brand"))):
    brand_id = user["uid"]

    # Get wallet data from wallets collection
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # If no wallet document exists yet, create a new one
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        # Create the wallet document with default values
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0.0,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)

        return {
            "wallet_balance": 0,  # For backward compatibility
            "total_spent": 0,  # For backward compatibility
            "current_total_budget": 0,  # For backward compatibility
            "total_available_balance": 0,
            "total_promo_available_balance": 0,
            "total_promo_balance_spent": 0,
            "total_balance_spent": 0,
            "total_budget_allocated": 0
        }
    else:
        # Get data from wallet document
        wallet_data = wallet_doc.to_dict()

        # Return both legacy fields and new wallet fields
        return {
            # Legacy fields for backward compatibility
            "wallet_balance": wallet_data.get("total_available_balance", 0),
            "total_spent": wallet_data.get("total_balance_spent", 0),
            "current_total_budget": wallet_data.get("total_budget_allocated", 0),

            # New wallet fields
            "total_available_balance": wallet_data.get("total_available_balance", 0),
            "total_promo_available_balance": wallet_data.get("total_promo_available_balance", 0),
            "total_promo_balance_spent": wallet_data.get("total_promo_balance_spent", 0),
            "total_balance_spent": wallet_data.get("total_balance_spent", 0),
            "total_budget_allocated": wallet_data.get("total_budget_allocated", 0)
        }


@router.get("/transactions")
async def get_transactions(user=Depends(require_role("brand"))):
    brand_id = user["uid"]

    # Get transactions from the wallet's transactions subcollection
    tx_ref = db.collection("wallets").document(brand_id).collection("transactions")
    tx_docs = tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING).stream()

    transactions = []
    for doc in tx_docs:
        tx = doc.to_dict()
        tx["id"] = doc.id

        # Convert timestamp to a serializable format
        if "timestamp" in tx and tx["timestamp"] is not None:
            # Check if it's a Firestore timestamp
            if hasattr(tx["timestamp"], "seconds"):
                # Convert to seconds since epoch
                tx["timestamp"] = {
                    "seconds": tx["timestamp"].seconds,
                    "nanoseconds": tx["timestamp"].nanoseconds
                }

        # If there's a reference_id and reference_type is "offer", try to get the offer title
        if tx.get("reference_id") and tx.get("reference_type") == "offer":
            try:
                offer_ref = db.collection("offers").document(tx["reference_id"])
                offer_doc = offer_ref.get()
                if offer_doc.exists:
                    offer_data = offer_doc.to_dict()
                    tx["offer_title"] = offer_data.get("title", "Unknown Offer")
            except Exception as e:
                logger.error(f"Error fetching offer details: {str(e)}")
                # Continue even if we can't get the offer title

        transactions.append(tx)

    # For backward compatibility, also get transactions from the payments collection
    payment_tx_ref = db.collection("payments").document(brand_id).collection("transactions")
    payment_tx_docs = payment_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING).stream()

    for doc in payment_tx_docs:
        tx = doc.to_dict()
        tx["id"] = f"payment_{doc.id}"  # Add prefix to avoid ID conflicts
        tx["source"] = "payments"  # Add source to distinguish from wallet transactions

        # Convert timestamp to a serializable format
        if "timestamp" in tx and tx["timestamp"] is not None:
            # Check if it's a Firestore timestamp
            if hasattr(tx["timestamp"], "seconds"):
                # Convert to seconds since epoch
                tx["timestamp"] = {
                    "seconds": tx["timestamp"].seconds,
                    "nanoseconds": tx["timestamp"].nanoseconds
                }

        transactions.append(tx)

    # Sort all transactions by timestamp (newest first)
    transactions.sort(key=lambda x: (
        x.get("timestamp", {}).get("seconds", 0) if isinstance(x.get("timestamp"), dict) else 0
    ), reverse=True)

    return { "transactions": transactions }

# ----- Subscription Management Routes -----

@router.get("/subscription/plans")
async def get_subscription_plans():
    """Get all available subscription plans"""
    plans = SubscriptionPlan.get_all_plans()
    return {"plans": [plan.model_dump() for plan in plans]}

@router.get("/subscription")
async def get_subscription(user=Depends(require_role("brand"))):
    """Get the current brand's subscription"""
    brand_id = user["uid"]

    # Get subscription from the new structure
    sub_doc = await get_subscription_document(brand_id)
    plan = SubscriptionPlan.get_plan_by_id(sub_doc.plan_id)

    # Convert to legacy format for backward compatibility with frontend
    legacy_subscription = {
        "plan_id": sub_doc.plan_id,
        "billing_cycle": sub_doc.billing_cycle,
        "status": sub_doc.status,
        "stripe_subscription_id": sub_doc.references.stripe_subscription_id,
        "stripe_customer_id": sub_doc.references.stripe_customer_id,
        "current_period_start": sub_doc.current_period_start,
        "current_period_end": sub_doc.current_period_end,
        "cancel_at_period_end": sub_doc.cancel_at_period_end,
        "created_at": sub_doc.created_at,
        "updated_at": sub_doc.updated_at,
        "product_listings_used": sub_doc.usage.product_listings,
        "promo_credit_applied": sub_doc.promo_credit_applied,
        "promo_credit_amount_cents": sub_doc.promo_credit_amount_cents,
        "has_multi_user_access": sub_doc.has_multi_user_access,
        "multi_user_count": sub_doc.usage.multi_users,
        "has_cpa_optimization": sub_doc.has_cpa_optimization,
        "has_agent_outreach_tools": sub_doc.has_agent_outreach_tools
    }

    return {
        "subscription": legacy_subscription,
        "plan": plan.model_dump(),
        "subscription_v2": sub_doc.model_dump()
    }

@router.post("/subscription/create-checkout")
async def create_subscription_checkout(
    plan_id: str,
    billing_cycle: Optional[str] = "monthly",
    user=Depends(require_role("brand"))
):
    """Create a checkout session for a subscription"""
    brand_id = user["uid"]

    # Validate plan ID
    try:
        # Determine if this is an annual plan
        is_annual = billing_cycle == "annual"

        # Get the appropriate plan based on ID and billing cycle
        if plan_id == "starter":
            plan = SubscriptionPlan.get_starter_plan(annual=is_annual)
        elif plan_id == "growth":
            plan = SubscriptionPlan.get_growth_plan(annual=is_annual)
        elif plan_id == "free":
            plan = SubscriptionPlan.get_free_plan()
        else:
            # Handle direct annual plan IDs
            plan = SubscriptionPlan.get_plan_by_id(plan_id)

    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid plan ID")

    # Free plan doesn't need checkout
    if plan.billing_cycle == "free":
        raise HTTPException(status_code=400, detail="Free plan doesn't require checkout")

    # Get brand data
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Check if brand already has a Stripe customer ID
    stripe_customer_id = None
    if "subscription" in brand_data and brand_data["subscription"].get("stripe_customer_id"):
        stripe_customer_id = brand_data["subscription"]["stripe_customer_id"]

    try:
        # Create or get Stripe product for the plan
        billing_suffix = "Annual" if plan.billing_cycle == "annual" else "Monthly"
        product_name = f"AdMesh {plan.name} Plan ({billing_suffix})"
        product_description = f"{plan.name} {billing_suffix.lower()} subscription plan for AdMesh"

        # Check if product already exists
        existing_products = stripe.Product.list(active=True)
        product_id = None

        for product in existing_products.data:
            if product.name == product_name:
                product_id = product.id
                break

        # Create product if it doesn't exist
        if not product_id:
            product = stripe.Product.create(
                name=product_name,
                description=product_description
            )
            product_id = product.id

        # Create price for the product with appropriate interval
        if plan.billing_cycle == "annual":
            price = stripe.Price.create(
                product=product_id,
                unit_amount=plan.price_annual_cents,
                currency="usd",
                recurring={"interval": "year"}
            )
        else:
            price = stripe.Price.create(
                product=product_id,
                unit_amount=plan.price_monthly_cents,
                currency="usd",
                recurring={"interval": "month"}
            )

        # Create checkout session
        checkout_params = {
            "payment_method_types": ["card"],
            "line_items": [{
                "price": price.id,
                "quantity": 1
            }],
            "mode": "subscription",
            "success_url": f"{DOMAIN}/dashboard/brand/billing?subscription_success=true&plan={plan.id}",
            "cancel_url": f"{DOMAIN}/dashboard/brand/billing?subscription_canceled=true",
            "metadata": {
                "brand_id": brand_id,
                "plan_id": plan.id,
                "billing_cycle": plan.billing_cycle
            }
        }

        # Add customer ID if it exists
        if stripe_customer_id:
            checkout_params["customer"] = stripe_customer_id

        session = stripe.checkout.Session.create(**checkout_params)

        return {"session_url": session.url}

    except Exception as e:
        logger.error(f"Error creating subscription checkout: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/subscription/cancel")
async def cancel_subscription(user=Depends(require_role("brand"))):
    """Cancel the current brand's subscription at the end of the billing period"""
    brand_id = user["uid"]

    # Get brand data
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Check if brand has an active subscription
    if "subscription" not in brand_data:
        raise HTTPException(status_code=400, detail="No active subscription found")

    subscription = brand_data["subscription"]

    # Check if subscription has a Stripe subscription ID
    if not subscription.get("stripe_subscription_id"):
        raise HTTPException(status_code=400, detail="No Stripe subscription found")

    try:
        # Cancel subscription at period end
        stripe.Subscription.modify(
            subscription["stripe_subscription_id"],
            cancel_at_period_end=True
        )

        # Update subscription in Firestore
        brand_ref.update({
            "subscription.cancel_at_period_end": True,
            "subscription.updated_at": firestore.SERVER_TIMESTAMP
        })

        return {"status": "success", "message": "Subscription will be canceled at the end of the billing period"}

    except Exception as e:
        logger.error(f"Error canceling subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/subscription/reactivate")
async def reactivate_subscription(user=Depends(require_role("brand"))):
    """Reactivate a canceled subscription that hasn't expired yet"""
    brand_id = user["uid"]

    # Get brand data
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Check if brand has a subscription
    if "subscription" not in brand_data:
        raise HTTPException(status_code=400, detail="No subscription found")

    subscription = brand_data["subscription"]

    # Check if subscription is set to cancel
    if not subscription.get("cancel_at_period_end", False):
        raise HTTPException(status_code=400, detail="Subscription is not scheduled for cancellation")

    # Check if subscription has a Stripe subscription ID
    if not subscription.get("stripe_subscription_id"):
        raise HTTPException(status_code=400, detail="No Stripe subscription found")

    try:
        # Reactivate subscription
        stripe.Subscription.modify(
            subscription["stripe_subscription_id"],
            cancel_at_period_end=False
        )

        # Update subscription in Firestore
        brand_ref.update({
            "subscription.cancel_at_period_end": False,
            "subscription.updated_at": firestore.SERVER_TIMESTAMP
        })

        return {"status": "success", "message": "Subscription reactivated successfully"}

    except Exception as e:
        logger.error(f"Error reactivating subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Import the promo credit model
from api.models.promo import PromoCredit
from datetime import datetime

# Update the webhook handler to process subscription events
@router.post("/subscription/apply-promo-credit")
async def apply_promo_credit(user=Depends(require_role("brand"))):
    """Apply the one-time promo credit for the current subscription plan"""
    brand_id = user["uid"]

    # Get brand data
    brand_ref = db.collection("brands").document(brand_id)
    brand_doc = brand_ref.get()

    if not brand_doc.exists:
        raise HTTPException(status_code=404, detail="Brand not found")

    brand_data = brand_doc.to_dict()

    # Check if brand has a subscription
    if "subscription" not in brand_data:
        raise HTTPException(status_code=400, detail="No subscription found")

    subscription = brand_data["subscription"]
    plan_id = subscription.get("plan_id", "free")

    # Check if promo credit has already been applied
    if subscription.get("promo_credit_applied", False):
        raise HTTPException(status_code=400, detail="Promo credit has already been applied")

    # Get the plan
    try:
        plan = SubscriptionPlan.get_plan_by_id(plan_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid plan ID")

    # Get brand creation date and promo eligibility deadline
    created_at = brand_data.get("created_at")
    if isinstance(created_at, firestore.SERVER_TIMESTAMP):
        # Handle SERVER_TIMESTAMP by using current time (this should be rare)
        created_at = datetime.now()

    # Get or initialize promo credit
    promo = brand_data.get("promo", {})
    if not promo:
        # Initialize promo credit for free plan if it doesn't exist
        promo = PromoCredit.create_free_plan_promo(created_at)

    # Update promo credit based on plan
    if plan_id == "starter":
        promo = PromoCredit.update_for_starter_plan(promo, created_at)
    elif plan_id == "growth":
        promo = PromoCredit.update_for_growth_plan(promo, created_at)

    # We're using a fixed amount of $50 (5000 cents) for all promo credits
    # No need to calculate based on the plan anymore

    # Add promo credit to wallet
    payments_ref = db.collection("payments").document(brand_id)
    payments_doc = payments_ref.get()

    # Add transaction entry with specific description based on plan
    # For all plans, we'll use the same fixed values as requested
    tx_data = {
        "type": "promo_credit",
        "amount": 5000,  # Fixed amount of 5000 cents ($50)
        "timestamp": firestore.SERVER_TIMESTAMP,
        "description": "Promo credit for Free plan"  # Use the same description for all plans
    }

    db.collection("payments").document(brand_id).collection("transactions").add(tx_data)

    # Update or create payments summary with fixed amount
    fixed_amount = 5000  # $50 in cents

    if payments_doc.exists:
        payments_ref.update({
            "total_balance": firestore.Increment(fixed_amount),
            "updated_at": firestore.SERVER_TIMESTAMP,
        })
    else:
        payments_ref.set({
            "uid": brand_id,
            "total_balance": fixed_amount,
            "total_spent": 0,
            "current_total_budget": 0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

    # Mark promo credit as applied in subscription with fixed amount
    fixed_amount = 5000  # $50 in cents

    brand_ref.update({
        "subscription.promo_credit_applied": True,
        "subscription.promo_credit_amount_cents": fixed_amount,
        "subscription.updated_at": firestore.SERVER_TIMESTAMP,
        # Update the promo field with the latest promo credit info
        "promo": promo
    })

    return {
        "status": "success",
        "message": f"Promo credit of $50 applied successfully",
        "amount": fixed_amount
    }