#!/usr/bin/env python3
"""
Environment validation script for AdMesh Protocol
Validates that all required environment variables and configurations are properly set
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any

# Add the parent directory to the path so we can import our config
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config.config_manager import get_config, get_environment
    from firebase.config import initialize_firebase
except ImportError as e:
    print(f"❌ Failed to import configuration modules: {e}")
    sys.exit(1)

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def validate_environment_variables(config) -> List[str]:
    """Validate that all required environment variables are set"""
    errors = []

    try:
        required_vars = config.get_required_env_vars()
        for var in required_vars:
            if not os.getenv(var):
                errors.append(f"Missing required environment variable: {var}")
    except Exception as e:
        errors.append(f"Error checking required environment variables: {e}")

    return errors


def validate_firebase_config(config) -> List[str]:
    """Validate Firebase configuration"""
    errors = []

    try:
        firebase_config = config.firebase_config
        required_fields = ['project_id', 'api_key', 'auth_domain', 'storage_bucket']

        for field in required_fields:
            if not firebase_config.get(field):
                errors.append(f"Missing Firebase configuration field: {field}")

        # Test Firebase initialization
        try:
            initialize_firebase()
            logger.info("✅ Firebase initialization successful")
        except Exception as e:
            errors.append(f"Firebase initialization failed: {e}")

    except Exception as e:
        errors.append(f"Error validating Firebase configuration: {e}")

    return errors


def validate_api_configuration(config) -> List[str]:
    """Validate API configuration"""
    errors = []

    try:
        # Check API base URL
        api_url = config.api_base_url
        if not api_url:
            errors.append("Missing API base URL")
        elif not api_url.startswith(('http://', 'https://')):
            errors.append(f"Invalid API base URL format: {api_url}")

        # Check CORS origins
        cors_origins = config.cors_origins
        if not cors_origins:
            errors.append("No CORS origins configured")

    except Exception as e:
        errors.append(f"Error validating API configuration: {e}")

    return errors


def validate_external_services(config) -> List[str]:
    """Validate external service configurations"""
    errors = []

    try:
        if hasattr(config, 'external_services'):
            external_services = config.external_services

            # Check OpenAI configuration
            openai_config = external_services.get('openai', {})
            if not openai_config.get('api_key'):
                errors.append("Missing OpenAI API key")

            # Check Stripe configuration (only for production)
            if config.environment == 'production':
                stripe_config = external_services.get('stripe', {})
                if not stripe_config.get('api_key'):
                    errors.append("Missing Stripe API key")
                if not stripe_config.get('webhook_secret'):
                    errors.append("Missing Stripe webhook secret")

            # Check Resend configuration
            resend_config = external_services.get('resend', {})
            if not resend_config.get('api_key'):
                errors.append("Missing Resend API key")

    except Exception as e:
        errors.append(f"Error validating external services: {e}")

    return errors


def validate_security_configuration(config) -> List[str]:
    """Validate security configuration"""
    errors = []

    try:
        if config.environment == 'production':
            if hasattr(config, 'security_config'):
                security_config = config.security_config

                if not security_config.get('ssl_required'):
                    errors.append("SSL should be required in production")

                if not security_config.get('secure_cookies'):
                    errors.append("Secure cookies should be enabled in production")

    except Exception as e:
        errors.append(f"Error validating security configuration: {e}")

    return errors


def print_configuration_summary(config):
    """Print a summary of the current configuration"""
    print("\n" + "="*60)
    print("CONFIGURATION SUMMARY")
    print("="*60)
    print(f"Environment: {config.environment}")
    print(f"Debug Mode: {config.debug}")
    print(f"Log Level: {config.log_level}")
    print(f"Port: {config.port}")
    print(f"API Base URL: {config.api_base_url}")
    print(f"Frontend URL: {config.frontend_url}")
    print(f"Firebase Project: {config.firebase_config.get('project_id')}")
    print(f"CORS Origins: {len(config.cors_origins)} configured")

    if hasattr(config, 'feature_flags'):
        print("\nFeature Flags:")
        for flag, enabled in config.feature_flags.items():
            print(f"  {flag}: {'✅' if enabled else '❌'}")

    print("="*60)


def main():
    """Main validation function"""
    print("🔍 Validating AdMesh Protocol Environment Configuration")
    print(f"Current working directory: {os.getcwd()}")

    try:
        # Load configuration
        config = get_config()
        environment = get_environment()

        print(f"📦 Loaded configuration for environment: {environment}")

        # Print configuration summary
        print_configuration_summary(config)

        # Run all validations
        all_errors = []

        print("\n🔍 Running validation checks...")

        # Validate environment variables
        env_errors = validate_environment_variables(config)
        all_errors.extend(env_errors)
        if env_errors:
            print("❌ Environment variables validation failed")
            for error in env_errors:
                print(f"   - {error}")
        else:
            print("✅ Environment variables validation passed")

        # Validate Firebase configuration
        firebase_errors = validate_firebase_config(config)
        all_errors.extend(firebase_errors)
        if firebase_errors:
            print("❌ Firebase configuration validation failed")
            for error in firebase_errors:
                print(f"   - {error}")
        else:
            print("✅ Firebase configuration validation passed")

        # Validate API configuration
        api_errors = validate_api_configuration(config)
        all_errors.extend(api_errors)
        if api_errors:
            print("❌ API configuration validation failed")
            for error in api_errors:
                print(f"   - {error}")
        else:
            print("✅ API configuration validation passed")

        # Validate external services
        service_errors = validate_external_services(config)
        all_errors.extend(service_errors)
        if service_errors:
            print("❌ External services validation failed")
            for error in service_errors:
                print(f"   - {error}")
        else:
            print("✅ External services validation passed")

        # Validate security configuration
        security_errors = validate_security_configuration(config)
        all_errors.extend(security_errors)
        if security_errors:
            print("❌ Security configuration validation failed")
            for error in security_errors:
                print(f"   - {error}")
        else:
            print("✅ Security configuration validation passed")

        # Final result
        print("\n" + "="*60)
        if all_errors:
            print(f"❌ VALIDATION FAILED - {len(all_errors)} error(s) found")
            print("\nErrors:")
            for i, error in enumerate(all_errors, 1):
                print(f"{i}. {error}")
            sys.exit(1)
        else:
            print("✅ ALL VALIDATIONS PASSED")
            print("Environment configuration is valid and ready for use!")
        print("="*60)

    except Exception as e:
        print(f"❌ Fatal error during validation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
