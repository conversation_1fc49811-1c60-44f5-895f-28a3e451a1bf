#!/usr/bin/env python3
"""
Test script to verify Firebase connection
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Test Firebase connection"""
    logger.info("Testing Firebase connection...")

    # Initialize Firebase
    try:
        # Check if Firebase is already initialized
        try:
            app = firebase_admin.get_app()
            logger.info("Firebase already initialized")
        except ValueError:
            # Initialize Firebase
            logger.info("Initializing Firebase...")

            # Check if service account key file exists
            service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
            if os.path.exists(service_account_path):
                # Use the service account key file
                cred = credentials.Certificate(service_account_path)
                firebase_admin.initialize_app(cred)
                logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
            else:
                # For local development, use the default app configuration
                firebase_admin.initialize_app()
                logger.info("Initialized Firebase with default configuration")

        # Get Firestore client
        logger.info("Getting Firestore client...")
        db = firestore.client()
        logger.info("Firestore client created successfully")

        # Test a simple query
        logger.info("Testing a simple query...")
        brands_ref = db.collection("brands")
        brands = brands_ref.limit(1).stream()

        # Check if we got any results
        brand_count = 0
        for brand in brands:
            brand_count += 1
            logger.info(f"Found brand: {brand.id}")

        if brand_count == 0:
            logger.warning("No brands found in the database")

        logger.info("Firebase connection test completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error testing Firebase connection: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
