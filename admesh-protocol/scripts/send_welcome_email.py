"""
<PERSON><PERSON><PERSON> to send a welcome email to a newly registered user.

Usage:
    python scripts/send_welcome_email.py --email <EMAIL> --name "User Name"
"""
import os
import sys
import logging
import argparse
import resend
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")
# Log that we're using the API key (without showing any part of it)
logger.info("Using Resend API key from environment")

def send_welcome_email(email, first_name=None):
    """
    Send a welcome email to a newly registered user

    Args:
        email (str): The user's email address
        first_name (str, optional): The user's first name

    Returns:
        dict: The response from the Resend API
    """
    try:
        # Read the welcome email template
        template_path = os.path.join(os.path.dirname(__file__), '..', 'templates', 'email_welcome.html')
        with open(template_path, 'r') as file:
            html_content = file.read()

        # Replace the first_name placeholder if provided
        if first_name:
            html_content = html_content.replace('{{ first_name | default: "there" }}', first_name)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Welcome to AdMesh!",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Welcome email sent to {email}")
        return response

    except Exception as e:
        logger.error(f"Error sending welcome email: {str(e)}")
        raise e

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Send a welcome email to a new user')
    parser.add_argument('--email', required=True, help='User email address')
    parser.add_argument('--name', help='User first name')
    args = parser.parse_args()

    try:
        # Send welcome email
        response = send_welcome_email(args.email, args.name)
        logger.info(f"Email sent with ID: {response.get('id', 'unknown')}")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
