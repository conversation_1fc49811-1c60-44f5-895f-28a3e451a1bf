"""
<PERSON><PERSON><PERSON> to create the initial "Registered Users" audience in Resend
and add all existing users to it.

Usage:
    python scripts/create_audience.py
"""
import os
import sys
import asyncio
import logging
import resend
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import from the project
from firebase.config import get_db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")

# Initialize Firestore
db = get_db()

async def get_all_users():
    """
    Get all registered users from Firebase
    """
    try:
        # Get all users from Firestore
        users_ref = db.collection("users").stream()
        users = []

        for user_doc in users_ref:
            user_data = user_doc.to_dict()
            if "email" in user_data:
                user_info = {
                    "email": user_data["email"],
                    "data": {
                        "uid": user_data["uid"],
                        "role": user_data.get("role", "user")
                    }
                }

                # Add name if available
                if "name" in user_data:
                    name_parts = user_data["name"].split(" ", 1)
                    user_info["first_name"] = name_parts[0]
                    if len(name_parts) > 1:
                        user_info["last_name"] = name_parts[1]

                # Add XP if available
                if "xp" in user_data:
                    user_info["data"]["xp"] = user_data["xp"]

                users.append(user_info)

        return users

    except Exception as e:
        logger.error(f"Error getting users: {str(e)}")
        raise e

async def add_users_to_audience(audience_id, users):
    """
    Add multiple users to an audience in Resend
    """
    try:
        added_count = 0
        failed_count = 0

        for user in users:
            try:
                # Create contact data with required format
                contact_data = {
                    "audience_id": audience_id,
                    "email": user["email"]
                }

                # Add optional fields if available
                if "first_name" in user:
                    contact_data["first_name"] = user["first_name"]
                if "last_name" in user:
                    contact_data["last_name"] = user["last_name"]
                if "data" in user:
                    contact_data["data"] = user["data"]

                contact_response = resend.Contacts.create(contact_data)
                logger.debug(f"Contact creation response: {contact_response}")
                added_count += 1
                logger.info(f"Added user {user['email']} to audience")

                # Add a delay to avoid rate limiting (Resend allows 2 requests per second)
                time.sleep(0.6)  # Sleep for 600ms to stay under the limit
            except Exception as e:
                logger.error(f"Error adding user {user['email']} to audience: {str(e)}")
                failed_count += 1

        return {
            "added": added_count,
            "failed": failed_count,
            "total": len(users)
        }

    except Exception as e:
        logger.error(f"Error adding users to audience: {str(e)}")
        raise e

async def main():
    try:
        # Log the API key being used (first 10 characters only for security)
        api_key = resend.api_key
        logger.info(f"Using Resend API key: {api_key[:10]}...")

        # Check if audience already exists
        logger.info("Checking for existing audiences in Resend...")
        try:
            audiences_response = resend.Audiences.list()
            logger.info(f"Audiences response type: {type(audiences_response)}")
            logger.info(f"Audiences response: {audiences_response}")

            # Handle different response formats
            if isinstance(audiences_response, dict) and "data" in audiences_response:
                audiences = audiences_response["data"]
            elif isinstance(audiences_response, list):
                audiences = audiences_response
            else:
                logger.warning(f"Unexpected response format: {audiences_response}")
                audiences = []

            existing_audience = None

            for audience in audiences:
                if isinstance(audience, dict) and "name" in audience and audience["name"] == "Registered Users":
                    existing_audience = audience
                    break
        except Exception as e:
            logger.error(f"Error listing audiences: {str(e)}")
            audiences = []
            existing_audience = None

        if existing_audience:
            logger.info(f"Audience 'Registered Users' already exists with ID: {existing_audience['id']}")
            audience_id = existing_audience["id"]
        else:
            # Create audience in Resend
            logger.info("Creating new 'Registered Users' audience in Resend...")
            params = {
                "name": "Registered Users",
                "description": "All registered users of AdMesh"
            }

            try:
                audience_response = resend.Audiences.create(params)
                logger.info(f"Audience creation response type: {type(audience_response)}")
                logger.info(f"Audience creation response: {audience_response}")

                # Handle different response formats
                if isinstance(audience_response, dict):
                    if "id" in audience_response:
                        audience_id = audience_response["id"]
                    elif "data" in audience_response and isinstance(audience_response["data"], dict) and "id" in audience_response["data"]:
                        audience_id = audience_response["data"]["id"]
                    else:
                        raise ValueError(f"Could not find audience ID in response: {audience_response}")
                else:
                    raise ValueError(f"Unexpected response format: {audience_response}")
            except Exception as e:
                logger.error(f"Error creating audience: {str(e)}")
                raise e

            # No longer storing audience in Firestore
            logger.info("Audience created successfully in Resend")

            logger.info(f"Created audience 'Registered Users' with ID: {audience_id}")

        # Get all users
        logger.info("Getting list of users from Firebase...")
        users = await get_all_users()
        logger.info(f"Found {len(users)} users in Firebase")

        # Save users to a file for reference
        with open("users_list.txt", "w") as f:
            f.write(f"Total users: {len(users)}\n\n")
            for user in users:
                name_parts = []
                if "first_name" in user:
                    name_parts.append(user["first_name"])
                if "last_name" in user:
                    name_parts.append(user["last_name"])
                name = " ".join(name_parts) if name_parts else "N/A"

                f.write(f"Email: {user['email']}\n")
                f.write(f"Name: {name}\n")
                f.write(f"Role: {user['data']['role']}\n")
                if "xp" in user['data']:
                    f.write(f"XP: {user['data']['xp']}\n")
                f.write("\n")

        logger.info(f"User list saved to users_list.txt")

        # Add users to audience
        logger.info(f"Adding {len(users)} users to audience {audience_id}...")
        result = await add_users_to_audience(audience_id, users)

        logger.info(f"Added {result['added']} users to audience (failed: {result['failed']})")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
