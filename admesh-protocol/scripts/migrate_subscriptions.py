#!/usr/bin/env python3
"""
Migration script to:
1. Move subscriptions from the old structure to the new structure
2. Store subscription plans in the database
3. Remove the old subscription structure
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging
from datetime import datetime
from typing import Dict, Any, List

# Add the parent directory to the path so we can import from api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.models.subscription import (
    SubscriptionPlan, SubscriptionDocument, SubscriptionLimits,
    SubscriptionUsage, SubscriptionReferences, convert_plan_to_limits
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Firebase
try:
    firebase_admin.get_app()
except ValueError:
    # Check if service account key file exists
    service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
    if os.path.exists(service_account_path):
        # Use the service account key file
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred)
        logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
    elif os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
        # Use the application default credentials
        cred = credentials.ApplicationDefault()
        firebase_admin.initialize_app(cred, {
            'projectId': os.environ.get('GOOGLE_CLOUD_PROJECT', 'admesh-9560c'),
        })
        logger.info("Initialized Firebase with application default credentials")
    else:
        # For local development, use the default app configuration
        firebase_admin.initialize_app()
        logger.info("Initialized Firebase with default configuration")

db = firestore.client()

def get_all_brands() -> List[Dict[str, Any]]:
    """Get all brands from Firestore"""
    brands_ref = db.collection("brands")
    brands = brands_ref.stream()

    result = []
    for brand in brands:
        brand_data = brand.to_dict()
        brand_data['id'] = brand.id
        result.append(brand_data)

    return result

def migrate_subscription(brand_id: str, brand_data: Dict[str, Any]) -> bool:
    """Migrate a brand's subscription to the new structure"""
    try:
        # Check if the brand has a subscription in the old structure
        if "subscription" not in brand_data:
            logger.info(f"Brand {brand_id} has no subscription, creating default free plan")
            # Create a default free subscription
            plan = SubscriptionPlan.get_free_plan()
            sub_doc = SubscriptionDocument(
                id="current",
                plan_id="free",
                billing_cycle="free",
                status="active",
                created_at=datetime.now(),
                updated_at=datetime.now(),
                limits=convert_plan_to_limits(plan)
            )
        else:
            # Convert old subscription to new format
            old_sub = brand_data["subscription"]

            # Get the plan details to set limits
            try:
                plan_id = old_sub.get("plan_id", "free")
                plan = SubscriptionPlan.get_plan_by_id(plan_id)
                limits = convert_plan_to_limits(plan)
            except ValueError:
                # Fallback to default limits if plan_id is invalid
                logger.warning(f"Invalid plan ID: {plan_id}, using default limits")
                limits = SubscriptionLimits()

            # Create references
            references = SubscriptionReferences(
                stripe_subscription_id=old_sub.get("stripe_subscription_id"),
                stripe_customer_id=old_sub.get("stripe_customer_id")
            )

            # Create usage
            active_products = brand_data.get("active_products", [])
            usage = SubscriptionUsage(
                product_listings=len(active_products),
                multi_users=old_sub.get("multi_user_count", 0)
            )

            # Create the new subscription document
            sub_doc = SubscriptionDocument(
                id="current",
                plan_id=plan_id,
                billing_cycle=old_sub.get("billing_cycle", "monthly"),
                status=old_sub.get("status", "active"),
                current_period_start=old_sub.get("current_period_start"),
                current_period_end=old_sub.get("current_period_end"),
                cancel_at_period_end=old_sub.get("cancel_at_period_end", False),
                created_at=old_sub.get("created_at") or datetime.now(),
                updated_at=datetime.now(),
                previous_plan_id=old_sub.get("previous_plan_id"),
                limits=limits,
                usage=usage,
                references=references,
                promo_credit_applied=old_sub.get("promo_credit_applied", False),
                promo_credit_amount_cents=old_sub.get("promo_credit_amount_cents", 0),
                has_multi_user_access=old_sub.get("has_multi_user_access", False),
                has_cpa_optimization=old_sub.get("has_cpa_optimization", False),
                has_agent_outreach_tools=old_sub.get("has_agent_outreach_tools", False)
            )

        # Generate a unique ID for the subscription
        subscription_id = db.collection("brands").document(brand_id).collection("subscriptions").document().id

        # Save the new subscription document with the generated ID
        sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document(subscription_id)
        sub_doc.id = subscription_id  # Update the ID in the document
        sub_ref.set(sub_doc.model_dump(mode="json"))

        # Update the brand document with the current subscription ID
        brand_ref = db.collection("brands").document(brand_id)
        brand_ref.update({
            "currentSubscriptionId": subscription_id,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Successfully migrated subscription for brand {brand_id} with subscription ID: {subscription_id}")
        return True

    except Exception as e:
        logger.error(f"Error migrating subscription for brand {brand_id}: {str(e)}")
        return False

def store_subscription_plans():
    """Store all subscription plans in the database"""
    try:
        plans = SubscriptionPlan.get_all_plans()

        # Create a batch
        batch = db.batch()

        for plan in plans:
            plan_ref = db.collection("subscriptionPlans").document(plan.id)
            batch.set(plan_ref, plan.model_dump(mode="json"))

        # Commit the batch
        batch.commit()

        logger.info(f"Successfully stored {len(plans)} subscription plans in the database")
        return True

    except Exception as e:
        logger.error(f"Error storing subscription plans: {str(e)}")
        return False

def remove_old_subscription_structure(brand_id: str) -> bool:
    """Remove the old subscription structure from a brand document"""
    try:
        brand_ref = db.collection("brands").document(brand_id)

        # Update the brand document to remove the subscription field
        brand_ref.update({
            "subscription": firestore.DELETE_FIELD,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Successfully removed old subscription structure for brand {brand_id}")
        return True

    except Exception as e:
        logger.error(f"Error removing old subscription structure for brand {brand_id}: {str(e)}")
        return False

def main():
    """Main migration function"""
    logger.info("Starting subscription migration")

    # Store subscription plans in the database
    logger.info("Storing subscription plans in the database")
    store_subscription_plans()

    # Get all brands
    logger.info("Getting all brands")
    brands = get_all_brands()
    logger.info(f"Found {len(brands)} brands")

    # Migrate each brand's subscription
    success_count = 0
    for brand in brands:
        brand_id = brand['id']
        logger.info(f"Migrating subscription for brand {brand_id}")

        # Migrate the subscription
        if migrate_subscription(brand_id, brand):
            # Remove the old subscription structure
            if remove_old_subscription_structure(brand_id):
                success_count += 1

    logger.info(f"Migration completed. Successfully migrated {success_count} out of {len(brands)} brands")

if __name__ == "__main__":
    main()
