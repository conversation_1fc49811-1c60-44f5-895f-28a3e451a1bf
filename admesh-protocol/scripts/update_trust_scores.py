from google.cloud import firestore
from collections import defaultdict
from firebase.config import get_db

db = get_db()

def update_offer_trust_scores():
    click_counts = defaultdict(int)
    conversion_counts = defaultdict(int)

    # Step 1: Count clicks per offer
    for doc in db.collection("clicks").stream():
        data = doc.to_dict()
        click_counts[data["offer_id"]] += 1

    # Step 2: Count conversions per offer
    for doc in db.collection("conversions").stream():
        data = doc.to_dict()
        conversion_counts[data["offer_id"]] += 1

    # Step 3: Compute trust scores
    for offer_id in click_counts:
        clicks = click_counts[offer_id]
        conversions = conversion_counts.get(offer_id, 0)
        cr = conversions / clicks if clicks > 0 else 0

        trust_score = round(50 + (cr * 100) - (5 if cr < 0.02 else 0), 2)

        print(f"[{offer_id}] CR={cr:.2%}, Score={trust_score}")

        db.collection("offers").document(offer_id).update({
            "trust_score": trust_score
        })

if __name__ == "__main__":
    update_offer_trust_scores()
