#!/usr/bin/env python3
"""
Scrip<PERSON> to remove the duplicate subscription_plans collection.
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase"""
    try:
        firebase_admin.get_app()
    except ValueError:
        # Check if service account key file exists
        service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
        if os.path.exists(service_account_path):
            # Use the service account key file
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred)
            logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
        else:
            # For local development, use the default app configuration
            firebase_admin.initialize_app()
            logger.info("Initialized Firebase with default configuration")

def remove_duplicate_collection():
    """Remove the duplicate subscription_plans collection"""
    db = firestore.client()
    
    # Check if both collections exist
    subscription_plans_ref = db.collection("subscription_plans")
    subscription_plans_docs = list(subscription_plans_ref.limit(10).stream())
    
    subscriptionPlans_ref = db.collection("subscriptionPlans")
    subscriptionPlans_docs = list(subscriptionPlans_ref.limit(10).stream())
    
    if not subscription_plans_docs:
        logger.info("No documents found in subscription_plans collection. Nothing to remove.")
        return True
    
    if not subscriptionPlans_docs:
        logger.warning("No documents found in subscriptionPlans collection. Cannot remove subscription_plans collection.")
        return False
    
    # Delete all documents in the subscription_plans collection
    logger.info("Deleting documents in subscription_plans collection...")
    
    # Get all documents in the collection
    docs = subscription_plans_ref.stream()
    
    # Create a batch
    batch = db.batch()
    count = 0
    
    for doc in docs:
        batch.delete(doc.reference)
        count += 1
        
        # Firestore batches can only contain up to 500 operations
        if count >= 500:
            batch.commit()
            batch = db.batch()
            count = 0
    
    # Commit any remaining operations
    if count > 0:
        batch.commit()
    
    logger.info(f"Successfully deleted {count} documents from subscription_plans collection")
    return True

def main():
    """Main function"""
    initialize_firebase()
    
    logger.info("Removing duplicate subscription_plans collection...")
    success = remove_duplicate_collection()
    
    if success:
        logger.info("Successfully removed duplicate subscription_plans collection")
        return 0
    else:
        logger.error("Failed to remove duplicate subscription_plans collection")
        return 1

if __name__ == "__main__":
    sys.exit(main())
