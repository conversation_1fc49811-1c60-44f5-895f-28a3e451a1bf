#!/usr/bin/env python3
"""
Script to check Firestore collections.
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase"""
    try:
        firebase_admin.get_app()
    except ValueError:
        # Check if service account key file exists
        service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
        if os.path.exists(service_account_path):
            # Use the service account key file
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred)
            logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
        else:
            # For local development, use the default app configuration
            firebase_admin.initialize_app()
            logger.info("Initialized Firebase with default configuration")

def list_collections():
    """List all collections in Firestore"""
    db = firestore.client()
    collections = db.collections()
    
    logger.info("Firestore collections:")
    for collection in collections:
        logger.info(f"- {collection.id}")
        
        # Check document count
        docs = collection.limit(10).stream()
        doc_count = 0
        for doc in docs:
            doc_count += 1
        
        logger.info(f"  - Document count (sample): {doc_count}")
        
        # If this is a subscription-related collection, check its contents
        if "subscription" in collection.id.lower():
            logger.info(f"  - Contents of {collection.id}:")
            docs = collection.limit(10).stream()
            for doc in docs:
                logger.info(f"    - {doc.id}: {doc.to_dict().keys()}")

def main():
    """Main function"""
    initialize_firebase()
    list_collections()
    return 0

if __name__ == "__main__":
    sys.exit(main())
