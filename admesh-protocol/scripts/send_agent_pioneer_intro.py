"""
<PERSON><PERSON><PERSON> to send the Agent Pioneer Program introduction email to eligible users.

Usage:
    python scripts/send_agent_pioneer_intro.py --email <EMAIL> --name "User Name"
    python scripts/send_agent_pioneer_intro.py --all-eligible  # Send to all eligible users
"""
import os
import sys
import logging
import argparse
import resend
import asyncio
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import from the project
from firebase.config import get_db

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")
# Log that we're using the API key (without showing any part of it)
logger.info("Using Resend API key from environment")

# Initialize Firestore
db = get_db()

async def get_eligible_users():
    """Get all users who are eligible for the Pioneer program but haven't seen the welcome message"""
    try:
        # Get users who haven't seen the Pioneer welcome message
        users_ref = db.collection("users").where("seenPioneerWelcome", "==", False).stream()

        eligible_users = []
        for user_doc in users_ref:
            user_data = user_doc.to_dict()

            # Check if user has an email and is eligible for Pioneer program
            if "email" in user_data and user_data.get("isPioneerEligible", True):
                eligible_users.append({
                    "uid": user_doc.id,
                    "email": user_data["email"],
                    "first_name": user_data.get("first_name", ""),
                    "name": user_data.get("name", "")
                })

        return eligible_users

    except Exception as e:
        logger.error(f"Error getting eligible users: {str(e)}")
        return []

def send_pioneer_email(email, first_name=None):
    """
    Send the Agent Pioneer Program introduction email to a user

    Args:
        email (str): The user's email address
        first_name (str, optional): The user's first name

    Returns:
        dict: The response from the Resend API
    """
    try:
        # Read the pioneer email template
        template_path = os.path.join(os.path.dirname(__file__), '..', 'templates', 'email_agent_pioneer_intro.html')
        with open(template_path, 'r') as file:
            html_content = file.read()

        # Replace the first_name placeholder if provided
        if first_name:
            html_content = html_content.replace('{{ first_name | default: "there" }}', first_name)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Introducing the Agent Pioneer Program",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Pioneer program introduction email sent to {email}")
        return response

    except Exception as e:
        logger.error(f"Error sending pioneer email: {str(e)}")
        raise e

async def send_to_all_eligible():
    """Send the Pioneer program introduction email to all eligible users"""
    try:
        # Get eligible users
        eligible_users = await get_eligible_users()
        logger.info(f"Found {len(eligible_users)} eligible users for Pioneer program introduction")

        if not eligible_users:
            logger.info("No eligible users found. Exiting.")
            return

        # Send emails to each user
        sent_count = 0
        for user in eligible_users:
            try:
                # Get user's first name
                first_name = user.get("first_name", user.get("name", "").split()[0] if user.get("name") else None)

                # Send email
                send_pioneer_email(user["email"], first_name)
                sent_count += 1

                # Update user to mark them as having seen the Pioneer welcome
                user_ref = db.collection("users").document(user["uid"])
                user_ref.update({"seenPioneerWelcome": True})

                # Log every 10 emails
                if sent_count % 10 == 0:
                    logger.info(f"Sent {sent_count} pioneer emails so far")

            except Exception as e:
                logger.error(f"Error sending pioneer email to {user['email']}: {str(e)}")

        # Log the task execution
        from google.cloud import firestore
        db.collection("scheduled_tasks").add({
            "task": "pioneer_intro_emails",
            "sent_count": sent_count,
            "total_users": len(eligible_users),
            "completed_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Pioneer program introduction emails sent to {sent_count} users")

    except Exception as e:
        logger.error(f"Error sending to all eligible users: {str(e)}")
        raise e

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Send the Agent Pioneer Program introduction email')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--email', help='User email address')
    group.add_argument('--all-eligible', action='store_true', help='Send to all eligible users')
    parser.add_argument('--name', help='User first name (only used with --email)')
    args = parser.parse_args()

    try:
        if args.all_eligible:
            # Send to all eligible users
            asyncio.run(send_to_all_eligible())
        else:
            # Send to a specific user
            response = send_pioneer_email(args.email, args.name)
            logger.info(f"Email sent with ID: {response.get('id', 'unknown')}")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
