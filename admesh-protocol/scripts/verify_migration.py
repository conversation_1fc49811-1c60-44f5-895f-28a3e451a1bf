#!/usr/bin/env python3
"""
Script to verify that the subscription migration was successful.
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging
from pprint import pprint

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def initialize_firebase():
    """Initialize Firebase"""
    try:
        firebase_admin.get_app()
    except ValueError:
        # Check if service account key file exists
        service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
        if os.path.exists(service_account_path):
            # Use the service account key file
            cred = credentials.Certificate(service_account_path)
            firebase_admin.initialize_app(cred)
            logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
        else:
            # For local development, use the default app configuration
            firebase_admin.initialize_app()
            logger.info("Initialized Firebase with default configuration")

def verify_subscription_plans():
    """Verify that subscription plans were stored in the database"""
    db = firestore.client()
    plans_ref = db.collection("subscriptionPlans")
    plans = plans_ref.stream()

    plan_count = 0
    for plan in plans:
        plan_count += 1
        plan_data = plan.to_dict()
        logger.info(f"Found plan: {plan.id}")
        logger.info(f"  - Name: {plan_data.get('name')}")
        logger.info(f"  - Price: ${plan_data.get('price_monthly_cents', 0) / 100}/month")
        logger.info(f"  - Product listings limit: {plan_data.get('product_listings_limit')}")

    logger.info(f"Found {plan_count} subscription plans in the database")
    return plan_count > 0

def verify_brand_subscriptions():
    """Verify that brand subscriptions were migrated to the new structure"""
    db = firestore.client()
    brands_ref = db.collection("brands")
    brands = brands_ref.stream()

    success_count = 0
    total_count = 0

    for brand in brands:
        total_count += 1
        brand_id = brand.id
        brand_data = brand.to_dict()

        # Check if the old subscription structure was removed
        if "subscription" in brand_data:
            logger.warning(f"Brand {brand_id} still has the old subscription structure")
        else:
            logger.info(f"Brand {brand_id} no longer has the old subscription structure")

        # Check if the brand has a currentSubscriptionId field
        if "currentSubscriptionId" in brand_data:
            logger.info(f"Brand {brand_id} has currentSubscriptionId: {brand_data['currentSubscriptionId']}")
        else:
            logger.warning(f"Brand {brand_id} does not have a currentSubscriptionId field")

        # Check if the new subscription structure exists
        if "currentSubscriptionId" in brand_data:
            # Use the currentSubscriptionId to get the subscription document
            subscription_id = brand_data["currentSubscriptionId"]
            sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document(subscription_id)
            sub_doc = sub_ref.get()
        else:
            # Fallback to checking for a "current" document
            sub_ref = db.collection("brands").document(brand_id).collection("subscriptions").document("current")
            sub_doc = sub_ref.get()

        if sub_doc.exists:
            success_count += 1
            sub_data = sub_doc.to_dict()
            logger.info(f"Brand {brand_id} has the new subscription structure")
            logger.info(f"  - Plan ID: {sub_data.get('plan_id')}")
            logger.info(f"  - Status: {sub_data.get('status')}")

            # Check if the limits map exists
            if "limits" in sub_data:
                limits = sub_data["limits"]
                logger.info(f"  - Limits: {limits}")
            else:
                logger.warning(f"Brand {brand_id} subscription does not have a limits map")

            # Check if the usage map exists
            if "usage" in sub_data:
                usage = sub_data["usage"]
                logger.info(f"  - Usage: {usage}")
            else:
                logger.warning(f"Brand {brand_id} subscription does not have a usage map")

            # Check if the references map exists
            if "references" in sub_data:
                references = sub_data["references"]
                logger.info(f"  - References: {references}")
            else:
                logger.warning(f"Brand {brand_id} subscription does not have a references map")
        else:
            logger.warning(f"Brand {brand_id} does not have the new subscription structure")

    logger.info(f"Found {success_count} out of {total_count} brands with the new subscription structure")
    return success_count == total_count

def main():
    """Main function"""
    initialize_firebase()

    logger.info("Verifying subscription plans...")
    plans_success = verify_subscription_plans()

    logger.info("\nVerifying brand subscriptions...")
    brands_success = verify_brand_subscriptions()

    if plans_success and brands_success:
        logger.info("\nMigration verification successful!")
        return 0
    else:
        logger.error("\nMigration verification failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
