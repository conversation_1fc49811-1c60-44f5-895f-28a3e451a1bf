"""
<PERSON><PERSON><PERSON> to send weekly update emails to all users with their XP, leaderboard position, and badge progress.

Usage:
    python scripts/send_weekly_update.py
    python scripts/send_weekly_update.py --email <EMAIL>  # Send to a specific email for testing
"""
import os
import sys
import logging
import argparse
import resend
from datetime import datetime, timedelta, timezone
import jinja2
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import from the project
from firebase.config import get_db

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")
# Log that we're using the API key (without showing any part of it)
logger.info("Using Resend API key from environment")

# Initialize Firestore
db = get_db()

# Initialize Jinja2 environment
template_loader = jinja2.FileSystemLoader(os.path.join(os.path.dirname(__file__), '..', 'templates'))
template_env = jinja2.Environment(loader=template_loader)
template = template_env.get_template('email_weekly_update.html')

def get_recent_badges(user_id):
    """Get badges earned by the user in the last 7 days"""
    try:
        # Calculate the timestamp for 7 days ago
        seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
        seven_days_ago_str = seven_days_ago.isoformat()

        # Query badges awarded after that timestamp
        badges_ref = db.collection("users").document(user_id).collection("badges").where(
            "awarded_at", ">=", seven_days_ago_str
        ).stream()

        recent_badges = []
        for badge_doc in badges_ref:
            badge_data = badge_doc.to_dict()
            if "metadata" in badge_data:
                recent_badges.append({
                    "name": badge_data["metadata"].get("name", "Unknown Badge"),
                    "description": badge_data["metadata"].get("description", ""),
                    "xp_bonus": badge_data["metadata"].get("xp_bonus", 0)
                })

        return recent_badges

    except Exception as e:
        logger.error(f"Error getting recent badges for user {user_id}: {str(e)}")
        return []

def get_login_streak(user_id):
    """Get the user's current login streak"""
    try:
        # Get the user's login history
        login_history_ref = db.collection("user_activity").document(user_id).collection("logins").order_by(
            "timestamp", direction="DESCENDING"
        ).limit(10).stream()

        # Convert to list of dates
        login_dates = []
        for login_doc in login_history_ref:
            login_data = login_doc.to_dict()
            if "timestamp" in login_data:
                # Convert timestamp to date
                if isinstance(login_data["timestamp"], str):
                    login_date = datetime.fromisoformat(login_data["timestamp"].replace('Z', '+00:00')).date()
                else:
                    # Assume it's a Firestore timestamp
                    login_date = login_data["timestamp"].date()
                login_dates.append(login_date)

        # Count consecutive days
        if not login_dates:
            return 0

        streak = 1
        today = datetime.now(timezone.utc).date()

        for i in range(1, 8):  # Check up to 7 days
            check_date = today - timedelta(days=i)
            if check_date in login_dates:
                streak += 1
            else:
                break

        return streak

    except Exception as e:
        logger.error(f"Error getting login streak for user {user_id}: {str(e)}")
        return 0

def send_test_email(email):
    """Send a test weekly update email to a specific email address"""
    try:
        # Get the base URL from environment or use default
        env = os.getenv("ENV", "production")
        base_url = os.getenv("FRONTEND_URL_PROD", "https://useadmesh.com")
        if env == "development":
            base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")

        # Sample data for testing
        context = {
            "first_name": "Test User",
            "xp": 450,
            "position": 12,
            "login_streak": 5,
            "pioneer_progress": 45,
            "weekly_progress": 71,
            "base_url": base_url,
            "recent_badges": [
                {
                    "name": "First Discovery",
                    "description": "Made your first product discovery query",
                    "xp_bonus": 10
                },
                {
                    "name": "Weekly Active",
                    "description": "Logged in for 7 consecutive days",
                    "xp_bonus": 20
                }
            ]
        }

        # Render the template
        html_content = template.render(**context)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Your Weekly AdMesh Update (Test)",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Test weekly update email sent to {email}")
        return response

    except Exception as e:
        logger.error(f"Error sending test weekly update email: {str(e)}")
        raise e

def send_to_all_users():
    """Send weekly update emails to all users"""
    try:
        # Get top users by XP for leaderboard positions
        users_ref = db.collection("users").order_by("xp", direction="DESCENDING").stream()

        # Create a mapping of user_id to position
        leaderboard_positions = {}
        for i, user_doc in enumerate(users_ref):
            leaderboard_positions[user_doc.id] = i + 1

        # Get all users with email addresses
        users_ref = db.collection("users").stream()

        sent_count = 0
        for user_doc in users_ref:
            try:
                user_data = user_doc.to_dict()
                user_id = user_doc.id

                # Skip users without email
                if "email" not in user_data:
                    continue

                # Get user's XP and position
                xp = user_data.get("xp", 0)
                position = leaderboard_positions.get(user_id, 0)

                # Get user's login streak
                login_streak = get_login_streak(user_id)

                # Get recent badges
                recent_badges = get_recent_badges(user_id)

                # Calculate progress percentages
                pioneer_progress = min(int((xp / 1000) * 100), 100)
                weekly_progress = min(int((login_streak / 7) * 100), 100)

                # Get the base URL from environment or use default
                env = os.getenv("ENV", "production")
                base_url = os.getenv("FRONTEND_URL_PROD", "https://useadmesh.com")
                if env == "development":
                    base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")

                # Prepare template context
                context = {
                    "first_name": user_data.get("first_name", user_data.get("name", "there")),
                    "xp": xp,
                    "position": position,
                    "login_streak": login_streak,
                    "pioneer_progress": pioneer_progress,
                    "weekly_progress": weekly_progress,
                    "base_url": base_url,
                    "recent_badges": recent_badges
                }

                # Render the template
                html_content = template.render(**context)

                # Prepare email payload
                params = {
                    "from": "AdMesh <<EMAIL>>",
                    "to": [user_data["email"]],
                    "subject": "Your Weekly AdMesh Update",
                    "html": html_content,
                }

                # Send email
                resend.Emails.send(params)
                sent_count += 1

                # Log every 10 emails
                if sent_count % 10 == 0:
                    logger.info(f"Sent {sent_count} weekly update emails so far")

            except Exception as e:
                logger.error(f"Error sending weekly update to user {user_doc.id}: {str(e)}")

        # Log the task execution
        from google.cloud import firestore
        db.collection("scheduled_tasks").add({
            "task": "weekly_update_emails",
            "sent_count": sent_count,
            "completed_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Weekly update emails sent to {sent_count} users")

    except Exception as e:
        logger.error(f"Error sending weekly updates: {str(e)}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Send weekly update emails')
    parser.add_argument('--email', help='Send a test email to this address')
    args = parser.parse_args()

    try:
        if args.email:
            # Send a test email to the specified address
            send_test_email(args.email)
        else:
            # Send to all users
            send_to_all_users()

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
