import firebase_admin
from firebase_admin import auth, credentials
import argparse

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        if not firebase_admin._apps:
            cred = credentials.Certificate("firebase/serviceAccountKey.json")
            firebase_admin.initialize_app(cred)
    except Exception as e:
        print(f"Error initializing Firebase: {e}")
        exit(1)

def make_admin(user_id=None, email=None):
    """Make a user an admin by setting the admin custom claim"""
    if not user_id and not email:
        print("Error: Either user_id or email must be provided")
        return
    
    try:
        # Get user by ID or email
        if user_id:
            user = auth.get_user(user_id)
        else:
            user = auth.get_user_by_email(email)
        
        # Get current custom claims
        custom_claims = dict(user.custom_claims or {})
        
        # Add admin claim
        custom_claims["admin"] = True
        
        # Update custom claims
        auth.set_custom_user_claims(user.uid, custom_claims)
        
        print(f"Successfully set admin status for user:")
        print(f"  User ID: {user.uid}")
        print(f"  Email: {user.email}")
        print(f"  Display Name: {user.display_name}")
        print(f"  Custom claims: {custom_claims}")
    except Exception as e:
        print(f"Error setting admin status: {e}")

def list_user_claims(user_id=None, email=None):
    """List a user's custom claims"""
    if not user_id and not email:
        print("Error: Either user_id or email must be provided")
        return
    
    try:
        # Get user by ID or email
        if user_id:
            user = auth.get_user(user_id)
        else:
            user = auth.get_user_by_email(email)
        
        # Get current custom claims
        custom_claims = dict(user.custom_claims or {})
        
        print(f"User information:")
        print(f"  User ID: {user.uid}")
        print(f"  Email: {user.email}")
        print(f"  Display Name: {user.display_name}")
        print(f"  Custom claims: {custom_claims}")
    except Exception as e:
        print(f"Error getting user information: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Manage Firebase admin users")
    parser.add_argument("--action", choices=["make-admin", "list-claims"], required=True, help="Action to perform")
    parser.add_argument("--user-id", help="Firebase user ID")
    parser.add_argument("--email", help="User email")
    
    args = parser.parse_args()
    
    initialize_firebase()
    
    if args.action == "make-admin":
        make_admin(args.user_id, args.email)
    elif args.action == "list-claims":
        list_user_claims(args.user_id, args.email)
