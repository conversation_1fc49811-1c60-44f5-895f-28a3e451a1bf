"""
<PERSON><PERSON><PERSON> to send a test weekly update email without Firebase dependencies.

Usage:
    python scripts/test_weekly_email.py --email <EMAIL>
"""
import os
import sys
import logging
import argparse
import resend
import jinja2
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")
# Log that we're using the API key (without showing any part of it)
logger.info("Using Resend API key from environment")

# Initialize Jinja2 environment
template_loader = jinja2.FileSystemLoader(os.path.join(os.path.dirname(__file__), '..', 'templates'))
template_env = jinja2.Environment(loader=template_loader)
template = template_env.get_template('email_weekly_update.html')

def send_test_email(email):
    """Send a test weekly update email to a specific email address"""
    try:
        # Get the base URL from environment or use default
        env = os.getenv("ENV", "production")
        base_url = os.getenv("FRONTEND_URL_PROD", "https://useadmesh.com")
        if env == "development":
            base_url = os.getenv("FRONTEND_URL", "http://localhost:3000")

        # Sample data for testing
        context = {
            "first_name": "Test User",
            "xp": 450,
            "position": 12,
            "login_streak": 5,
            "pioneer_progress": 45,
            "weekly_progress": 71,
            "base_url": base_url,
            "recent_badges": [
                {
                    "name": "First Discovery",
                    "description": "Made your first product discovery query",
                    "xp_bonus": 10
                },
                {
                    "name": "Weekly Active",
                    "description": "Logged in for 7 consecutive days",
                    "xp_bonus": 20
                }
            ]
        }

        # Render the template
        html_content = template.render(**context)

        # Prepare email payload
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Your Weekly AdMesh Update (Test)",
            "html": html_content,
        }

        # Send email
        response = resend.Emails.send(params)
        logger.info(f"Test weekly update email sent to {email}")
        return response

    except Exception as e:
        logger.error(f"Error sending test weekly update email: {str(e)}")
        raise e

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Send a test weekly update email')
    parser.add_argument('--email', required=True, help='Email address to send the test to')
    args = parser.parse_args()

    try:
        # Send a test email to the specified address
        send_test_email(args.email)
        print(f"Test email sent to {args.email}")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
