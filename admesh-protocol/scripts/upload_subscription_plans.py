#!/usr/bin/env python3
"""
Script to upload subscription plans to the database.
This script creates or updates subscription plans in the 'subscriptionPlans' collection.
"""

import os
import sys
import firebase_admin
from firebase_admin import credentials, firestore
import logging
from datetime import datetime
from typing import Dict, Any, List

# Add the parent directory to the path so we can import from api
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Firebase
try:
    firebase_admin.get_app()
except ValueError:
    # Check if service account key file exists
    service_account_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'firebase/serviceAccountKey.json')
    if os.path.exists(service_account_path):
        # Use the service account key file
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred)
        logger.info(f"Initialized Firebase with service account key file: {service_account_path}")
    else:
        # For local development, use the default app configuration
        firebase_admin.initialize_app()
        logger.info("Initialized Firebase with default configuration")

db = firestore.client()

# Define subscription plans
def get_free_plan():
    """Get the free plan"""
    return {
        "id": "free",
        "name": "Free",
        "price_monthly_cents": 0,
        "price_annual_cents": 0,
        "billing_cycle": "free",
        "product_listings_limit": 1,
        "active_offers_per_product_limit": 1,
        "promo_credit_cents": 5000,  # $50
        "visibility_boost": "Basic (relevance only)",
        "analytics_level": "Basic (clicks + conversions)",
        "conversion_reports": "Basic (no filters)",
        "agent_match_priority": "Normal",
        "badge_type": None,
        "support_level": "Community",
        "agent_outreach_tools": False,
        "multi_user_access": False,
        "multi_user_limit": 0,
        "cpa_optimization": False,
        "keyword_limit": 10,
        "features": [
            "1 Product Listing",
            "1 Active Offer per Product",
            "$50 one-time promo credit",
            "Basic analytics (clicks + conversions)",
            "Basic conversion reports (no filters)",
            "Normal agent match priority",
            "Community support"
        ],
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }

def get_starter_plan(annual=False):
    """Get the starter plan"""
    # 20% discount for annual billing
    monthly_price = 2900  # $29/month
    annual_price = monthly_price * 12 * 0.8  # 20% discount

    return {
        "id": "starter_annual" if annual else "starter",
        "name": "Starter",
        "price_monthly_cents": monthly_price,
        "price_annual_cents": int(annual_price),
        "billing_cycle": "annual" if annual else "monthly",
        "product_listings_limit": 5,
        "active_offers_per_product_limit": -1,  # Unlimited
        "promo_credit_cents": 10000,  # $100
        "visibility_boost": "Boosted (CPA-weighted placement)",
        "analytics_level": "CTR + Conversions",
        "conversion_reports": "Filter by product / offer",
        "agent_match_priority": "Medium",
        "badge_type": "Featured",
        "support_level": "Email (48hr SLA)",
        "agent_outreach_tools": False,
        "multi_user_access": False,
        "multi_user_limit": 0,
        "cpa_optimization": False,
        "keyword_limit": 20,
        "features": [
            "5 Product Listings",
            "Unlimited Active Offers per Product",
            "$100 one-time promo credit",
            "Boosted offer visibility (CPA-weighted placement)",
            "CTR + Conversion analytics",
            "Conversion reports with product/offer filters",
            "Medium agent match priority",
            "Featured badge in UI/Chat",
            "Email support (48hr SLA)",
            f"Save 20% with annual billing" if annual else "Monthly billing flexibility"
        ],
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }

def get_growth_plan(annual=False):
    """Get the growth plan"""
    # 20% discount for annual billing
    monthly_price = 9900  # $99/month
    annual_price = monthly_price * 12 * 0.8  # 20% discount

    return {
        "id": "growth_annual" if annual else "growth",
        "name": "Growth",
        "price_monthly_cents": monthly_price,
        "price_annual_cents": int(annual_price),
        "billing_cycle": "annual" if annual else "monthly",
        "product_listings_limit": -1,  # Unlimited
        "active_offers_per_product_limit": -1,  # Unlimited
        "promo_credit_cents": 15000,  # $150
        "visibility_boost": "High priority in agent results",
        "analytics_level": "Full funnel: Impressions → ROI",
        "conversion_reports": "Filter by time, intent, offer, ROI",
        "agent_match_priority": "Highest",
        "badge_type": "Top Pick",
        "support_level": "Priority (24hr SLA)",
        "agent_outreach_tools": True,
        "multi_user_access": True,
        "multi_user_limit": 3,
        "cpa_optimization": True,
        "keyword_limit": 30,
        "features": [
            "Unlimited Product Listings",
            "Unlimited Active Offers per Product",
            "$150 one-time promo credit",
            "High priority in agent results",
            "Full funnel analytics (Impressions → ROI)",
            "Advanced conversion reports with time/intent/ROI filters",
            "Highest agent match priority",
            "Top Pick badge in UI/Chat",
            "Priority support (24hr SLA)",
            "Early access to Agent Outreach Tools (Beta)",
            "Multi-user access (up to 3 teammates)",
            "CPA Optimization Concierge",
            f"Save 20% with annual billing" if annual else "Monthly billing flexibility"
        ],
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }

def get_all_plans():
    """Get all subscription plans"""
    return [
        get_free_plan(),
        get_starter_plan(annual=False),
        get_starter_plan(annual=True),
        get_growth_plan(annual=False),
        get_growth_plan(annual=True)
    ]

def upload_subscription_plans():
    """Upload subscription plans to the database"""
    plans = get_all_plans()

    # Create a batch
    batch = db.batch()

    for plan in plans:
        plan_id = plan["id"]
        plan_ref = db.collection("subscriptionPlans").document(plan_id)
        batch.set(plan_ref, plan)

    # Commit the batch
    batch.commit()

    logger.info(f"Successfully uploaded {len(plans)} subscription plans to the database")
    return True

def main():
    """Main function"""
    logger.info("Starting subscription plans upload")

    # Upload subscription plans
    success = upload_subscription_plans()

    if success:
        logger.info("Subscription plans upload completed successfully")
        return 0
    else:
        logger.error("Subscription plans upload failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
