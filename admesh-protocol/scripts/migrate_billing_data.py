#!/usr/bin/env python3
"""
Combined migration script to move both wallet balances and transactions to the payments collection.

This script:
1. Fetches all brands
2. For each brand:
   a. Migrates wallet_balance from brands to payments/{brandUid}
   b. Migrates transactions from brands/{brandUid}/transactions to payments/{brandUid}/transactions
   c. Calculates total_spent and current_total_budget from offers
3. Creates a payments/{brandUid} document with summary data
4. Optionally removes the original data after migration

Usage:
python migrate_billing_data.py
"""

# Import required modules
import os
import sys
import logging
import time
import argparse

# Add the parent directory to sys.path to allow importing from the firebase package
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now we can import from the firebase package
from firebase.config import get_db
from google.cloud import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_billing_data(delete_old_data=False, dry_run=False):
    """
    Migrate wallet balances and transactions to the payments collection
    
    Args:
        delete_old_data (bool): Whether to delete the original data after migration
        dry_run (bool): If True, only log what would be done without making changes
    """
    db = get_db()
    
    # Get all brands
    brands_ref = db.collection("brands")
    brands = list(brands_ref.stream())
    
    logger.info(f"Found {len(brands)} brands to process")
    
    total_transactions = 0
    migrated_transactions = 0
    brands_with_wallet = 0
    
    # Process each brand
    for brand_doc in brands:
        brand_id = brand_doc.id
        brand_data = brand_doc.to_dict()
        brand_name = brand_data.get("company_name", "Unknown")
        wallet_balance = brand_data.get("wallet_balance", 0)
        
        logger.info(f"Processing brand: {brand_name} (ID: {brand_id})")
        
        # Get all transactions for this brand
        tx_ref = db.collection("brands").document(brand_id).collection("transactions")
        transactions = list(tx_ref.stream())
        
        logger.info(f"Found {len(transactions)} transactions for brand {brand_name}")
        total_transactions += len(transactions)
        
        # Calculate total_spent and current_total_budget from offers
        total_spent = 0
        current_total_budget = 0
        
        # Query offers for this brand
        offers_ref = db.collection("offers").where("brand_id", "==", brand_id)
        offers = list(offers_ref.stream())
        
        for offer_doc in offers:
            offer_data = offer_doc.to_dict()
            # Add to total_spent
            total_spent += offer_data.get("total_spent", 0)
            
            # Add to current_total_budget if offer is active
            if offer_data.get("active", False):
                current_total_budget += offer_data.get("budget", 0)
        
        logger.info(f"Brand {brand_name}: wallet_balance=${wallet_balance}, total_spent=${total_spent}, current_total_budget=${current_total_budget}")
        
        if dry_run:
            logger.info(f"DRY RUN: Would create/update payments document for brand {brand_name}")
            for tx_doc in transactions:
                tx_id = tx_doc.id
                logger.info(f"DRY RUN: Would migrate transaction {tx_id} for brand {brand_name}")
            continue
        
        # Create or update the payments document for this brand
        payments_ref = db.collection("payments").document(brand_id)
        payments_doc = payments_ref.get()
        
        if not payments_doc.exists:
            # Create the payments document with summary data (without timestamps)
            payments_data = {
                "uid": brand_id,
                "total_balance": wallet_balance,
                "total_spent": total_spent,
                "current_total_budget": current_total_budget
            }
            payments_ref.set(payments_data)
            
            # Update with timestamps separately
            payments_ref.update({
                "created_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            logger.info(f"Created payments document for brand {brand_name}")
        else:
            # Update existing document
            payments_data = payments_doc.to_dict()
            current_balance = payments_data.get("total_balance", 0)
            
            # Only update if the current balance is different
            if current_balance != wallet_balance:
                logger.info(f"Updating payments document for brand {brand_name}: ${current_balance} -> ${wallet_balance}")
                payments_ref.update({
                    "total_balance": wallet_balance,
                    "total_spent": total_spent,
                    "current_total_budget": current_total_budget,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            else:
                logger.info(f"Payments document already has correct balance for brand {brand_name}")
        
        if wallet_balance > 0:
            brands_with_wallet += 1
        
        # Migrate each transaction to the subcollection
        for tx_doc in transactions:
            tx_id = tx_doc.id
            tx_data = tx_doc.to_dict()
            
            # Create new document in payments/{brandUid}/transactions subcollection
            try:
                # First create the transaction without timestamp
                tx_data_without_timestamp = {k: v for k, v in tx_data.items() if k != "timestamp"}
                new_tx_ref = db.collection("payments").document(brand_id).collection("transactions").document(tx_id)
                new_tx_ref.set(tx_data_without_timestamp)
                
                # Then add timestamp separately if it exists
                if "timestamp" in tx_data:
                    new_tx_ref.update({"timestamp": tx_data["timestamp"]})
                
                migrated_transactions += 1
                logger.info(f"Migrated transaction {tx_id} for brand {brand_name}")
                
                # Small delay to avoid overwhelming Firestore
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error migrating transaction {tx_id}: {e}")
        
        # Optionally delete old data
        if delete_old_data:
            # Delete old transactions
            for tx_doc in transactions:
                tx_id = tx_doc.id
                tx_ref.document(tx_id).delete()
                logger.info(f"Deleted old transaction {tx_id}")
                time.sleep(0.05)  # Small delay
            
            # Remove wallet_balance from brand document
            if "wallet_balance" in brand_data:
                brands_ref.document(brand_id).update({
                    "wallet_balance": firestore.DELETE_FIELD
                })
                logger.info(f"Removed wallet_balance field from brand {brand_name}")
    
    logger.info(f"Migration complete.")
    logger.info(f"Brands with wallet balance: {brands_with_wallet} of {len(brands)}")
    logger.info(f"Migrated transactions: {migrated_transactions} of {total_transactions}")
    
    if not delete_old_data:
        logger.info("Original data was preserved. Run with --delete-old-data to remove after confirming migration.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Migrate billing data to payments collection")
    parser.add_argument("--delete-old-data", action="store_true", help="Delete original data after migration")
    parser.add_argument("--dry-run", action="store_true", help="Only log what would be done without making changes")
    args = parser.parse_args()
    
    logger.info("Starting billing data migration")
    logger.info(f"Delete old data: {args.delete_old_data}")
    logger.info(f"Dry run: {args.dry_run}")
    
    migrate_billing_data(delete_old_data=args.delete_old_data, dry_run=args.dry_run)
    
    logger.info("Migration script completed")
