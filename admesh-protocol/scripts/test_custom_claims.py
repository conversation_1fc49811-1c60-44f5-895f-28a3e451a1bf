"""
<PERSON><PERSON><PERSON> to test the custom claims endpoints.

Usage:
    python scripts/test_custom_claims.py
"""
import os
import sys
import json
import requests
import argparse
from firebase_admin import auth, credentials, initialize_app
import firebase_admin

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure API base URL
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        if not firebase_admin._apps:
            cred = credentials.Certificate("firebase/serviceAccountKey.json")
            firebase_admin.initialize_app(cred)
    except Exception as e:
        print(f"Error initializing Firebase: {e}")
        exit(1)

def get_admin_token(admin_email):
    """Get an admin token for testing"""
    try:
        # Get the admin user
        user = auth.get_user_by_email(admin_email)
        
        # Check if user has admin claim
        custom_claims = user.custom_claims or {}
        if not custom_claims.get("admin", False):
            print(f"User {admin_email} is not an admin. Setting admin claim...")
            
            # Set admin claim
            custom_claims["admin"] = True
            auth.set_custom_user_claims(user.uid, custom_claims)
            print(f"Admin claim set for {admin_email}")
        
        # Create a custom token
        custom_token = auth.create_custom_token(user.uid)
        
        # Exchange custom token for ID token (requires Firebase Auth REST API)
        # This is a simplified example - in a real app, you would use the Firebase Auth REST API
        print(f"Custom token created for {admin_email}: {custom_token.decode()}")
        print("Please exchange this token for an ID token using the Firebase Auth REST API")
        print("and set the ID token in the ADMIN_TOKEN environment variable.")
        
        # For testing purposes, you can manually get an ID token from the Firebase console
        # or use the Firebase Auth REST API
        return os.getenv("ADMIN_TOKEN")
        
    except Exception as e:
        print(f"Error getting admin token: {e}")
        return None

def test_update_custom_claims(token, user_id, claims, merge=True):
    """Test the /auth/custom-claims/update endpoint"""
    if not token:
        print("Please set a valid admin token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "user_id": user_id,
        "claims": claims,
        "merge": merge
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/custom-claims/update", headers=headers, json=payload)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_get_custom_claims(token, user_id):
    """Test the /auth/custom-claims/{user_id} endpoint"""
    if not token:
        print("Please set a valid admin token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{API_BASE_URL}/auth/custom-claims/{user_id}", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_verify_email(token):
    """Test the /auth/custom-claims/verify-email endpoint"""
    if not token:
        print("Please set a valid user token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/custom-claims/verify-email", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_add_badge_claim(token, badge_id):
    """Test the /auth/custom-claims/badge/{badge_id} endpoint"""
    if not token:
        print("Please set a valid user token")
        return
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/custom-claims/badge/{badge_id}", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def main():
    parser = argparse.ArgumentParser(description="Test custom claims endpoints")
    parser.add_argument("--admin-email", help="Admin email for testing")
    parser.add_argument("--user-id", help="User ID to update claims for")
    parser.add_argument("--user-token", help="User token for testing user endpoints")
    parser.add_argument("--badge-id", help="Badge ID to add to user claims")
    
    args = parser.parse_args()
    
    initialize_firebase()
    
    # Get admin token
    admin_token = None
    if args.admin_email:
        admin_token = get_admin_token(args.admin_email)
    else:
        admin_token = os.getenv("ADMIN_TOKEN")
    
    # Get user token
    user_token = args.user_token or os.getenv("USER_TOKEN")
    
    # Test update custom claims
    if admin_token and args.user_id:
        print("\nTesting /auth/custom-claims/update endpoint:")
        test_update_custom_claims(admin_token, args.user_id, {"test_claim": "test_value"})
    
    # Test get custom claims
    if admin_token and args.user_id:
        print("\nTesting /auth/custom-claims/{user_id} endpoint:")
        test_get_custom_claims(admin_token, args.user_id)
    
    # Test verify email
    if user_token:
        print("\nTesting /auth/custom-claims/verify-email endpoint:")
        test_verify_email(user_token)
    
    # Test add badge claim
    if user_token and args.badge_id:
        print("\nTesting /auth/custom-claims/badge/{badge_id} endpoint:")
        test_add_badge_claim(user_token, args.badge_id)

if __name__ == "__main__":
    main()
