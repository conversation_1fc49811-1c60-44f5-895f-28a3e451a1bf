"""
<PERSON><PERSON><PERSON> to send a test broadcast email to the "Registered Users" audience.

Usage:
    python scripts/send_test_broadcast.py
"""
import os
import sys
import logging
import resend
import argparse
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Now we can import from the project
from firebase.config import get_db

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")
if not resend.api_key:
    logger.error("RESEND_API_KEY environment variable not set")
    raise ValueError("RESEND_API_KEY environment variable is required")
# Log that we're using the API key (without showing any part of it)
logger.info("Using Resend API key from environment")

# Initialize Firestore
db = get_db()

def main():
    # Parse command line arguments (kept for compatibility)
    parser = argparse.ArgumentParser(description='Send a test broadcast email')
    parser.parse_args()

    try:
        # Get audience ID directly from Resend
        audiences = resend.Audiences.list()
        audience_id = None

        # Find the audience by name
        for audience in audiences:
            if isinstance(audience, dict) and "name" in audience and audience["name"] == "Registered Users":
                audience_id = audience["id"]
                break

        if not audience_id:
            logger.error("Audience 'Registered Users' not found. Run create_audience.py first.")
            return

        # Get the weekly template
        template_path = os.path.join(os.path.dirname(__file__), '..', 'templates', 'email_weekly_update.html')
        subject = "Your Weekly AdMesh Update"

        # Read template from file
        with open(template_path, 'r') as file:
            html_content = file.read()

        # Add sample data for the weekly template
        html_content = html_content.replace('{{ xp | default: "0" }}', '450')
        html_content = html_content.replace('{{ position | default: "—" }}', '12')
        html_content = html_content.replace('{{ login_streak | default: "0" }}', '5')
        html_content = html_content.replace('{{ pioneer_progress | default: "0" }}', '45')
        html_content = html_content.replace('{{ weekly_progress | default: "0" }}', '71')

        # Prepare broadcast data
        params = {
            "audience_id": audience_id,
            "from": "AdMesh <<EMAIL>>",
            "subject": subject,
            "html": html_content
        }

        # Send broadcast
        broadcast_response = resend.Broadcasts.create(params)
        logger.info(f"Broadcast response type: {type(broadcast_response)}")
        logger.info(f"Broadcast response: {broadcast_response}")

        # Handle different response formats
        if isinstance(broadcast_response, dict):
            if "id" in broadcast_response:
                broadcast_id = broadcast_response["id"]
            elif "data" in broadcast_response and isinstance(broadcast_response["data"], dict) and "id" in broadcast_response["data"]:
                broadcast_id = broadcast_response["data"]["id"]
            else:
                raise ValueError(f"Could not find broadcast ID in response: {broadcast_response}")
        else:
            raise ValueError(f"Unexpected response format: {broadcast_response}")

        broadcast = {"id": broadcast_id}

        # Log the broadcast
        from google.cloud import firestore
        db.collection("email_broadcasts").add({
            "broadcast_id": broadcast["id"],
            "audience_id": audience_id,
            "subject": "Welcome to AdMesh!",
            "sent_at": firestore.SERVER_TIMESTAMP
        })

        logger.info(f"Broadcast sent to audience {audience_id} with ID: {broadcast['id']}")

    except Exception as e:
        logger.error(f"Error sending broadcast: {str(e)}")

if __name__ == "__main__":
    main()
