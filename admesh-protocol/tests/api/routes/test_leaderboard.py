import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from api.main import app
from datetime import datetime, timezone

client = TestClient(app)

# Mock data
mock_user = {
    "uid": "test-user-id",
    "role": "admin"
}

mock_leaderboard_data = {
    "id": "test-leaderboard-id",
    "name": "Test Leaderboard",
    "description": "A test leaderboard",
    "start_date": "2023-01-01T00:00:00Z",
    "end_date": "2023-12-31T23:59:59Z",
    "user_ids": ["user1", "user2"],
    "is_active": True,
    "created_at": datetime.now(timezone.utc).isoformat(),
    "created_by": "admin-user-id"
}

mock_user_data = {
    "id": "user1",
    "name": "Test User",
    "avatar_url": "https://example.com/avatar.jpg",
    "xp": 100,
    "badge_count": 2,
    "badges": [
        {
            "badge_id": "badge1",
            "badge_type": "agent_pioneer",
            "metadata": {
                "name": "Agent Pioneer",
                "description": "Reached 1000 XP",
                "icon": "sparkles",
                "color": "#FFD700"
            }
        }
    ],
    "agent_name": "TestAgent",
    "joined_at": "2023-01-01T00:00:00Z"
}

# Mock the Firebase authentication
@pytest.fixture(autouse=True)
def mock_auth():
    with patch("auth.deps.verify_firebase_token", return_value=mock_user):
        with patch("auth.deps.require_role", return_value=lambda: mock_user):
            yield

# Mock Firestore
@pytest.fixture(autouse=True)
def mock_firestore():
    # Create mock document and collection references
    mock_doc = MagicMock()
    mock_doc.get.return_value.exists = True
    mock_doc.get.return_value.to_dict.return_value = mock_leaderboard_data
    
    mock_collection = MagicMock()
    mock_collection.document.return_value = mock_doc
    mock_collection.where.return_value.stream.return_value = [
        type('obj', (object,), {
            'id': 'user1',
            'to_dict': lambda: {
                "displayName": "Test User",
                "photoURL": "https://example.com/avatar.jpg",
                "xp": 100,
                "agentName": "TestAgent",
                "onboardingStatus": "completed",
                "created_at": None
            }
        })
    ]
    
    # Mock badges subcollection
    mock_badges_collection = MagicMock()
    mock_badges_collection.stream.return_value = [
        type('obj', (object,), {
            'to_dict': lambda: {
                "badge_id": "badge1",
                "badge_type": "agent_pioneer",
                "metadata": {
                    "name": "Agent Pioneer",
                    "description": "Reached 1000 XP",
                    "icon": "sparkles",
                    "color": "#FFD700"
                }
            }
        })
    ]
    
    # Setup the collection path
    with patch("api.routes.leaderboard.db") as mock_db:
        mock_db.collection.return_value = mock_collection
        mock_db.collection.return_value.document.return_value.collection.return_value = mock_badges_collection
        yield mock_db

def test_get_leaderboard():
    """Test the GET leaderboard endpoint"""
    response = client.get("/leaderboard")
    assert response.status_code == 200
    data = response.json()
    assert "users" in data
    assert "total" in data
    assert "page" in data
    assert "limit" in data
    assert len(data["users"]) > 0

def test_create_custom_leaderboard():
    """Test creating a custom leaderboard"""
    leaderboard_data = {
        "name": "Test Leaderboard",
        "description": "A test leaderboard",
        "start_date": "2023-01-01T00:00:00Z",
        "end_date": "2023-12-31T23:59:59Z",
        "user_ids": ["user1", "user2"],
        "is_active": True
    }
    
    response = client.post("/leaderboard/custom", json=leaderboard_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == leaderboard_data["name"]
    assert data["description"] == leaderboard_data["description"]
    assert "id" in data
    assert "created_at" in data
    assert "created_by" in data

def test_get_custom_leaderboard():
    """Test getting a custom leaderboard by ID"""
    response = client.get("/leaderboard/custom/test-leaderboard-id")
    assert response.status_code == 200
    data = response.json()
    assert "users" in data
    assert "total" in data
    assert "page" in data
    assert "limit" in data

def test_update_custom_leaderboard():
    """Test updating a custom leaderboard"""
    update_data = {
        "name": "Updated Leaderboard",
        "description": "Updated description"
    }
    
    response = client.patch("/leaderboard/custom/test-leaderboard-id", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == update_data["name"]
    assert data["description"] == update_data["description"]
    assert "updated_at" in data
    assert "updated_by" in data

def test_list_custom_leaderboards():
    """Test listing all custom leaderboards"""
    response = client.get("/leaderboard/custom")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) > 0

def test_delete_custom_leaderboard():
    """Test deleting a custom leaderboard"""
    response = client.delete("/leaderboard/custom/test-leaderboard-id")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "message" in data
