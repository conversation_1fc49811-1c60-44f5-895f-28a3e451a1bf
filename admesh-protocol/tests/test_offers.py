import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)

def test_create_offer():
    offer_data = {
        "title": "Test Offer",
        "description": "Test Description",
        "payout": 10.0,
        "advertiser_id": "test123"
    }
    response = client.post("/api/v1/offers", json=offer_data)
    assert response.status_code == 200
    assert response.json()["title"] == offer_data["title"]
