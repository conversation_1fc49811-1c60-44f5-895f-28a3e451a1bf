import unittest
import os
import sys
from unittest.mock import patch

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.utils.embedding import embed_text, cosine_similarity


class TestSemanticMatching(unittest.TestCase):
    """Test cases for semantic matching functionality"""

    def test_cosine_similarity_identical_vectors(self):
        """Test cosine similarity with identical vectors"""
        vector_a = [1.0, 2.0, 3.0, 4.0]
        vector_b = [1.0, 2.0, 3.0, 4.0]

        similarity = cosine_similarity(vector_a, vector_b)

        # Identical vectors should have similarity of 1.0
        self.assertAlmostEqual(similarity, 1.0, places=4)

    def test_cosine_similarity_orthogonal_vectors(self):
        """Test cosine similarity with orthogonal vectors"""
        vector_a = [1.0, 0.0, 0.0]
        vector_b = [0.0, 1.0, 0.0]

        similarity = cosine_similarity(vector_a, vector_b)

        # Orthogonal vectors should have similarity of 0.0
        self.assertAlmostEqual(similarity, 0.0, places=4)

    def test_cosine_similarity_opposite_vectors(self):
        """Test cosine similarity with opposite vectors"""
        vector_a = [1.0, 2.0, 3.0]
        vector_b = [-1.0, -2.0, -3.0]

        similarity = cosine_similarity(vector_a, vector_b)

        # Opposite vectors should have similarity close to 0.0 (normalized to 0-1 range)
        self.assertGreaterEqual(similarity, 0.0)
        self.assertLessEqual(similarity, 1.0)

    def test_cosine_similarity_zero_vector(self):
        """Test cosine similarity with zero vector"""
        vector_a = [1.0, 2.0, 3.0]
        vector_b = [0.0, 0.0, 0.0]

        similarity = cosine_similarity(vector_a, vector_b)

        # Zero vector should return 0.0
        self.assertEqual(similarity, 0.0)

    def test_cosine_similarity_similar_vectors(self):
        """Test cosine similarity with similar vectors"""
        vector_a = [1.0, 2.0, 3.0, 4.0]
        vector_b = [1.1, 2.1, 3.1, 4.1]

        similarity = cosine_similarity(vector_a, vector_b)

        # Similar vectors should have high similarity
        self.assertGreater(similarity, 0.9)
        self.assertLessEqual(similarity, 1.0)

    @patch('api.utils.embedding.client.embeddings.create')
    def test_embed_text_success(self, mock_openai_create):
        """Test successful text embedding"""
        # Mock the OpenAI response for the new client API
        from unittest.mock import MagicMock

        mock_response = MagicMock()
        mock_response.data = [MagicMock()]
        mock_response.data[0].embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_openai_create.return_value = mock_response

        text = "This is a test text for embedding"
        result = embed_text(text)

        # Verify the function returns the expected embedding
        self.assertEqual(result, [0.1, 0.2, 0.3, 0.4, 0.5])

        # Verify OpenAI was called with correct parameters
        mock_openai_create.assert_called_once_with(
            input=text,
            model="text-embedding-3-small"
        )

    @patch('api.utils.embedding.client.embeddings.create')
    def test_embed_text_failure(self, mock_openai_create):
        """Test text embedding failure handling"""
        # Mock OpenAI to raise an exception
        mock_openai_create.side_effect = Exception("API Error")

        text = "This is a test text for embedding"

        # Verify that the function raises an exception
        with self.assertRaises(Exception) as context:
            embed_text(text)

        self.assertIn("Failed to generate embedding", str(context.exception))

    def test_cosine_similarity_range(self):
        """Test that cosine similarity always returns values between 0 and 1"""
        test_cases = [
            ([1.0, 0.0], [0.0, 1.0]),  # Orthogonal
            ([1.0, 1.0], [1.0, 1.0]),  # Identical
            ([1.0, 2.0], [2.0, 4.0]),  # Proportional
            ([-1.0, -1.0], [1.0, 1.0]),  # Opposite
            ([0.5, 0.8, 0.3], [0.2, 0.9, 0.1]),  # Random
        ]

        for vector_a, vector_b in test_cases:
            similarity = cosine_similarity(vector_a, vector_b)
            self.assertGreaterEqual(similarity, 0.0,
                                  f"Similarity {similarity} should be >= 0.0 for vectors {vector_a}, {vector_b}")
            self.assertLessEqual(similarity, 1.0,
                               f"Similarity {similarity} should be <= 1.0 for vectors {vector_a}, {vector_b}")


if __name__ == '__main__':
    # Set environment variable for testing
    os.environ['OPENAI_API_KEY'] = 'test-key'

    unittest.main()
