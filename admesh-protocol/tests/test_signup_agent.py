"""
Test script for the SignupAgent

This script tests the SignupAgent's ability to detect signup fields
and submit forms on various websites.
"""

import asyncio
import sys
import os
import logging

# Add the parent directory to the path so we can import the api module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.agents.signup_agent import signup_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_detect_signup_fields():
    """Test the detect_signup_fields method on a few websites"""
    test_urls = [
        "https://github.com/signup",
        "https://accounts.google.com/signup",
        "https://www.linkedin.com/signup",
        "https://twitter.com/i/flow/signup",
    ]
    
    for url in test_urls:
        logger.info(f"Testing detect_signup_fields on {url}")
        try:
            result = await signup_agent.detect_signup_fields(url)
            logger.info(f"Result for {url}: {result}")
        except Exception as e:
            logger.error(f"Error testing {url}: {str(e)}")

async def main():
    """Run the tests"""
    await test_detect_signup_fields()

if __name__ == "__main__":
    asyncio.run(main())
