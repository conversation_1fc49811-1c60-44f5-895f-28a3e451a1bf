"""
Test brand registration functionality with new direct onboarding flow
"""
import pytest
import requests
import json
from unittest.mock import patch, MagicMock

# Test configuration
BASE_URL = "http://127.0.0.1:8000"  # Adjust based on your dev server
REGISTER_ENDPOINT = f"{BASE_URL}/auth/email-register"

class TestBrandRegistration:
    """Test cases for the new brand registration flow"""
    
    def test_brand_registration_success(self):
        """Test successful brand registration with all required fields"""
        payload = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "role": "brand",
            "company_name": "Example Corp",
            "website": "https://example.com"
        }
        
        # Mock Firebase user creation
        with patch('firebase_admin.auth.create_user') as mock_create_user, \
             patch('firebase_admin.auth.set_custom_user_claims') as mock_set_claims, \
             patch('firebase.config.get_db') as mock_get_db:
            
            # Setup mocks
            mock_user = MagicMock()
            mock_user.uid = "test-uid-123"
            mock_create_user.return_value = mock_user
            
            mock_db = MagicMock()
            mock_collection = MagicMock()
            mock_document = MagicMock()
            mock_db.collection.return_value = mock_collection
            mock_collection.document.return_value = mock_document
            mock_get_db.return_value = mock_db
            
            # Make request
            response = requests.post(REGISTER_ENDPOINT, json=payload)
            
            # Assertions
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["role"] == "brand"
            assert data["uid"] == "test-uid-123"
            
            # Verify Firebase calls
            mock_create_user.assert_called_once()
            mock_set_claims.assert_called_once_with("test-uid-123", {"role": "brand"})
            
            # Verify database document creation
            mock_db.collection.assert_called_with("brands")
            mock_collection.document.assert_called_with("test-uid-123")
    
    def test_brand_registration_missing_website(self):
        """Test brand registration fails without website"""
        payload = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "role": "brand",
            "company_name": "Example Corp"
            # Missing website
        }
        
        response = requests.post(REGISTER_ENDPOINT, json=payload)
        assert response.status_code == 422
        data = response.json()
        assert "website" in data["detail"].lower()
    
    def test_brand_registration_missing_company_name(self):
        """Test brand registration fails without company name"""
        payload = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "role": "brand",
            "website": "https://example.com"
            # Missing company_name
        }
        
        response = requests.post(REGISTER_ENDPOINT, json=payload)
        assert response.status_code == 422
        data = response.json()
        assert "company name" in data["detail"].lower()
    
    def test_brand_registration_email_domain_mismatch(self):
        """Test brand registration fails when email domain doesn't match website"""
        payload = {
            "email": "<EMAIL>",  # Different domain
            "password": "securepassword123",
            "role": "brand",
            "company_name": "Example Corp",
            "website": "https://example.com"
        }
        
        with patch('firebase_admin.auth.create_user') as mock_create_user:
            mock_user = MagicMock()
            mock_user.uid = "test-uid-123"
            mock_create_user.return_value = mock_user
            
            response = requests.post(REGISTER_ENDPOINT, json=payload)
            assert response.status_code == 422
            data = response.json()
            assert "domain" in data["detail"].lower()
            assert "match" in data["detail"].lower()
    
    def test_brand_registration_invalid_website_url(self):
        """Test brand registration fails with invalid website URL"""
        payload = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "role": "brand",
            "company_name": "Example Corp",
            "website": "not-a-valid-url"
        }
        
        with patch('firebase_admin.auth.create_user') as mock_create_user:
            mock_user = MagicMock()
            mock_user.uid = "test-uid-123"
            mock_create_user.return_value = mock_user
            
            response = requests.post(REGISTER_ENDPOINT, json=payload)
            assert response.status_code == 422
            data = response.json()
            assert "invalid" in data["detail"].lower()
    
    def test_brand_registration_www_domain_handling(self):
        """Test that www. prefix is properly handled in domain extraction"""
        payload = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "role": "brand",
            "company_name": "Example Corp",
            "website": "https://www.example.com"  # With www prefix
        }
        
        with patch('firebase_admin.auth.create_user') as mock_create_user, \
             patch('firebase_admin.auth.set_custom_user_claims'), \
             patch('firebase.config.get_db') as mock_get_db:
            
            mock_user = MagicMock()
            mock_user.uid = "test-uid-123"
            mock_create_user.return_value = mock_user
            
            mock_db = MagicMock()
            mock_collection = MagicMock()
            mock_document = MagicMock()
            mock_db.collection.return_value = mock_collection
            mock_collection.document.return_value = mock_document
            mock_get_db.return_value = mock_db
            
            response = requests.post(REGISTER_ENDPOINT, json=payload)
            assert response.status_code == 200
            
            # Verify the domain was extracted correctly (without www)
            call_args = mock_document.set.call_args[0][0]
            assert call_args["domain"] == "example.com"
            assert call_args["website"] == "https://www.example.com"

if __name__ == "__main__":
    # Run tests
    test_instance = TestBrandRegistration()
    
    print("Testing brand registration functionality...")
    
    try:
        test_instance.test_brand_registration_missing_website()
        print("✓ Missing website validation test passed")
        
        test_instance.test_brand_registration_missing_company_name()
        print("✓ Missing company name validation test passed")
        
        print("\nAll validation tests passed!")
        print("Note: Integration tests require a running server and proper mocking.")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
