import os
import sys
import pytest

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.routes.click import build_admesh_link

def test_build_admesh_link_development():
    """Test that build_admesh_link adds test=true in development environment"""
    # Save the original ENV value
    original_env = os.environ.get("ENV")

    try:
        # Set ENV to development
        os.environ["ENV"] = "development"

        # Build a link
        link = build_admesh_link(
            ad_id="test-ad-id",
            product_id="test-product-id",
            redirect_url="https://example.com"
        )

        # Check that test=true is included
        assert "test=true" in link

    finally:
        # Restore the original ENV value
        if original_env is not None:
            os.environ["ENV"] = original_env
        else:
            del os.environ["ENV"]

def test_build_admesh_link_production():
    """Test that build_admesh_link does not add test=true in production environment"""
    # Save the original ENV value
    original_env = os.environ.get("ENV")

    try:
        # Set ENV to production
        os.environ["ENV"] = "production"

        # Build a link
        link = build_admesh_link(
            ad_id="test-ad-id",
            product_id="test-product-id",
            redirect_url="https://example.com"
        )

        # Check that test=true is not included
        assert "test=true" not in link

    finally:
        # Restore the original ENV value
        if original_env is not None:
            os.environ["ENV"] = original_env
        else:
            del os.environ["ENV"]

def test_build_admesh_link_explicit_test():
    """Test that build_admesh_link adds test=true when is_test=True regardless of environment"""
    # Save the original ENV value
    original_env = os.environ.get("ENV")

    try:
        # Set ENV to production
        os.environ["ENV"] = "production"

        # Build a link with is_test=True
        link = build_admesh_link(
            ad_id="test-ad-id",
            product_id="test-product-id",
            redirect_url="https://example.com",
            is_test=True
        )

        # Check that test=true is included
        assert "test=true" in link

    finally:
        # Restore the original ENV value
        if original_env is not None:
            os.environ["ENV"] = original_env
        else:
            del os.environ["ENV"]

def test_build_admesh_link_all_params():
    """Test that build_admesh_link includes all parameters in UTM format only"""
    link = build_admesh_link(
        ad_id="test-ad-id",
        product_id="test-product-id",
        redirect_url="https://example.com",
        rec_id="test-rec-id",
        session_id="test-session-id",
        agent_id="test-agent-id",
        user_id="test-user-id"
    )

    # Check that original parameters are NOT included
    assert "product_id=test-product-id" not in link
    assert "redirect_url=https%3A%2F%2Fexample.com" not in link
    assert "rec_id=test-rec-id" not in link
    assert "session_id=test-session-id" not in link
    assert "agent_id=test-agent-id" not in link
    assert "user_id=test-user-id" not in link

    # Check that all UTM parameters are included
    assert "utm_product=test-product-id" in link
    assert "utm_redirect=https%3A%2F%2Fexample.com" in link
    assert "utm_rec=test-rec-id" in link
    assert "utm_session=test-session-id" in link
    assert "utm_agent=test-agent-id" in link
    assert "utm_user=test-user-id" in link

def test_build_admesh_link_minimal_params():
    """Test that build_admesh_link works with only ad_id"""
    link = build_admesh_link(
        ad_id="test-ad-id"
    )

    # Check that only ad_id is included in the base URL
    assert "/click/r/test-ad-id" in link

    # Check that original parameters are not included
    assert "product_id=" not in link
    assert "redirect_url=" not in link
    assert "rec_id=" not in link
    assert "session_id=" not in link
    assert "agent_id=" not in link
    assert "user_id=" not in link

    # Check that UTM parameters are not included
    assert "utm_product=" not in link
    assert "utm_redirect=" not in link
    assert "utm_rec=" not in link
    assert "utm_session=" not in link
    assert "utm_agent=" not in link
    assert "utm_user=" not in link

    # Check that test=true is included in development environment
    env = os.environ.get("ENV", "development")
    if env == "development":
        assert "test=true" in link
