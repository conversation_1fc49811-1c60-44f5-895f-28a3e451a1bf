import requests
import os
import json
import firebase_admin
from firebase_admin import auth, credentials
import time

# Initialize Firebase Admin SDK for testing
if not firebase_admin._apps:
    cred = credentials.Certificate("firebase/serviceAccountKey.json")
    firebase_admin.initialize_app(cred)

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

# Set your token here after getting it from Firebase Auth
TOKEN = None  # Replace with a valid Firebase ID token

def test_update_role():
    """Test the /auth/update-role endpoint"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test updating to agent role
    payload = {
        "role": "agent"
    }
    
    response = requests.post(f"{API_BASE_URL}/auth/update-role", headers=headers, json=payload)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_admin_update_role():
    """Test the /admin/update-user-role endpoint (requires admin privileges)"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Replace with the user ID you want to update
    target_user_id = "USER_ID_TO_UPDATE"
    
    payload = {
        "user_id": target_user_id,
        "role": "brand",
        "onboarding_completed": False
    }
    
    response = requests.post(f"{API_BASE_URL}/admin/update-user-role", headers=headers, json=payload)
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_set_admin_status():
    """Test the /admin/set-admin-status endpoint (requires admin privileges)"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    # Replace with the user ID you want to make an admin
    target_user_id = "USER_ID_TO_MAKE_ADMIN"
    
    response = requests.post(
        f"{API_BASE_URL}/admin/set-admin-status?user_id={target_user_id}&is_admin=true", 
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def test_get_user_by_email():
    """Test the /admin/get-user-by-email endpoint (requires admin privileges)"""
    if not TOKEN:
        print("Please set a valid Firebase token in the TOKEN variable")
        return
    
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    
    # Replace with the email you want to look up
    email = "<EMAIL>"
    
    response = requests.get(
        f"{API_BASE_URL}/admin/get-user-by-email?email={email}", 
        headers=headers
    )
    
    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(json.dumps(data, indent=2))
    else:
        print(f"Error: {response.text}")

def make_first_admin():
    """Helper function to make the first admin user"""
    # This should be run only once to create the first admin
    # Replace with the UID of the user you want to make an admin
    user_id = "FIRST_ADMIN_USER_ID"
    
    try:
        # Get current custom claims
        user = auth.get_user(user_id)
        custom_claims = dict(user.custom_claims or {})
        
        # Add admin claim
        custom_claims["admin"] = True
        
        # Update custom claims
        auth.set_custom_user_claims(user_id, custom_claims)
        
        print(f"Successfully set admin status for user {user_id}")
        print(f"Email: {user.email}")
        print(f"Custom claims: {custom_claims}")
    except Exception as e:
        print(f"Error setting admin status: {e}")

if __name__ == "__main__":
    # Uncomment to create the first admin user
    # make_first_admin()
    
    # Run tests
    print("Testing /auth/update-role endpoint:")
    test_update_role()
    
    print("\nTesting /admin/update-user-role endpoint:")
    test_admin_update_role()
    
    print("\nTesting /admin/set-admin-status endpoint:")
    test_set_admin_status()
    
    print("\nTesting /admin/get-user-by-email endpoint:")
    test_get_user_by_email()
