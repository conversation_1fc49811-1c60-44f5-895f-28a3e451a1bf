# Custom Claims Management

This document describes the custom claims management system for AdMesh, which allows updating and retrieving Firebase custom claims for users.

## Overview

Firebase custom claims are used to store user-specific information that can be accessed in security rules and on the client side. They are limited to 1000 bytes per user.

The custom claims management system provides the following functionality:

1. **Update Custom Claims**: Update custom claims for a user (admin only)
2. **Get Custom Claims**: Get custom claims for a user (admin only)
3. **Verify Email**: Mark a user's email as verified in custom claims
4. **Add Badge**: Add a badge to a user's custom claims

## API Endpoints

### Admin Endpoints

- `POST /auth/custom-claims/update`: Update custom claims for a user
- `GET /auth/custom-claims/{user_id}`: Get custom claims for a user

### User Endpoints

- `POST /auth/custom-claims/verify-email`: Mark a user's email as verified in custom claims
- `POST /auth/custom-claims/badge/{badge_id}`: Add a badge to a user's custom claims

## Usage Examples

### Update Custom Claims

```bash
curl -X POST "http://localhost:8000/auth/custom-claims/update" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "USER_ID",
    "claims": {
      "premium": true,
      "subscription_level": "pro"
    },
    "merge": true
  }'
```

### Get Custom Claims

```bash
curl -X GET "http://localhost:8000/auth/custom-claims/USER_ID" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Verify Email

```bash
curl -X POST "http://localhost:8000/auth/custom-claims/verify-email" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

### Add Badge

```bash
curl -X POST "http://localhost:8000/auth/custom-claims/badge/agent_pioneer" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"
```

## Common Custom Claims

AdMesh uses the following custom claims:

- `role`: User role (user, agent, brand, admin)
- `email_verified`: Whether the user's email is verified
- `badges`: List of badges earned by the user
- `onboarding_completed`: Whether the user has completed onboarding (for brands)
- `admin`: Whether the user is an admin

## Testing

To test the custom claims endpoints, you can use the provided script:

```bash
python scripts/test_custom_claims.py --admin-email <EMAIL> --user-id USER_ID --user-token USER_TOKEN --badge-id agent_pioneer
```

## Limitations

- Custom claims are limited to 1000 bytes per user
- Custom claims are included in the ID token, which is sent with every authenticated request
- Changes to custom claims may take up to an hour to propagate to all users
- To force a refresh of custom claims, the user must sign out and sign in again, or call `getIdTokenResult(true)` on the client side
