Here's the **updated `schema.md` for AdMesh**, now using `products/` instead of `tools/` and reflecting the latest structure:

---

### Firestore Schema (AdMesh)

---

#### **products/**
Stores all known tools/products.

- **Doc ID:** slugified product title (e.g. `notion`, `zapier`, `grammarly`)
- **Fields:**
  - `title` (string)
  - `url` (string)
  - `admesh_link` (string) — generic fallback link (no offer)
  - `category`, `keywords[]`, `audience`
  - `has_free_tier` (bool), `is_open_source` (bool), `is_ai_powered` (bool)
  - `confidence_score` (float)
  - `created_at`, `last_viewed_at` (ISO timestamp)
  - `view_count`, `clicks`, `conversions` (int)
  - `active_offers`,`inactive_offers`
  - `pricing_url` (string) — URL to the product's pricing page
  - `audience_segment` (string) — Target audience (e.g., "Startups", "Agencies", "Developers")
  - `integration_list` (array of strings) — List of integrations the product supports

---

#### **offers/**
Performance-based structured offers by brands.

- **Doc ID:** `offer_id` (UUID or slug)
- **Fields:**
  - `product_id` (string, ref to `products/`)
  - `ad_id` (string, used in redirect)
  - `url` (landing page to redirect)
  - `reward_note` (string)
  - `payout.amount` (float)
  - `payout.currency` (string)
  - `payout.model` ("CPA" | "CPC")
  - `budget` (float)
  - `active` (bool)
  - `created_at`, `valid_until` (timestamp)
  - `brand_id`, `campaign_name` (optional)
  - `tracking` (object) - URLs for tracking clicks and conversions
  - `integration` (object):
    - `method` (string): "redirect_pixel", "server_api", or "manual"
    - `webhook_url` (string, optional): For server-side API integration
    - `notes` (string, optional): Additional integration notes
    - `updated_at` (timestamp)

---

#### **recommendations/**
Session-level product recommendations.

- **Doc ID:** `recommendation_id` (UUID)
- **Fields:**
  - `product_ids[]` (list of product doc IDs)
  - `ad_ids[]` (list of offer ad_ids used in this session)
  - `query` (string)
  - `session_id`, `agent_id`, `user_id`
  - `created_at` (timestamp)

---

#### **queries/**
Track all queries in a session.

- **Doc ID:** `session_id` (UUID)
- **Fields:**
  - `agent_id`, `user_id`
  - `created_at` (timestamp)
  - `queries[]`: array of:
    ```json
    {
      "query": "best ai crm",
      "intent": { ... },
      "recommendation_ids": ["notion", "flowrite"],
      "model_used": "mistral",
      "timestamp": "..."
    }
    ```

---

#### **clicks/**
When a user clicks a product or offer.

- **Doc ID:** Auto-generated
- **Fields:**
  - `recommendation_id`, `session_id`, `agent_id`, `user_id`, `timestamp`

---

#### **conversions/**
Logged on successful user actions.

- **Doc ID:** Auto-generated
- **Fields:**
  - `offer_id`, `ad_id`, `product_id`
  - `user_id`, `agent_id`, `session_id`
  - `conversion_type`: "signup", "purchase", "install"
  - `value`, `payout`, `timestamp`

---

#### **brands/**
Verified brands that submit offers.

- **Doc ID:** Firebase UID
- **Fields:**
  - `email`, `company_name`, `domain`, `logo_url`, `industry`
  - `verified`, `offers_submitted`
  - `budget_used`, `budget_remaining`
  - `payment_method`, `billing_country`, `account_manager`
  - `created_at`
  - `active_products[]: string[]`
  - `inactive_products[]: string[]`
  - `active_offers[]: string[]`
  - `inactive_offers[]: string[]`
  - `conversion_verification` (object):
    - `method` (string): "redirect_pixel", "server_api", or "manual"
    - `webhook_url` (string, optional): For server-side API integration
    - `notes` (string, optional): Additional integration notes
    - `updated_at` (timestamp)


---

#### **agents/**
Human or AI agents recommending products.

- **Doc ID:** Firebase UID
- **Fields:**
  - `email`, `name`, `tier`, `clicks`, `conversions`, `total_earnings`
  - `joined_at`, `referrals`, `keywords[]`, `trust_score`, `payout_account`

---

#### **users/**
End users using AdMesh.

- **Doc ID:** Firebase UID
- **Fields:**
  - `email`, `platform`, `location`
  - `clicks_made`, `conversions`
  - `referrer_agent`, `keywords[]`, `created_at`

---

Let me know if you want to add **wallets**, **earnings ledger**, or **usage analytics** next.