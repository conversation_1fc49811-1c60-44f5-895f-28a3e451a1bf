# AdMesh Protocol — API Endpoints

This document provides all the HTTP API endpoints supported by the AdMesh Protocol.
It includes request/response formats, auth, and key usage patterns.

---

## 🔐 Auth Overview
- All endpoints require **Firebase Auth**
- Pass token in header: `Authorization: Bearer <id_token>`

---

## 🚀 Offer Endpoints

### `POST /offers/register`
Registers a new offer.

- **Auth**: Brand (Firebase)
- **Body**: `admesh.offer.json`
- **Returns**: Success message + offer ID

---

### `POST /offers/integration/setup`
Configures conversion tracking integration for an offer.

- **Auth**: Brand (Firebase)
- **Body**:
```json
{
  "offer_id": "...",
  "product_id": "...",
  "method": "redirect_pixel", // or "server_api" or "manual"
  "webhook_url": "https://yourdomain.com/webhook", // only for server_api
  "notes": "Additional integration notes" // optional
}
```
- **Returns**: Success message + tracking URLs/info

---

### `GET /offers/discover`
Agents fetch relevant offers based on intent.

- **Auth**: Agent (optional for MVP)
- **Query Params**:
  - `query`: user intent string (required)
  - `categories`: comma-separated list
  - `min_score`: minimum trust score (default 50)
  - `limit`: max offers to return (default 10)
- **Returns**: Array of offers

---

## 🖱️ Click Tracking

### `POST /click`
Logs an offer click from an agent.

- **Auth**: Agent (Firebase)
- **Body**:
```json
{
  "offer_id": "...",
  "agent_id": "...",
  "user_id": "...", // optional
  "session_id": "...",
  "timestamp": "..."
}
```
- **Returns**: Confirmation + optional redirect URL

---

## 🧾 Conversion Tracking

### `POST /conversion/log`
Logs a conversion and triggers earning attribution.

- **Auth**: Optional (MVP)
- **Body**:
```json
{
  "offer_id": "...",
  "agent_id": "...",
  "session_id": "...",
  "conversion_value": 49.99,
  "currency": "USD",
  "event_type": "purchase",
  "timestamp": "..."
}
```
- **Returns**: Success response

---

## 🪙 Earnings

### `GET /earnings/agent/{agent_id}` *(planned)*
Returns all earnings for a specific agent.

- **Auth**: Agent
- **Returns**: List of earnings

---

## ⚠️ Fraud Flagging *(internal)*

- Automatic background job
- Writes to: `flags/{auto_id}`
- Types: `click_flood`, `no_conversion_spam`, `ctr_suspicious`

---

## 📘 Related Docs
- [`offer-schema.md`](./offer-schema.md)
- [`trust-score.md`](./trust-score.md)
- [`earnings-model.md`](./earnings-model.md)
- [`fraud-detection.md`](./fraud-detection.md)
