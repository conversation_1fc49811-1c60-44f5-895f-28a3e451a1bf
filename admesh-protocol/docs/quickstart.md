# AdMesh Protocol Quickstart

Welcome to the **AdMesh Protocol**! This guide walks you through how to quickly get started as a:
- ✅ Brand (submit offers)
- 🤖 Agent (fetch + promote offers)

---

## 🔐 Step 1: Authentication

AdMesh uses **Firebase Auth** for secure access.

### 🔑 Get Started:
1. Create a Firebase account and enable **Email/Password Auth**.
2. Use Firebase Admin SDK in your backend to verify ID tokens.
3. Add the `Authorization: Bearer <ID_TOKEN>` header to all API requests.

---

## 🏢 For Brands: Submit Offers

### 1. Register an Offer
```http
POST /offers/register
Authorization: Bearer <brand_token>
```

#### Sample Payload:
```json
{
  "id": "offer_001",
  "title": "Get 20% off on productivity tools",
  "description": "Exclusive deal for remote workers on ProjectZen Pro.",
  "url": "https://projectzen.com/deal",
  "suggestion_reason": "Great for teams scaling async workflows.",
  "reward_note": "Users get 20% off. Agents earn $5 per signup.",
  "payout": {
    "amount": 5,
    "currency": "USD",
    "model": "CPA"
  },
  "categories": ["productivity", "remote work"],
  "trust_score": 50,
  "budget": 500,
  "brand_id": "brand_xyz",
  "created_at": "2025-03-31T10:00:00Z"
}
```

---

## 🤖 For Agents: Discover Offers

### 1. Fetch Relevant Offers
```http
GET /offers/discover?query=crm&min_score=60&limit=5
Authorization: Bearer <agent_token>
```

#### Optional Filters:
- `categories=marketing,productivity`
- `min_score=60`
- `limit=10`

### 2. Track Clicks
```http
POST /click
Authorization: Bearer <agent_token>
```
```json
{
  "offer_id": "offer_001",
  "agent_id": "agent_xyz",
  "user_id": "user_789",
  "session_id": "sess_123",
  "timestamp": "2025-03-31T10:05:00Z"
}
```

### 3. Log Conversions
```http
POST /conversion/log
```
```json
{
  "offer_id": "offer_001",
  "agent_id": "agent_xyz",
  "session_id": "sess_123",
  "conversion_value": 49.99,
  "currency": "USD",
  "event_type": "purchase",
  "timestamp": "2025-03-31T10:10:00Z"
}
```

---

## 🧪 Test Offers Locally

Use Postman or cURL to try offer registration, discovery, clicks, and conversions.

---

## 📘 What's Next?
- Explore the [Offer Schema](./offer-schema.md)
- Learn [How Earnings Work](./earnings-model.md)
- Understand [Trust Scoring](./trust-score.md)
- Dive into [Fraud Detection Rules](./fraud-detection.md)

You're now ready to build with AdMesh 🚀
