# AdMesh Email System

This document describes the email system for AdMesh, which uses Resend for sending emails, managing audiences, and sending broadcasts.

## Overview

The email system provides the following functionality:

1. **Audience Management**: Create and manage audiences in Resend
2. **User Management**: Add users to audiences when they sign up
3. **Broadcast Emails**: Send broadcast emails to audiences
4. **Scheduled Emails**: Send weekly leaderboard position emails and streak reminder emails

## Setup

### Prerequisites

1. Resend API key (set in environment variable `RESEND_API_KEY`)
2. Firebase project with Firestore database

### Initial Setup

1. Run the script to create the initial "Registered Users" audience and add all existing users:

```bash
python scripts/create_audience.py
```

## API Endpoints

### Email Sending

- `POST /email/send`: Send an email to specific recipients
- `POST /email/test`: Send a test email (for testing purposes)

### Audience Management

- `POST /email/audience/create`: Create a new audience in Resend
- `GET /email/audience/list`: List all audiences in Resend
- `POST /email/audience/add-user`: Add a user to an audience
- `POST /email/audience/add-all-users`: Add all registered users to an audience

### Broadcast Emails

- `POST /email/broadcast/send`: Send a broadcast email to an audience

### Webhooks

- `POST /email/webhook/user-signup`: Webhook to add a new user to an audience when they sign up

### Scheduled Emails

- `POST /email/send-weekly-leaderboard`: Send weekly leaderboard position emails to all users

## Integration with User Registration

The email system is integrated with the user registration process:

1. When a user signs up with email or Google, they are automatically added to the "Registered Users" audience
2. This allows sending broadcast emails to all registered users

## Scheduled Tasks

The following scheduled tasks are set up:

1. **Weekly Leaderboard Emails**: Sent every Monday at 9:00 AM
2. **Streak Reminder Emails**: Sent every day at 8:00 PM to users who are about to break their daily streak

## Testing

To send a test broadcast email to all registered users:

```bash
python scripts/send_test_broadcast.py
```

## Unsubscribe Management

Resend handles unsubscribe management automatically. The unsubscribe link is included in all emails using the `{{ RESEND_UNSUBSCRIBE_URL }}` template variable.

## Troubleshooting

If emails are not being sent:

1. Check the Resend API key is correctly set
2. Check the Resend dashboard for any errors
3. Check the logs for any error messages

## Data Structure

### Firestore Collections

- `email_broadcasts`: Logs of broadcast emails sent
- `email_tasks`: Logs of background tasks related to emails
