# AdMesh Trust Score System

The **trust score** is a dynamic reputation metric applied to every offer (and later, agents).
It helps rank high-performing, high-quality offers while filtering out spam or low-value content.

## 🎯 Purpose
- Boost visibility of trusted offers
- Penalize clickbait or poor conversion offers
- Incentivize real value to users

## 🔢 Trust Score Range

**Range**: 0 to 100

- **0–40**: Low trust, flagged or poorly converting offers
- **41–70**: Average to good offers
- **71–100**: Top-performing, high-integrity offers

## 📈 MVP Trust Score Formula

```
trust_score = base + (conversion_rate × 100) - penalty_factor
```

Where:
- `conversion_rate` = conversions / clicks
- `base` = 50 (default starting score)
- `penalty_factor` = fraud flags, no-conversion spam, etc.

## ⚙️ Example

| Offer   | Clicks | Conversions | CR  | Score |
|---------|--------|-------------|-----|-------|
| Offer A | 100    | 10         | 10% | 60    |
| Offer B | 200    | 0          | 0%  | 45 (penalized) |
| Offer C | 50     | 35         | 70% | 120 → capped to 100 |

## 🔄 Update Frequency
- Updated **weekly** or **per X conversions**
- Via background job (script or Cloud Scheduler)

## 🧪 Trust Score Engine (Firestore)
1. Fetch click + conversion counts per offer
2. Calculate CR
3. Apply penalties if flagged
4. Write back to `offers/{id}.trust_score`

## 🔮 Future Enhancements
- Track **agent trust score** separately
- Include **offer freshness**, **CTR trends**, and **user feedback**
- Use ML models for anomaly/fraud detection

## 📘 Related Docs
- [`earnings-model.md`](./earnings-model.md)
- [`fraud-detection.md`](./fraud-detection.md)
- [`endpoints.md`](./endpoints.md)
