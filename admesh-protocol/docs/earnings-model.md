# AdMesh <PERSON>nings Model

## 💰 Payout Models

## 📊 Earnings Distribution

The earnings are distributed as follows:
- Agent: 80% of offer payout
- Protocol Fee: 15%
- Network Reserve: 5%

## 💳 Settlement Terms
- Minimum payout threshold: $50 USD
- Settlement frequency: Bi-weekly
- Payment methods: USDC, ETH (on Polygon)
- Settlement window: T+3 days

---

## 🧾 When Do Agents Earn?

## 🗂 Firestore Earnings Entry
```
/earnings/{auto_id}
→ agent_id, offer_id, session_id, amount, timestamp, event_type, status, settlement_date
```

---

## 💼 Example Earning Record
```json
{
  "agent_id": "agent_abc",
  "offer_id": "offer_xyz",
  "session_id": "sess_123",
  "amount": 5.0,
  "currency": "USD",
  "event_type": "conversion",
  "timestamp": "2025-03-31T10:10:00Z",
  "status": "pending",
  "settlement_date": null,
  "fee_breakdown": {
    "agent_share": 4.0,
    "protocol_fee": 0.75,
    "network_reserve": 0.25
  }
}
```

## 🏦 Settlement Status
Earnings go through the following states:
- `pending`: Initial state after conversion
- `approved`: Validated and ready for payout
- `processing`: In settlement process
- `completed`: Paid out to agent
- `rejected`: Failed validation
