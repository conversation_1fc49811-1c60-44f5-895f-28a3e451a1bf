# AdMesh Fraud Detection System

The fraud detection system in AdMesh helps protect the integrity of the protocol and prevent abuse by detecting suspicious activity patterns across offers, agents, and sessions.

---

## 🎯 Goal
- Detect spam clicks or bots
- Penalize offers or agents with low-quality intent
- Flag sessions and events for review (or auto action in future)

---

## 🚩 Types of Fraud Flags (MVP)

| Flag Type          | Trigger Condition                                             | Actor Impacted      |
|--------------------|----------------------------------------------------------------|----------------------|
| `click_flood`      | Multiple offers clicked within 5 seconds in same session       | session_id           |
| `no_conversion_spam` | 100+ clicks, 0 conversions in 24h for an offer               | offer_id, agent_id   |
| `self_click`       | Agent ID equals brand ID (self-promoting)                     | agent_id, brand_id   |
| `ctr_suspicious`   | Conversion rate > 80% over 50+ clicks                          | agent_id             |
| `missing_linkage`  | Conversion with no preceding click or session linkage         | offer_id             |

---

## 🗂 Firestore Structure
```
/flags/{auto_id}
→ type, actor_id, offer_id, session_id, reason, score, detected_at
```

---

## 🛠 Engine Logic (MVP Script)
- Triggered periodically (or post-event)
- Scans click/conversion logs
- Writes to `/flags` if suspicious behavior found
- Updates `trust_score` accordingly

---

## 🔒 Penalty Options
- Lower offer trust score
- Hide offer from discovery feed
- Blacklist agents (future release)

---

## 🧠 Future Roadmap
- Machine-learning based fraud classification
- IP + device fingerprinting
- Collaborative reputation graph (agent/brand)
- Real-time detection + action engine

---

## 📘 Related Docs
- [`trust-score.md`](./trust-score.md)
- [`earnings-model.md`](./earnings-model.md)
- [`endpoints.md`](./endpoints.md)