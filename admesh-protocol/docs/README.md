# AdMesh Protocol

**AdMesh** is an open protocol for agent-native, intent-driven offer monetization.

It enables:
- Agents (GPTs, extensions, apps) to monetize user intent
- Brands to register structured offers with payout rules
- Attribution via click + conversion tracking
- Transparent earnings & trust scoring

Inspired by OpenRTB, Stripe, and the future of agent commerce.

---

## 🔁 Core Flow

1. **Brands** register offers: `POST /offers/register`
2. **Agents** query offers: `GET /offers/discover`
3. **Users click**: `POST /click`
4. **Users convert**: `POST /conversion/log`
5. **Agents earn**: stored in `/earnings`
6. **Trust & fraud**: handled automatically by protocol engine

---

## 🔐 Auth

- Firebase Auth (ID token in `Authorization: Bearer <token>`)
- Brands & Agents must register via Firebase for secure usage

---

## 💡 Tech Stack

| Component      | Stack               |
|----------------|---------------------|
| Backend API    | FastAPI (Python)    |
| Database       | Firestore           |
| Auth           | Firebase Auth       |
| Hosting        | Google Cloud Run    |

---

## 📦 Offer Schema

Structured JSON offers based on `admesh.offer.json`.  
See [`offer-schema.md`](./offer-schema.md) for details.

---

## 🧠 Trust Score

Built-in scoring system based on real engagement.  
See [`trust-score.md`](./trust-score.md)

---

## 💰 Earnings Model

Transparent CPA-based earnings for agents.  
See [`earnings-model.md`](./earnings-model.md)

---

## 🛡️ Fraud Detection

Real-time fraud rule engine flags abuse.  
See [`fraud-detection.md`](./fraud-detection.md)

---

## 🧪 Examples

Sample payloads:  
→ [`examples/sample-offer.json`](./examples/sample-offer.json)  
→ [`examples/sample-click.json`](./examples/sample-click.json)  
→ [`examples/sample-conversion.json`](./examples/sample-conversion.json)
