# AdMesh Email Templates

This document describes the email templates available in the AdMesh system.

## Available Templates

### 1. Welcome Email

**Purpose**: Welcomes new users to AdMesh after they sign up.

**Template File**: `templates/email_welcome.html`

**Key Features**:
- Modern black and white design with gold accents
- Personalized greeting with user's name
- Overview of next steps for new users
- Link to the dashboard
- Responsive design for mobile devices

**Usage**:
```bash
# Send to a specific user
python scripts/send_welcome_email.py --email <EMAIL> --name "User Name"
```

### 2. Agent Pioneer Program Introduction

**Purpose**: Introduces users to the Agent Pioneer Program, explaining how they can earn XP and badges.

**Template File**: `templates/email_agent_pioneer_intro.html`

**Key Features**:
- Modern dark theme design
- Explains the Agent Pioneer Program concept
- Highlights the Agent Pioneer badge (1000 XP milestone)
- Provides information on how to earn XP
- Includes a call-to-action button

**Usage**:
```bash
# Send to a specific user
python scripts/send_agent_pioneer_intro.py --email <EMAIL> --name "User Name"

# Send to all eligible users
python scripts/send_agent_pioneer_intro.py --all-eligible
```

### 3. Weekly Update

**Purpose**: Provides users with a weekly summary of their progress, including XP earned, leaderboard position, and badge progress.

**Template File**: `templates/email_weekly_update.html`

**Key Features**:
- Shows current XP and leaderboard position
- Displays progress toward key badges (Agent Pioneer and Weekly Active)
- Lists recently earned badges
- Provides tips for earning more XP
- Includes links to the leaderboard and badges pages

**Usage**:
```bash
# Send to all users
python scripts/send_weekly_update.py

# Send a test email using this template
python scripts/send_test_broadcast.py --template weekly
```



## Template Variables

The templates use the following variables:

### Common Variables
- `first_name`: User's first name (falls back to "there" if not available)
- `RESEND_UNSUBSCRIBE_URL`: Automatically replaced by Resend with an unsubscribe link

### Welcome Email Variables
- No additional variables beyond common variables

### Agent Pioneer Introduction Variables
- No additional variables beyond common variables

### Weekly Update Variables
- `xp`: User's current XP
- `position`: User's position on the leaderboard
- `login_streak`: Number of consecutive days the user has logged in
- `pioneer_progress`: Percentage progress toward the Agent Pioneer badge (0-100)
- `weekly_progress`: Percentage progress toward the Weekly Active badge (0-100)
- `recent_badges`: List of badges earned in the last 7 days, each with:
  - `name`: Badge name
  - `description`: Badge description
  - `xp_bonus`: XP bonus awarded for the badge

## Adding New Templates

To add a new email template:

1. Create an HTML file in the `templates` directory
2. Update the `send_test_broadcast.py` script to include the new template option
3. Create a dedicated script for sending the email if needed
4. Update this documentation

## Testing Templates

You can test the weekly update template using the `send_test_broadcast.py` script:

```bash
python scripts/send_test_broadcast.py --template weekly
```

For the welcome email and Agent Pioneer introduction, use their dedicated scripts:

```bash
# Test welcome email
python scripts/send_welcome_email.py --email <EMAIL> --name "Test User"

# Test Agent Pioneer introduction
python scripts/send_agent_pioneer_intro.py --email <EMAIL> --name "Test User"
```
