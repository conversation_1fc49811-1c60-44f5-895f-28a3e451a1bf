# AdMesh Protocol Configuration System

This directory contains the environment-based configuration system for AdMesh Protocol. The configuration system automatically detects the deployment environment and loads appropriate settings for Firebase, API endpoints, external services, and security configurations.

## Architecture

The configuration system follows a hierarchical structure:

```
config/
├── __init__.py              # Package initialization
├── base.py                  # Base configuration class with common functionality
├── development.py           # Development environment configuration
├── staging.py              # Staging environment configuration
├── production.py           # Production environment configuration
├── config_manager.py       # Configuration manager and utilities
└── README.md               # This file
```

## Key Features

- **Automatic Environment Detection**: Detects environment based on deployment context
- **Environment-Specific Settings**: Different configurations for dev, staging, and production
- **Secure Credential Management**: Proper handling of sensitive credentials
- **Validation**: Built-in validation for required environment variables
- **Singleton Pattern**: Ensures consistent configuration across the application
- **Fallback Support**: Graceful fallback to development configuration

## Usage

### Basic Usage

```python
from config.config_manager import get_config

# Get the current configuration
config = get_config()

# Access configuration properties
print(f"Environment: {config.environment}")
print(f"API Base URL: {config.api_base_url}")
print(f"Firebase Project: {config.firebase_config['project_id']}")
```

### Specific Configuration Access

```python
from config.config_manager import (
    get_firebase_config,
    get_api_base_url,
    get_cors_origins,
    is_debug,
    get_environment
)

# Get specific configuration values
firebase_config = get_firebase_config()
api_url = get_api_base_url()
cors_origins = get_cors_origins()
debug_mode = is_debug()
current_env = get_environment()
```

### Configuration Manager

```python
from config.config_manager import config_manager

# Get all configuration as dictionary
all_config = config_manager.get_all_config()

# Reload configuration (useful for testing)
config_manager.reload_config()

# Access external services configuration
external_services = config_manager.get_external_services()
feature_flags = config_manager.get_feature_flags()
```

## Environment Detection

The system detects the environment using the following priority:

1. `ENV` or `ENVIRONMENT` environment variable
2. Cloud Run detection (`K_SERVICE` environment variable)
3. Default to `development` for local environments

## Configuration Classes

### BaseConfig

The base configuration class provides common functionality:

- Environment detection
- Credential path resolution
- Configuration validation
- Abstract methods for environment-specific settings

### DevelopmentConfig

Development environment configuration:

- Uses `admesh-dev` Firebase project
- Enables debug mode and verbose logging
- Permissive CORS settings
- Optional Firestore emulator support

### StagingConfig

Staging environment configuration:

- Uses `admesh-staging` Firebase project
- Balanced security and debugging
- Restricted CORS to staging domains
- Test mode for external services

### ProductionConfig

Production environment configuration:

- Uses `admesh-prod` Firebase project
- Maximum security settings
- Minimal logging
- Live mode for external services
- Performance optimizations

## Environment Variables

### Required for All Environments

```bash
GOOGLE_APPLICATION_CREDENTIALS  # Firebase credentials
OPENAI_API_KEY                 # OpenAI API key
RESEND_API_KEY                 # Email service API key
```

### Environment-Specific Variables

#### Staging
```bash
FIREBASE_API_KEY_STAGING
FIREBASE_MESSAGING_SENDER_ID_STAGING
FIREBASE_APP_ID_STAGING
STRIPE_SECRET_KEY_STAGING
STRIPE_WEBHOOK_SECRET_STAGING
```

#### Production
```bash
FIREBASE_API_KEY_PROD
FIREBASE_MESSAGING_SENDER_ID_PROD
FIREBASE_APP_ID_PROD
STRIPE_SECRET_KEY_PROD
STRIPE_WEBHOOK_SECRET_PROD
```

## Validation

The configuration system includes built-in validation:

```python
# Validation is automatically performed during initialization
config = get_config()  # Will raise ValueError if validation fails

# Manual validation
try:
    config.validate_config()
    print("Configuration is valid")
except ValueError as e:
    print(f"Configuration error: {e}")
```

## Testing

Test the configuration system:

```bash
# Run configuration tests
python scripts/test_environment_config.py

# Validate current environment
python scripts/validate_environment.py
```

## Integration with Firebase

The configuration system integrates with Firebase initialization:

```python
from firebase.config import initialize_firebase
from config.config_manager import get_firebase_config

# Firebase automatically uses the configuration system
db = initialize_firebase()

# Manual access to Firebase config
firebase_config = get_firebase_config()
```

## Integration with FastAPI

The main application uses the configuration system:

```python
from config.config_manager import get_config, get_cors_origins, is_debug

config = get_config()

app = FastAPI(
    title="AdMesh Protocol API",
    debug=is_debug()
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## Best Practices

1. **Always use the configuration manager** instead of directly accessing environment variables
2. **Validate configuration** before deploying to production
3. **Use environment-specific credentials** to isolate environments
4. **Test configuration changes** in staging before production
5. **Monitor logs** for configuration-related errors
6. **Keep sensitive data** in environment variables, not in code

## Troubleshooting

### Common Issues

1. **Missing environment variables**: Run the validation script to identify missing variables
2. **Firebase initialization fails**: Check credentials path and project ID
3. **Configuration not updating**: Use `config_manager.reload_config()` to force reload
4. **Import errors**: Ensure the config directory is in the Python path

### Debug Information

Enable debug logging to see configuration details:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from config.config_manager import get_config
config = get_config()
```

## Contributing

When adding new configuration options:

1. Add the property to the appropriate base class or environment-specific class
2. Update the validation logic if the option is required
3. Add tests for the new configuration option
4. Update documentation

## Security Considerations

- Never commit credentials to version control
- Use environment-specific Firebase projects
- Validate all configuration inputs
- Use secure defaults for production
- Monitor configuration access in logs
