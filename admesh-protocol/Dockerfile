# Base image
FROM python:3.12-slim

# Set environment
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8080
ENV ENV=production

# Set working directory
WORKDIR /app

# Install system deps
RUN apt-get update && apt-get install -y build-essential libffi-dev gcc && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Python deps
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir email-validator dnspython python-multipart

# Create directory for logs
RUN mkdir -p /var/log/app && \
    touch /var/log/app/app.log && \
    chmod 777 /var/log/app/app.log

# Copy all code
COPY . .

# Create directory for Firebase credentials
RUN mkdir -p /firebase

# Expose the port Gun<PERSON> will run on
EXPOSE 8080

# Default command - use environment variable for port
C<PERSON> ["sh", "-c", "exec gunicorn api.main:app -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:${PORT:-8080} --log-level debug --timeout 120"]
