# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: true
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
annotated-types==0.6.0
    # via pydantic
anyio==4.4.0
    # via admesh-python
    # via httpx
argcomplete==3.1.2
    # via nox
certifi==2023.7.22
    # via httpcore
    # via httpx
colorlog==6.7.0
    # via nox
dirty-equals==0.6.0
distlib==0.3.7
    # via virtualenv
distro==1.8.0
    # via admesh-python
exceptiongroup==1.2.2
    # via anyio
    # via pytest
filelock==3.12.4
    # via virtualenv
h11==0.14.0
    # via httpcore
httpcore==1.0.2
    # via httpx
httpx==0.28.1
    # via admesh-python
    # via respx
idna==3.4
    # via anyio
    # via httpx
importlib-metadata==7.0.0
iniconfig==2.0.0
    # via pytest
markdown-it-py==3.0.0
    # via rich
mdurl==0.1.2
    # via markdown-it-py
mypy==1.14.1
mypy-extensions==1.0.0
    # via mypy
nest-asyncio==1.6.0
nodeenv==1.8.0
    # via pyright
nox==2023.4.22
packaging==23.2
    # via nox
    # via pytest
platformdirs==3.11.0
    # via virtualenv
pluggy==1.5.0
    # via pytest
pydantic==2.10.3
    # via admesh-python
pydantic-core==2.27.1
    # via pydantic
pygments==2.18.0
    # via rich
pyright==1.1.399
pytest==8.3.3
    # via pytest-asyncio
pytest-asyncio==0.24.0
python-dateutil==2.8.2
    # via time-machine
pytz==2023.3.post1
    # via dirty-equals
respx==0.22.0
rich==13.7.1
ruff==0.9.4
setuptools==68.2.2
    # via nodeenv
six==1.16.0
    # via python-dateutil
sniffio==1.3.0
    # via admesh-python
    # via anyio
time-machine==2.9.0
tomli==2.0.2
    # via mypy
    # via pytest
typing-extensions==4.12.2
    # via admesh-python
    # via anyio
    # via mypy
    # via pydantic
    # via pydantic-core
    # via pyright
virtualenv==20.24.5
    # via nox
zipp==3.17.0
    # via importlib-metadata
