# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: true
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
annotated-types==0.6.0
    # via pydantic
anyio==4.4.0
    # via admesh-python
    # via httpx
certifi==2023.7.22
    # via httpcore
    # via httpx
distro==1.8.0
    # via admesh-python
exceptiongroup==1.2.2
    # via anyio
h11==0.14.0
    # via httpcore
httpcore==1.0.2
    # via httpx
httpx==0.28.1
    # via admesh-python
idna==3.4
    # via anyio
    # via httpx
pydantic==2.10.3
    # via admesh-python
pydantic-core==2.27.1
    # via pydantic
sniffio==1.3.0
    # via admesh-python
    # via anyio
typing-extensions==4.12.2
    # via admesh-python
    # via anyio
    # via pydantic
    # via pydantic-core
