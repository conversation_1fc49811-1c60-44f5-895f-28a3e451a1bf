// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/debian
{
  "name": "Debian",
  "build": {
    "dockerfile": "Dockerfile",
    "context": ".."
  },

  "postStartCommand": "rye sync --all-features",

  "customizations": {
    "vscode": {
      "extensions": [
        "ms-python.python"
      ],
      "settings": { 
        "terminal.integrated.shell.linux": "/bin/bash",
        "python.pythonPath": ".venv/bin/python",
        "python.defaultInterpreterPath": ".venv/bin/python",
        "python.typeChecking": "basic",
        "terminal.integrated.env.linux": {
          "PATH": "/home/<USER>/.rye/shims:${env:PATH}"
        }
      }
    }
  },
  "features": {
    "ghcr.io/devcontainers/features/node:1": {}
  }

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Configure tool-specific properties.
  // "customizations": {},

  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "root"
}
