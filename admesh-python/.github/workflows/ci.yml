name: CI
on:
  push:
    branches-ignore:
      - 'generated'
      - 'codegen/**'
      - 'integrated/**'
      - 'stl-preview-head/**'
      - 'stl-preview-base/**'

jobs:
  lint:
    timeout-minutes: 10
    name: lint
    runs-on: ${{ github.repository == 'stainless-sdks/admesh-python' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    steps:
      - uses: actions/checkout@v4

      - name: Install Rye
        run: |
          curl -sSf https://rye.astral.sh/get | bash
          echo "$HOME/.rye/shims" >> $GITHUB_PATH
        env:
          RYE_VERSION: '0.44.0'
          RYE_INSTALL_OPTION: '--yes'

      - name: Install dependencies
        run: rye sync --all-features

      - name: Run lints
        run: ./scripts/lint

  upload:
    if: github.repository == 'stainless-sdks/admesh-python'
    timeout-minutes: 10
    name: upload
    permissions:
      contents: read
      id-token: write
    runs-on: depot-ubuntu-24.04
    steps:
      - uses: actions/checkout@v4

      - name: Get GitHub OIDC Token
        id: github-oidc
        uses: actions/github-script@v6
        with:
          script: core.setOutput('github_token', await core.getIDToken());

      - name: Upload tarball
        env:
          URL: https://pkg.stainless.com/s
          AUTH: ${{ steps.github-oidc.outputs.github_token }}
          SHA: ${{ github.sha }}
        run: ./scripts/utils/upload-artifact.sh

  test:
    timeout-minutes: 10
    name: test
    runs-on: ${{ github.repository == 'stainless-sdks/admesh-python' && 'depot-ubuntu-24.04' || 'ubuntu-latest' }}
    steps:
      - uses: actions/checkout@v4

      - name: Install Rye
        run: |
          curl -sSf https://rye.astral.sh/get | bash
          echo "$HOME/.rye/shims" >> $GITHUB_PATH
        env:
          RYE_VERSION: '0.44.0'
          RYE_INSTALL_OPTION: '--yes'

      - name: Bootstrap
        run: ./scripts/bootstrap

      - name: Run tests
        run: ./scripts/test
