#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const args = process.argv.slice(2);
const environment = args[0];

if (!environment || !['dev', 'prod', 'production'].includes(environment)) {
  console.log('❌ Please specify environment: dev or prod');
  console.log('Usage: node scripts/switch-env.js [dev|prod]');
  process.exit(1);
}

const envFile = environment === 'dev' ? '.env.dev' : '.env.production';
const envPath = path.join(__dirname, '..', envFile);
const targetPath = path.join(__dirname, '..', '.env');

if (!fs.existsSync(envPath)) {
  console.log(`❌ Environment file ${envFile} not found`);
  process.exit(1);
}

try {
  fs.copyFileSync(envPath, targetPath);
  
  // Read the environment file to show current config
  const envContent = fs.readFileSync(targetPath, 'utf8');
  const apiUrl = envContent.match(/NEXT_PUBLIC_API_BASE_URL=(.+)/)?.[1];
  const env = envContent.match(/NEXT_PUBLIC_ENVIRONMENT=(.+)/)?.[1];
  
  console.log('✅ Environment switched successfully!');
  console.log(`🌍 Environment: ${env}`);
  console.log(`🔗 API URL: ${apiUrl}`);
  console.log('');
  console.log('Available commands:');
  console.log('  npm run dev        - Start development server');
  console.log('  npm run build      - Build for production');
  console.log('  npm run start      - Start production server');
  
} catch (error) {
  console.log('❌ Error switching environment:', error.message);
  process.exit(1);
}
