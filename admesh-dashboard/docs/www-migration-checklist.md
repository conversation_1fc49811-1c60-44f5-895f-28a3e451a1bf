# WWW Domain Migration Checklist

This document outlines the steps needed to properly migrate from `useadmesh.com` to `www.useadmesh.com`.

## Completed Steps

- [x] Update canonical URL in layout.tsx to use www subdomain
- [x] Update JSON-LD structured data to use www subdomain
- [x] Update all URLs in metadata.ts to use www subdomain
- [x] Update sitemap.ts to use www subdomain
- [x] Update static sitemap.xml to use www subdomain
- [x] Update robots.ts to use www subdomain
- [x] Update static robots.txt to use www subdomain
- [x] Verify redirect from non-www to www is properly set up in next.config.mjs

## Remaining Steps

### Google Search Console Verification

1. Log in to [Google Search Console](https://search.google.com/search-console)
2. Add a new property for `https://www.useadmesh.com`
3. Verify ownership using one of these methods:
   - HTML file verification: Upload the `google-site-verification.html` file to your server
   - HTML tag verification: Add the verification meta tag to your `<head>` section
   - DNS verification: Add a TXT record to your domain's DNS configuration
   - Google Analytics verification: Link your Google Analytics account

4. Once verified, set up the following:
   - Submit your sitemap: `https://www.useadmesh.com/sitemap.xml`
   - Request indexing of important pages
   - Monitor for any crawl errors or issues

### DNS and Server Configuration

1. Ensure your DNS settings are properly configured:
   - Both `useadmesh.com` and `www.useadmesh.com` should point to the same server
   - CNAME record for `www` should be set up correctly

2. Server configuration:
   - Ensure your server is configured to handle both domains
   - Set up 301 redirects from non-www to www (already done in Next.js config)
   - Test that the redirects work properly

### Testing

1. Test all redirects:
   - Visit `https://useadmesh.com` and verify it redirects to `https://www.useadmesh.com`
   - Test various paths to ensure they redirect correctly

2. Verify canonical URLs:
   - Use browser developer tools to check that the canonical URL is set correctly on all pages
   - Use tools like [Screaming Frog](https://www.screamingfrog.co.uk/seo-spider/) to crawl your site and check canonical URLs

3. Test social sharing:
   - Test how your pages appear when shared on social media platforms
   - Use tools like [Facebook Sharing Debugger](https://developers.facebook.com/tools/debug/) and [Twitter Card Validator](https://cards-dev.twitter.com/validator)

### Monitoring

1. Monitor search performance in Google Search Console
2. Watch for any drop in rankings or traffic
3. Check for crawl errors or indexing issues
4. Monitor for any broken links or 404 errors

## Additional Resources

- [Google's Site Move Guide](https://developers.google.com/search/docs/advanced/crawling/site-move-with-url-changes)
- [Moz's Site Migration Guide](https://moz.com/blog/site-migration-guide)
- [Ahrefs' Website Migration Checklist](https://ahrefs.com/blog/website-migration-checklist/)
