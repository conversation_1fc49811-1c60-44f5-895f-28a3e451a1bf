"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON>, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { BadgeData } from "@/components/BadgeIcon";
import { BADGE_ICONS, BADGE_COLORS } from "@/lib/badge-utils";
import { toast } from "sonner";

interface ShareableBadgeData {
  badge: BadgeData;
  user_name: string;
}

export default function ShareableBadgePage() {
  const params = useParams();
  const badgeId = params.id as string;
  const [badgeData, setBadgeData] = useState<ShareableBadgeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBadge = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/badges/share/${badgeId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch badge");
        }

        const data = await response.json();
        setBadgeData(data);
      } catch (error) {
        console.error("Error fetching badge:", error);
        setError("This badge could not be found or is no longer available.");
      } finally {
        setLoading(false);
      }
    };

    fetchBadge();
  }, [badgeId]);

  const shareOnTwitter = () => {
    if (!badgeData) return;

    const text = `I just earned the ${badgeData.badge.metadata.name} badge on AdMesh! ${badgeData.badge.metadata.description} Check it out:`;
    const url = window.location.href;

    window.open(
      `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
      "_blank"
    );
  };

  const shareOnLinkedIn = () => {
    if (!badgeData) return;

    const url = window.location.href;

    window.open(
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      "_blank"
    );
  };

  const shareOnFacebook = () => {
    if (!badgeData) return;

    const url = window.location.href;

    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      "_blank"
    );
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(window.location.href);
    toast.success("Link copied to clipboard!");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent className="flex flex-col items-center space-y-4">
            <Skeleton className="h-24 w-24 rounded-full" />
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-10 w-full" />
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (error || !badgeData) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Badge Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error || "This badge could not be found or is no longer available."}</p>
          </CardContent>
          <CardFooter>
            <Link href="/" passHref>
              <Button className="w-full">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to AdMesh
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    );
  }

  const { badge, user_name } = badgeData;
  const IconComponent = BADGE_ICONS[badge.badge_type] || Award;
  const bgColor = BADGE_COLORS[badge.badge_type] || "bg-gray-500";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl">AdMesh Achievement</CardTitle>
            <Link href="/" passHref>
              <Button variant="ghost" size="sm">
                <Image
                  src="/logo.svg"
                  alt="AdMesh Logo"
                  width={36}
                  height={36}
                />
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center space-y-6">
          <div className={`${bgColor} h-24 w-24 rounded-full flex items-center justify-center text-white`}>
            <IconComponent className="h-12 w-12" />
          </div>

          <div className="text-center">
            <h2 className="text-2xl font-bold">{badge.metadata.name}</h2>
            <p className="text-muted-foreground mt-1">{badge.metadata.description}</p>
          </div>

          <div className="text-center">
            <p className="text-sm">Earned by <span className="font-semibold">{user_name}</span></p>
            <p className="text-xs text-muted-foreground mt-1">
              {formatDistanceToNow(new Date(badge.awarded_at), { addSuffix: true })}
            </p>
          </div>

          {badge.metadata.xp_bonus > 0 && (
            <div className="flex items-center gap-2 text-sm">
              <Sparkles className="h-4 w-4 text-yellow-500" />
              <span>+{badge.metadata.xp_bonus} XP Bonus</span>
            </div>
          )}

          <div className="w-full pt-4 border-t">
            <p className="text-center text-sm mb-4">Share this achievement</p>
            <div className="flex justify-center space-x-4">
              <Button onClick={shareOnTwitter} variant="outline" size="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter">
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                </svg>
              </Button>
              <Button onClick={shareOnLinkedIn} variant="outline" size="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-linkedin">
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                  <rect width="4" height="12" x="2" y="9" />
                  <circle cx="4" cy="4" r="2" />
                </svg>
              </Button>
              <Button onClick={shareOnFacebook} variant="outline" size="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-facebook">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                </svg>
              </Button>
              <Button onClick={copyToClipboard} variant="outline" size="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-link">
                  <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                  <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                </svg>
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Link href="/" passHref>
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Return to AdMesh
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}
