import { Metadata } from "next";
import { generateMetadata } from "./metadata";

// Home page specific metadata - serves brands content at root
// Root page serves brands content directly, no redirect
export const metadata: Metadata = generateMetadata(
  "AdMesh – Promote your products inside AI", // Title
  "AdMesh places your products directly inside real conversations happening across AI tools, right when users are searching, comparing, and ready to act. Official AdMesh website.", // Description
  "/", // Path - canonical points to root since content is served here
  undefined // Let the generateMetadata function create the static OG image URL
);
