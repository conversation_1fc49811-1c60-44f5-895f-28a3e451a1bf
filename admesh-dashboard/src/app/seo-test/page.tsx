"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function SEOTestPage() {
  const [url, setUrl] = useState("https://www.useadmesh.com");

  const handleTest = () => {
    // Open Google's Rich Results Test
    window.open(`https://search.google.com/test/rich-results?url=${encodeURIComponent(url)}`, "_blank");
  };

  const handleSchemaTest = () => {
    // Open Schema.org Validator
    window.open(`https://validator.schema.org/#url=${encodeURIComponent(url)}`, "_blank");
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">SEO Structured Data Test</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Test Your Structured Data</CardTitle>
          <CardDescription>
            Enter your URL to test the structured data implementation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="url">URL to Test</Label>
              <Input
                id="url"
                placeholder="https://www.useadmesh.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handleSchemaTest}>
            Test with Schema.org Validator
          </Button>
          <Button onClick={handleTest}>
            Test with Google Rich Results
          </Button>
        </CardFooter>
      </Card>

      <Tabs defaultValue="sitelinks">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sitelinks">Sitelinks</TabsTrigger>
          <TabsTrigger value="breadcrumbs">Breadcrumbs</TabsTrigger>
          <TabsTrigger value="organization">Organization</TabsTrigger>
        </TabsList>

        <TabsContent value="sitelinks" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Sitelinks Implementation</CardTitle>
              <CardDescription>
                How to implement sitelinks for Google search results
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                Sitelinks are additional links that appear under your main search result in Google.
                They help users navigate directly to specific sections of your website.
              </p>

              <h3 className="text-lg font-semibold mt-4">Implementation Steps:</h3>
              <ol className="list-decimal pl-5 space-y-2">
                <li>Implement proper WebSite structured data</li>
                <li>Add SiteNavigationElement structured data</li>
                <li>Ensure your site has a clear navigation structure</li>
                <li>Use descriptive anchor text for internal links</li>
                <li>Create a comprehensive sitemap.xml file</li>
              </ol>

              <div className="bg-muted p-4 rounded-md mt-4">
                <p className="text-sm">
                  Note: Google algorithmically determines which sitelinks to show.
                  Proper structured data increases the likelihood of sitelinks appearing,
                  but doesn&apos;t guarantee it.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breadcrumbs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Breadcrumbs Implementation</CardTitle>
              <CardDescription>
                How to implement breadcrumbs for Google search results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>
                Breadcrumbs in search results show the path to the page, helping users understand
                the site structure and navigate more easily.
              </p>

              <h3 className="text-lg font-semibold mt-4">Implementation Steps:</h3>
              <ol className="list-decimal pl-5 space-y-2">
                <li>Add BreadcrumbList structured data</li>
                <li>Ensure each ListItem has position, name, and item properties</li>
                <li>Make sure breadcrumbs reflect your actual site hierarchy</li>
              </ol>

              <div className="bg-muted p-4 rounded-md mt-4">
                <p className="text-sm">
                  Breadcrumbs help both users and search engines understand your site structure.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organization" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Schema Implementation</CardTitle>
              <CardDescription>
                How to implement Organization schema for brand recognition
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>
                Organization schema helps search engines understand your brand identity and can
                enable rich results like Knowledge Panels.
              </p>

              <h3 className="text-lg font-semibold mt-4">Implementation Steps:</h3>
              <ol className="list-decimal pl-5 space-y-2">
                <li>Add Organization structured data</li>
                <li>Include your logo, social profiles, and contact information</li>
                <li>Ensure consistent NAP (Name, Address, Phone) information across the web</li>
              </ol>

              <div className="bg-muted p-4 rounded-md mt-4">
                <p className="text-sm">
                  Organization schema helps establish your brand identity in search results.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
