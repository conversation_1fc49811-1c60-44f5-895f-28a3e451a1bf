// app/dashboard/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

export default function DashboardRedirect() {
  const router = useRouter();
  const { role, user, onboardingStatus } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set a timeout to show loading state for at least 1 second
    // to avoid flickering for fast loads
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // If we have a user but no role after loading completes,
    // there might be an issue with role assignment
    if (!isLoading && user && !role) {
      setError("Unable to determine your role. Please try logging out and back in.");
      return;
    }

    if (!role) return;

    // If user is admin, redirect to products page
    if (role === "admin") {
      router.replace("/dashboard/admin/products");
      return;
    }

    const defaultRoutes: Record<string, string> = {
      user: "/dashboard/user/chat",
      agent: "/dashboard/agent/queries",
      brand: "/dashboard/brand/offers",
    };

    // For brands, check onboarding status
    if (role === "brand") {
      // First check the onboardingStatus from Firebase claims
      if (onboardingStatus && onboardingStatus !== "completed") {
        // If we know from claims that onboarding is not completed, redirect immediately
        router.replace("/dashboard/brand/onboarding");
        return;
      } else if (onboardingStatus === "completed") {
        // If we know from claims that onboarding is completed, redirect to dashboard
        router.replace(defaultRoutes[role]);
        return;
      }

      // If onboardingStatus is null (not in claims or not set), check with the API
      if (user) {
        const checkOnboardingStatus = async () => {
          try {
            const token = await user.getIdToken();
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/onboarding/status`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const data = await response.json();

              // If onboarding is not completed, redirect to onboarding page
              if (data.onboarding_status !== "completed") {
                router.replace("/dashboard/brand/onboarding");
                return;
              }
            }

            // If onboarding is completed or API call fails, proceed with normal routing
            router.replace(defaultRoutes[role]);
          } catch (error) {
            console.error("Error checking onboarding status:", error);
            // Fallback to normal routing if API call fails
            router.replace(defaultRoutes[role]);
          }
        };

        checkOnboardingStatus();
      }
    } else {
      // For non-brand roles, use normal routing
      if (defaultRoutes[role]) {
        router.replace(defaultRoutes[role]);
      } else {
        setError(`Unknown role: ${role}. Please contact support.`);
      }
    }
  }, [role, router, isLoading, user, onboardingStatus]);

  // Show loading spinner while determining role
  if (isLoading || (user && !role)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading your dashboard...</p>
      </div>
    );
  }

  // Show error message if there's an issue
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4">
        <div className="bg-destructive/10 text-destructive p-4 rounded-md max-w-md text-center">
          <p className="font-medium mb-2">Error</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // Return null when redirecting
  return null;
}
