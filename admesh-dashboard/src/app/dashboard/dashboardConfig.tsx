import { Spark<PERSON>, Star, PackageSearch, Coins, BarChart2 } from "lucide-react";

export const dashboardCardsByRole: Record<
  "user" | "agent" | "brand",
  {
    title: string;
    icon: React.ElementType;
    value: string | number;
    description: string;
  }[]
> = {
  user: [
    {
      title: "Queries",
      icon: Sparkles,
      value: 12,
      description: "This week",
    },
    {
      title: "Saved",
      icon: Star,
      value: 7,
      description: "Favorites",
    },
    {
      title: "Matches",
      icon: PackageSearch,
      value: 24,
      description: "Recommended",
    },
  ],
  agent: [
    {
      title: "Handled",
      icon: Sparkles,
      value: 85,
      description: "Total queries",
    },
    {
      title: "Earnings",
      icon: Coins,
      value: "$210",
      description: "This month",
    },
    {
      title: "Conversions",
      icon: PackageSearch,
      value: 19,
      description: "Tracked",
    },
  ],
  brand: [
    {
      title: "Offers",
      icon: Star,
      value: 14,
      description: "Active now",
    },
    {
      title: "Conversions",
      icon: PackageSearch,
      value: 43,
      description: "Total",
    },
    {
      title: "Analytics",
      icon: BarChart2,
      value: "View",
      description: "Reports",
    },
  ],
};
