"use client";

import { ReactNode, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { role, isAdmin, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If user is not loading and either not an admin or has a different role, redirect
    if (!isLoading && (!isAdmin || (role && role !== "admin"))) {
      router.push("/dashboard");
    }
  }, [isLoading, isAdmin, role, router]);

  // Show loading while checking admin status
  if (isLoading || !isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Render admin content if user is an admin
  return <>{children}</>;
}
