"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Search,
  ArrowUpDown,
  Mail,
  User,
  Award,
  DollarSign,
  MousePointerClick,
  BarChart2,
  UserCog
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import ChangeRoleDialog from "@/components/admin/ChangeRoleDialog";

interface User {
  uid: string;
  name?: string;
  agentName?: string;
  email?: string;
  role?: string;
  credits?: number;
  xp?: number;
  lifetime_xp?: number;
  clicks_made?: number;
  conversions?: number;
  created_at?: {
    seconds: number;
    nanoseconds: number;
  } | string;
  updated_at?: {
    seconds: number;
    nanoseconds: number;
  } | string;
  onboardingStatus?: string;
  isPioneerEligible?: boolean;
  total_earnings?: number;
  pending_total?: number;
}

type SortField = "name" | "email" | "xp" | "credits" | "clicks_made" | "conversions" | "created_at" | "none";
type SortDirection = "asc" | "desc";

export default function AdminUsersPage() {
  const { user, role, isAdmin } = useAuth();
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [sortField, setSortField] = useState<SortField>("created_at");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const productsPerPage = 50;

  // Role change dialog state
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Redirect if not admin
  useEffect(() => {
    if (role && role !== "admin") {
      router.push("/dashboard");
    }
  }, [role, router]);

  // Fetch users on component mount and when dependencies change
  useEffect(() => {
    fetchUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isAdmin, currentPage, sortField, sortDirection, productsPerPage]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Filter users by search query
  const filteredUsers = users.filter((user) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      (user.name?.toLowerCase().includes(searchLower) || false) ||
      (user.agentName?.toLowerCase().includes(searchLower) || false) ||
      (user.email?.toLowerCase().includes(searchLower) || false)
    );
  });

  // Handle opening the role change dialog
  const handleOpenRoleDialog = (user: User) => {
    setSelectedUser(user);
    setIsRoleDialogOpen(true);
  };

  // Fetch users function
  const fetchUsers = async () => {
    if (!user || !isAdmin) return;

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const offset = (currentPage - 1) * productsPerPage;

      // Build query parameters
      const params = new URLSearchParams({
        limit: productsPerPage.toString(),
        offset: offset.toString(),
        sort_by: sortField !== 'none' ? sortField : 'created_at',
        sort_direction: sortDirection,
      });

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/admin/all?${params.toString()}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }

      const data = await response.json();
      setUsers(data.users || []);
      setTotalUsers(data.pagination?.total || 0);
      setTotalPages(Math.ceil((data.pagination?.total || 0) / productsPerPage));
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to load users. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data after role change
  const handleRoleChanged = () => {
    fetchUsers();
  };

  // Format timestamp
  const formatTimestamp = (
    timestamp?: { seconds: number; nanoseconds: number } | string
  ) => {
    if (!timestamp) return "N/A";

    // Handle Firestore timestamp
    if (typeof timestamp === 'object' && 'seconds' in timestamp) {
      const date = new Date(timestamp.seconds * 1000);
      return formatDistanceToNow(date, { addSuffix: true });
    }

    // Handle string timestamp
    if (typeof timestamp === 'string') {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    }

    return "N/A";
  };

  // If not admin, show loading or redirect
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Admin: Users</h2>
          <p className="text-muted-foreground mt-1">
            Manage all users in the AdMesh platform.
          </p>
        </div>
        <div className="w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="w-full sm:w-[300px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Sorting Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Sort by:</span>
            <Select
              value={sortField}
              onValueChange={(value) => setSortField(value as SortField)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at">Date Joined</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="xp">XP</SelectItem>
                <SelectItem value="credits">Credits</SelectItem>
                <SelectItem value="clicks_made">Clicks</SelectItem>
                <SelectItem value="conversions">Conversions</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortDirection(sortDirection === "asc" ? "desc" : "asc")}
            className="gap-1"
          >
            <ArrowUpDown className="h-4 w-4" />
            {sortDirection === "asc" ? "Ascending" : "Descending"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl">All Users</CardTitle>
          <CardDescription>
            Total: {totalUsers} users | Showing {filteredUsers.length} users | {productsPerPage} per page
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">{error}</div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No users found.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("name")}
                    >
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        User
                        {sortField === "name" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("email")}
                    >
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        Email
                        {sortField === "email" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("xp")}
                    >
                      <div className="flex items-center gap-1">
                        <Award className="h-4 w-4" />
                        XP
                        {sortField === "xp" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("credits")}
                    >
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        Credits
                        {sortField === "credits" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("clicks_made")}
                    >
                      <div className="flex items-center gap-1">
                        <MousePointerClick className="h-4 w-4" />
                        Clicks
                        {sortField === "clicks_made" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("conversions")}
                    >
                      <div className="flex items-center gap-1">
                        <BarChart2 className="h-4 w-4" />
                        Conversions
                        {sortField === "conversions" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("created_at")}
                    >
                      <div className="flex items-center gap-1">
                        Joined
                        {sortField === "created_at" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.uid}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span className="truncate max-w-[150px]">
                            {user.name || user.agentName || "Unnamed User"}
                          </span>
                          <span className="text-xs text-muted-foreground truncate max-w-[150px]">
                            {user.uid}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="truncate max-w-[150px] block">
                          {user.email || "No email"}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={user.role === "admin" ? "destructive" : "default"}
                        >
                          {user.role || "user"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{user.xp || 0}</span>
                          {user.lifetime_xp && user.lifetime_xp > 0 && (
                            <span className="text-xs text-muted-foreground">
                              Lifetime: {user.lifetime_xp}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {user.credits || 0}
                      </TableCell>
                      <TableCell>
                        {user.clicks_made || 0}
                      </TableCell>
                      <TableCell>
                        {user.conversions || 0}
                      </TableCell>
                      <TableCell>
                        {formatTimestamp(user.created_at)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleOpenRoleDialog(user)}
                            title="Change user role"
                          >
                            <UserCog className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination className="mt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  />
                </PaginationItem>

                {/* Show limited page numbers with ellipsis for large page counts */}
                {(() => {
                  // Always show first page
                  const pagesToShow = [1];

                  // Show pages around current page
                  const rangeStart = Math.max(2, currentPage - 1);
                  const rangeEnd = Math.min(totalPages - 1, currentPage + 1);

                  // Add ellipsis indicator if needed before current range
                  if (rangeStart > 2) {
                    pagesToShow.push(-1); // -1 represents ellipsis
                  }

                  // Add pages around current page
                  for (let i = rangeStart; i <= rangeEnd; i++) {
                    pagesToShow.push(i);
                  }

                  // Add ellipsis indicator if needed after current range
                  if (rangeEnd < totalPages - 1) {
                    pagesToShow.push(-2); // -2 represents ellipsis (different key)
                  }

                  // Always show last page if more than 1 page
                  if (totalPages > 1) {
                    pagesToShow.push(totalPages);
                  }

                  return pagesToShow.map((page) => (
                    <PaginationItem key={page}>
                      {page < 0 ? (
                        <span className="px-4">...</span>
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ));
                })()}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </CardContent>
      </Card>

      {/* Role Change Dialog */}
      {selectedUser && (
        <ChangeRoleDialog
          open={isRoleDialogOpen}
          onOpenChange={setIsRoleDialogOpen}
          user={selectedUser}
          onRoleChanged={handleRoleChanged}
        />
      )}
    </div>
  );
}
