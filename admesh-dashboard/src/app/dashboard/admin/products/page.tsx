"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Search, Trash2, ExternalLink, ArrowUpDown, Eye, MousePointerClick, BarChart2 } from "lucide-react";
import { toast } from "sonner";

interface Product {
  id: string;
  title: string;
  url: string;
  brand_id: string;
  category?: string;
  status?: string;
  has_free_tier?: boolean;
  is_open_source?: boolean;
  is_ai_powered?: boolean;
  created_at?: {
    seconds: number;
    nanoseconds: number;
  } | string;
  active_offers?: string[];
  view_count?: number;
  clicks?: number;
  conversions?: number;
}

type SortField = "title" | "view_count" | "clicks" | "conversions" | "none";
type SortDirection = "asc" | "desc";
type FilterField = "view_count" | "clicks" | "conversions" | "none";

export default function AdminProductsPage() {
  const { user, role, isAdmin } = useAuth();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [sortField, setSortField] = useState<SortField>("none");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [filterField, setFilterField] = useState<FilterField>("none");
  const [filterValue, setFilterValue] = useState<number | null>(null);
  const productsPerPage = 50;

  // Redirect if not admin
  useEffect(() => {
    if (role && role !== "admin") {
      router.push("/dashboard");
    }
  }, [role, router]);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      if (!user || !isAdmin) return;

      try {
        setLoading(true);
        setError(null);

        const token = await user.getIdToken();
        const offset = (currentPage - 1) * productsPerPage;

        // Build query parameters
        const params = new URLSearchParams({
          limit: productsPerPage.toString(),
          offset: offset.toString(),
        });

        // Add sorting parameters if set
        if (sortField && sortField !== 'none') {
          params.append('sort_by', sortField);
          params.append('sort_direction', sortDirection);
        }

        // Add filtering parameters if set
        if (filterField !== 'none' && filterValue !== null) {
          if (filterField === 'view_count') {
            params.append('min_views', filterValue.toString());
          } else if (filterField === 'clicks') {
            params.append('min_clicks', filterValue.toString());
          } else if (filterField === 'conversions') {
            params.append('min_conversions', filterValue.toString());
          }
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/products/admin/all?${params.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await response.json();
        setProducts(data.products || []);
        setTotalProducts(data.pagination?.total || 0);
        setTotalPages(Math.ceil((data.pagination?.total || 0) / productsPerPage));
      } catch (err) {
        console.error("Error fetching products:", err);
        setError("Failed to load products. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user, isAdmin, currentPage, sortField, sortDirection, filterField, filterValue, productsPerPage]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Filter and sort products
  const filteredAndSortedProducts = products
    // First filter by search query
    .filter((product) =>
      product.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
    // Then filter by metric if filter is applied
    .filter((product) => {
      if (filterField === 'none' || filterValue === null) return true;

      const value = product[filterField as keyof Product] as number | undefined;
      return value !== undefined && value >= filterValue;
    })
    // Then sort if sort field is selected
    .sort((a, b) => {
      if (sortField === 'none') return 0;

      const aValue = a[sortField as keyof Product];
      const bValue = b[sortField as keyof Product];

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return sortDirection === "asc" ? -1 : 1;
      if (bValue === undefined) return sortDirection === "asc" ? 1 : -1;

      // Sort strings
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Sort numbers
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

  // Handle product deletion
  const handleDeleteProduct = async (productId: string) => {
    if (!user || !isAdmin) return;

    try {
      setIsDeleting(true);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/products/admin/${productId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to delete product");
      }

      // Remove the deleted product from the list
      setProducts((prevProducts) =>
        prevProducts.filter((product) => product.id !== productId)
      );

      toast.success("Product deleted successfully");
    } catch (err) {
      console.error("Error deleting product:", err);
      if (err instanceof Error) {
        toast.error(err.message);
      } else {
        toast.error("Failed to delete product");
      }
    } finally {
      setIsDeleting(false);
    }
  };

  // If not admin, show loading or redirect
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Admin: Products</h2>
          <p className="text-muted-foreground mt-1">
            Manage all products in the AdMesh platform.
          </p>
        </div>
        <div className="w-full sm:w-auto">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search products..."
              className="w-full sm:w-[300px] pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Filtering and Sorting Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Sort by:</span>
            <Select
              value={sortField}
              onValueChange={(value) => setSortField(value as SortField)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="view_count">Views</SelectItem>
                <SelectItem value="clicks">Clicks</SelectItem>
                <SelectItem value="conversions">Conversions</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {sortField !== 'none' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortDirection(sortDirection === "asc" ? "desc" : "asc")}
              className="gap-1"
            >
              <ArrowUpDown className="h-4 w-4" />
              {sortDirection === "asc" ? "Ascending" : "Descending"}
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Filter by:</span>
          <Select
            value={filterField}
            onValueChange={(value) => {
              setFilterField(value as FilterField);
              if (value === 'none') setFilterValue(null);
            }}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select metric" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="view_count">Views</SelectItem>
              <SelectItem value="clicks">Clicks</SelectItem>
              <SelectItem value="conversions">Conversions</SelectItem>
            </SelectContent>
          </Select>

          {filterField !== 'none' && (
            <div className="flex items-center gap-2">
              <span className="text-sm">≥</span>
              <Input
                type="number"
                min="0"
                className="w-24"
                value={filterValue !== null ? filterValue : ""}
                onChange={(e) => setFilterValue(e.target.value ? parseInt(e.target.value) : null)}
                placeholder="Value"
              />
            </div>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl">All Products</CardTitle>
          <CardDescription>
            Total: {totalProducts} products | Showing {filteredAndSortedProducts.length} products | {productsPerPage} per page
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">{error}</div>
          ) : filteredAndSortedProducts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No products found.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("title")}
                    >
                      <div className="flex items-center gap-1">
                        Title
                        {sortField === "title" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Features</TableHead>
                    <TableHead>Offers</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("view_count")}
                    >
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        Views
                        {sortField === "view_count" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("clicks")}
                    >
                      <div className="flex items-center gap-1">
                        <MousePointerClick className="h-4 w-4" />
                        Clicks
                        {sortField === "clicks" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSort("conversions")}
                    >
                      <div className="flex items-center gap-1">
                        <BarChart2 className="h-4 w-4" />
                        Conversions
                        {sortField === "conversions" && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAndSortedProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span className="truncate max-w-[200px]">{product.title}</span>
                          <a
                            href={product.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-muted-foreground hover:text-primary flex items-center gap-1 mt-1"
                          >
                            <span className="truncate max-w-[180px]">{product.url}</span>
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </div>
                      </TableCell>
                      <TableCell>
                        {product.category || <span className="text-muted-foreground">-</span>}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={product.status === "active" ? "default" : "secondary"}
                        >
                          {product.status || "inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {product.has_free_tier && (
                            <Badge variant="outline">Free Tier</Badge>
                          )}
                          {product.is_open_source && (
                            <Badge variant="outline">Open Source</Badge>
                          )}
                          {product.is_ai_powered && (
                            <Badge variant="outline">AI Powered</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge>
                          {product.active_offers?.length || 0} active
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        {product.view_count || 0}
                      </TableCell>
                      <TableCell className="text-center">
                        {product.clicks || 0}
                      </TableCell>
                      <TableCell className="text-center">
                        {product.conversions || 0}
                      </TableCell>
                      <TableCell className="text-right">
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              disabled={isDeleting}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Product</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this product? This action cannot be undone.
                                {product.active_offers && product.active_offers.length > 0 && (
                                  <div className="mt-2 text-red-500 font-semibold">
                                    Warning: This product has {product.active_offers.length} active offers.
                                    Deleting it will affect these offers.
                                  </div>
                                )}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteProduct(product.id)}
                                disabled={isDeleting}
                                className="bg-red-500 hover:bg-red-600"
                              >
                                {isDeleting ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Deleting...
                                  </>
                                ) : (
                                  "Delete"
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination className="mt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  />
                </PaginationItem>

                {/* Show limited page numbers with ellipsis for large page counts */}
                {(() => {
                  // Always show first page
                  const pagesToShow = [1];

                  // Show pages around current page
                  const rangeStart = Math.max(2, currentPage - 1);
                  const rangeEnd = Math.min(totalPages - 1, currentPage + 1);

                  // Add ellipsis indicator if needed before current range
                  if (rangeStart > 2) {
                    pagesToShow.push(-1); // -1 represents ellipsis
                  }

                  // Add pages around current page
                  for (let i = rangeStart; i <= rangeEnd; i++) {
                    pagesToShow.push(i);
                  }

                  // Add ellipsis indicator if needed after current range
                  if (rangeEnd < totalPages - 1) {
                    pagesToShow.push(-2); // -2 represents ellipsis (different key)
                  }

                  // Always show last page if more than 1 page
                  if (totalPages > 1) {
                    pagesToShow.push(totalPages);
                  }

                  return pagesToShow.map((page) => (
                    <PaginationItem key={page}>
                      {page < 0 ? (
                        <span className="px-4">...</span>
                      ) : (
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                        >
                          {page}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ));
                })()}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
