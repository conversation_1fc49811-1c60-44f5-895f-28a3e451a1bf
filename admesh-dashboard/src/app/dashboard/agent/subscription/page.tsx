"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Loader2, Calendar, Package, Tag, Clock } from "lucide-react";
import { toast } from "sonner";
import AgentSubscriptionPlans from "@/components/AgentSubscriptionPlans";

interface Subscription {
  plan_id: string;
  status: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  current_period_start?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  created_at?: string;
  updated_at?: string;
}

interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  revenue_share_percentage: number;
  attribution_window: string;
  offer_visibility_boost: string;
  monetized_query_usage: string;
  conversion_reports: string;
  support_level: string;
  custom_integrations: boolean;
  multi_agent_teams: boolean;
  features: string[];
}

export default function AgentSubscriptionPage() {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [canceling, setCanceling] = useState(false);
  const [reactivating, setReactivating] = useState(false);
  const [activeTab, setActiveTab] = useState("current");

  // Format price from cents to dollars
  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Fetch subscription data
  const fetchSubscription = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = await user.getIdToken();

      // Fetch current subscription from the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/agent/subscription`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }

      const data = await response.json();
      setSubscription(data.subscription);
      setPlan(data.plan);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching subscription:", error);
      toast.error("Failed to load subscription data");
      setLoading(false);
    }
  };

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!user || !subscription?.stripe_subscription_id) return;

    try {
      setCanceling(true);
      // Token will be used in the actual implementation
      // const token = await user.getIdToken();

      // Mock successful cancellation
      setTimeout(() => {
        setSubscription({
          ...subscription,
          cancel_at_period_end: true
        });
        setCancelDialogOpen(false);
        setCanceling(false);
        toast.success("Subscription will be canceled at the end of the billing period");
      }, 1000);
    } catch (error) {
      console.error("Error canceling subscription:", error);
      toast.error("Failed to cancel subscription");
      setCanceling(false);
    }
  };

  // Handle subscription reactivation
  const handleReactivateSubscription = async () => {
    if (!user || !subscription?.stripe_subscription_id) return;

    try {
      setReactivating(true);
      // Token will be used in the actual implementation
      // const token = await user.getIdToken();

      // Mock successful reactivation
      setTimeout(() => {
        setSubscription({
          ...subscription,
          cancel_at_period_end: false
        });
        setReactivating(false);
        toast.success("Your subscription has been reactivated");
      }, 1000);
    } catch (error) {
      console.error("Error reactivating subscription:", error);
      toast.error("Failed to reactivate subscription");
      setReactivating(false);
    }
  };

  // Fetch subscription on component mount
  useEffect(() => {
    fetchSubscription();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Agent Subscription</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={fetchSubscription}
          disabled={loading}
          className="rounded-full"
          title="Refresh"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={loading ? "animate-spin" : ""}
          >
            <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
            <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
            <path d="M16 21h5v-5" />
          </svg>
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="current">Current Plan</TabsTrigger>
            <TabsTrigger value="plans">Available Plans</TabsTrigger>
          </TabsList>

          <TabsContent value="current" className="space-y-6 mt-6">
            {/* Current Plan Details */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{plan?.name} Plan</CardTitle>
                    <CardDescription>
                      {plan?.price_monthly_cents ? `${formatPrice(plan.price_monthly_cents)}/month` : "Free"}
                    </CardDescription>
                  </div>
                  <Badge
                    variant={subscription?.status === "active" ? "default" : "destructive"}
                    className="capitalize"
                  >
                    {subscription?.status || "Unknown"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Plan Features */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Package className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Revenue Share:</span>
                      <span className="font-medium">{plan?.revenue_share_percentage}%</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Attribution Window:</span>
                      <span className="font-medium">{plan?.attribution_window}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Offer Visibility:</span>
                      <span className="font-medium">{plan?.offer_visibility_boost}</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    {subscription?.current_period_end && (
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Current Period Ends:</span>
                        <span className="font-medium">
                          {formatDate(subscription.current_period_end)}
                          {subscription.cancel_at_period_end && (
                            <span className="text-red-500 ml-1">(Will not renew)</span>
                          )}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-muted-foreground">Conversion Reports:</span>
                      <span className="font-medium">{plan?.conversion_reports}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-muted-foreground">Support Level:</span>
                      <span className="font-medium">{plan?.support_level}</span>
                    </div>
                  </div>
                </div>

                {/* Subscription Actions */}
                <div className="flex flex-col sm:flex-row gap-2 pt-4">
                  {subscription?.stripe_subscription_id && (
                    <>
                      {subscription.cancel_at_period_end ? (
                        <Button
                          variant="outline"
                          onClick={handleReactivateSubscription}
                          disabled={reactivating}
                        >
                          {reactivating ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Reactivating...
                            </>
                          ) : (
                            "Reactivate Subscription"
                          )}
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          onClick={() => setCancelDialogOpen(true)}
                        >
                          Cancel Subscription
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="plans" className="mt-6">
            <AgentSubscriptionPlans />
          </TabsContent>
        </Tabs>
      )}

      {/* Cancel Subscription Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? You will still have access to your current plan until the end of your billing period.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCancelDialogOpen(false)}>
              Keep Subscription
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelSubscription}
              disabled={canceling}
            >
              {canceling ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Canceling...
                </>
              ) : (
                "Cancel Subscription"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
