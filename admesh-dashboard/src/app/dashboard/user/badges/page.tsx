"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useCreditsContext } from "@/contexts/credits-context";
import { useBadges } from "@/contexts/badge-context";
import BadgeIcon, { BadgeData } from "@/components/BadgeIcon";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { RefreshCw, Sparkles, Award, Share2, Search, X, Coins, ArrowRight } from "lucide-react";
import ShareBadgeModal from "@/components/ShareBadgeModal";
import BadgeUnlockedModal from "@/components/BadgeUnlockedModal";
import XpLogs from "@/components/XpLogs";
import XpConvertDialog from "@/components/XpConvertDialog";
import { toast } from "sonner";
import { formatDistanceToNow } from "date-fns";
import { BADGE_ICONS } from "@/lib/badge-utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

export default function BadgesPage() {
  const { user, xp, lifetimeXp, refreshUser } = useAuth();
  const { credits, refreshCredits } = useCreditsContext();
  const { checkForNewBadges: checkBadges, markBadgesAsRead } = useBadges();
  const [badges, setBadges] = useState<BadgeData[]>([]);
  const [allBadges, setAllBadges] = useState<BadgeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingAllBadges, setLoadingAllBadges] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState<BadgeData | null>(null);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [newBadge] = useState<BadgeData | null>(null);
  const [isUnlockedModalOpen, setIsUnlockedModalOpen] = useState(false);
  const [isConvertModalOpen, setIsConvertModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredEarnedBadges, setFilteredEarnedBadges] = useState<BadgeData[]>([]);
  const [filteredAvailableBadges, setFilteredAvailableBadges] = useState<BadgeData[]>([]);

  const fetchBadges = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = await user.getIdToken();
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';

      // Use the API endpoint path
      const response = await fetch(`${baseUrl}/badges/user/all`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to fetch badges");
      }

      const data = await response.json();
      // Update any Agent Pioneer badges to show 1000 XP requirement
      const updatedBadges = (data.badges || []).map((badge: BadgeData) => {
        if (badge.badge_type === 'agent_pioneer') {
          return {
            ...badge,
            metadata: {
              ...badge.metadata,
              description: "Reached 1000 XP in the Agent Pioneer program",
            },
            progress_target: badge.progress_target === 100 ? 1000 : badge.progress_target
          };
        }
        return badge;
      });
      setBadges(updatedBadges);
    } catch (error) {
      console.error("Error fetching badges:", error);
      toast.error("Failed to load badges");
    } finally {
      setLoading(false);
    }
  }, [user]);

  const fetchAllBadges = useCallback(async () => {
    if (!user) return;

    try {
      setLoadingAllBadges(true);
      const token = await user.getIdToken();
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';

      // Use the API endpoint path
      const url = `${baseUrl}/badges/user/available`;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch all badges: ${response.status}`);
      }

      const data = await response.json();
      console.log("Available badges response:", data);
      console.log("Number of badges returned:", data.badges ? data.badges.length : 0);

      // Update any Agent Pioneer badges to show 1000 XP requirement
      const updatedBadges = (data.badges || []).map((badge: BadgeData) => {
        if (badge.badge_type === 'agent_pioneer') {
          return {
            ...badge,
            metadata: {
              ...badge.metadata,
              description: "Reached 1000 XP in the Agent Pioneer program",
            },
            progress_target: badge.progress_target === 100 ? 1000 : badge.progress_target
          };
        }
        return badge;
      });

      // Filter out badges that don't have icons in badge-utils.ts
      const filteredBadges = updatedBadges.filter((badge: BadgeData) => {
        return BADGE_ICONS[badge.badge_type] !== undefined;
      });

      console.log("Number of badges after filtering:", filteredBadges.length);
      setAllBadges(filteredBadges);
    } catch (error) {
      console.error("Error fetching all badges:", error);
      toast.error("Failed to load available badges");
    } finally {
      setLoadingAllBadges(false);
    }
  }, [user]);



  const checkForNewBadges = async () => {
    if (!user) return;

    try {
      setRefreshing(true);

      // Mark badges as read when checking from the badges page
      markBadgesAsRead();

      // Use the context function to check for new badges
      await checkBadges();

      // Refresh badges list
      await fetchBadges();

    } catch (error) {
      console.error("Error checking for badges:", error);
      toast.error("Failed to check for new badges");
    } finally {
      setRefreshing(false);
    }
  };

  // Register the share modal callback
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as Window & { __showBadgeShareModal?: (badge: BadgeData) => void }).__showBadgeShareModal = (badge: BadgeData) => {
        setSelectedBadge(badge);
        setIsShareModalOpen(true);
      };
    }

    // Mark badges as read when visiting the badges page
    markBadgesAsRead();

    return () => {
      if (typeof window !== 'undefined') {
        delete (window as Window & { __showBadgeShareModal?: (badge: BadgeData) => void }).__showBadgeShareModal;
      }
    };
  }, [markBadgesAsRead]);

  const toggleBadgeDisplay = async (badgeId: string, currentState: boolean) => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';

      const response = await fetch(`${baseUrl}/badges/user/${badgeId}/toggle-display`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to update badge display setting");
      }

      // Update local state
      setBadges(prev => prev.map(badge =>
        badge.badge_id === badgeId
          ? { ...badge, is_displayed: !currentState }
          : badge
      ));

      toast.success(`Badge display ${currentState ? 'hidden' : 'shown'} successfully`);
    } catch (error) {
      console.error("Error toggling badge display:", error);
      toast.error("Failed to update badge display setting");
    }
  };

  // We no longer need to fetch XP logs to calculate total XP earned
  // since we're using lifetimeXp directly from the auth context

  useEffect(() => {
    if (user) {
      fetchBadges();
      fetchAllBadges();
    }
  }, [user, fetchBadges, fetchAllBadges]);

  // Filter badges based on search query
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredEarnedBadges(badges);
      setFilteredAvailableBadges(allBadges.filter(badge => badge.status !== "earned"));
    } else {
      const query = searchQuery.toLowerCase();

      // Filter earned badges
      const earnedResults = badges.filter(badge =>
        badge.metadata.name.toLowerCase().includes(query) ||
        badge.metadata.description.toLowerCase().includes(query)
      );
      setFilteredEarnedBadges(earnedResults);

      // Filter available badges
      const availableResults = allBadges
        .filter(badge => badge.status !== "earned")
        .filter(badge =>
          badge.metadata.name.toLowerCase().includes(query) ||
          badge.metadata.description.toLowerCase().includes(query)
        );
      setFilteredAvailableBadges(availableResults);
    }
  }, [searchQuery, badges, allBadges]);

  const clearSearch = () => {
    setSearchQuery("");
  };

  // Calculate total XP bonus from badges
  const totalXpBonus = badges.reduce((total, badge) => total + (badge.metadata.xp_bonus || 0), 0);

  // Calculate total earnings boost percentage
  const totalEarningsBoost = badges.reduce((total, badge) =>
    total + (badge.metadata.earnings_boost_percent || 0), 0);

  if (loading) {
    return (
      <div className="p-4 md:p-6 space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 space-y-6 max-w-7xl mx-auto">
      {selectedBadge && (
        <ShareBadgeModal
          badge={selectedBadge}
          isOpen={isShareModalOpen}
          onClose={() => setIsShareModalOpen(false)}
        />
      )}

      {newBadge && (
        <BadgeUnlockedModal
          badge={newBadge}
          isOpen={isUnlockedModalOpen}
          onClose={() => setIsUnlockedModalOpen(false)}
        />
      )}

      <XpConvertDialog
        isOpen={isConvertModalOpen}
        onClose={() => setIsConvertModalOpen(false)}
        onSuccess={() => {
          refreshUser();
          refreshCredits();
          fetchBadges();
        }}
      />

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">My Badges</h1>
          <p className="text-muted-foreground">Collect badges by using AdMesh and completing milestones</p>
        </div>
        <Button
          onClick={checkForNewBadges}
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Check for New Badges
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Badges
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{badges.length}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Badges earned
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <Sparkles className="h-4 w-4 text-yellow-500" />
              XP Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Total XP Earned */}
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="text-xs text-muted-foreground mb-1">Total XP Earned</div>
                <div className="text-2xl font-bold text-yellow-500">{lifetimeXp} XP</div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground mb-1">Available XP</div>
                <div className="text-2xl font-bold">{xp} XP</div>
              </div>
            </div>

            {/* XP Bonus from badges */}
            {totalXpBonus > 0 && (
              <div className="mt-2 flex items-center justify-between text-xs">
                <span className="text-muted-foreground">XP Bonus from badges:</span>
                <span className="font-medium text-yellow-500">+{totalXpBonus} XP</span>
              </div>
            )}

            {/* Conversion Section */}
            <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-800">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-1 text-sm">
                  <Sparkles className="h-4 w-4 text-yellow-500" />
                  <span>10 XP</span>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center gap-1 text-sm">
                  <Coins className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">1 Credit</span>
                </div>
              </div>
              <Button
                variant="default"
                size="sm"
                className="w-full flex items-center gap-2"
                onClick={() => setIsConvertModalOpen(true)}
                disabled={xp < 10}
              >
                <Coins className="h-3.5 w-3.5 text-green-500" />
                Convert XP to <span className="text-green-500">Credits</span>
              </Button>
              <div className="mt-2 text-xs text-center text-muted-foreground">
                Current balance: <span className="text-green-500 font-medium">{credits !== null ? credits : '--'} Credits</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <Award className="h-4 w-4 text-green-500" />
              Earnings Boost
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">+{totalEarningsBoost}%</div>
            <p className="text-xs text-muted-foreground mt-1">
              Earnings boost from badges
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Badge List */}
      <Tabs defaultValue="earned" className="w-full">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <TabsList>
            <TabsTrigger value="earned">
              Earned Badges ({badges.length})
            </TabsTrigger>
            <TabsTrigger value="available">
              Available Badges ({loadingAllBadges ? "..." : allBadges.filter(badge => badge.status !== "earned").length})
            </TabsTrigger>
            <TabsTrigger value="xp">
              XP Activity
            </TabsTrigger>
          </TabsList>

          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search badges & XP activity..."
              className="pl-9 pr-9"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button
                onClick={clearSearch}
                className="absolute right-2.5 top-2.5 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </button>
            )}
          </div>
        </div>

        <TabsContent value="earned">
          {badges.length === 0 ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <p>You haven&apos;t earned any badges yet. Keep using AdMesh to earn badges!</p>
                <Button
                  onClick={checkForNewBadges}
                  variant="outline"
                  className="mt-4"
                  disabled={refreshing}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  Check for Badges
                </Button>
              </CardContent>
            </Card>
          ) : filteredEarnedBadges.length === 0 && searchQuery ? (
            <Card>
              <CardContent className="pt-6 text-center">
                <p>No badges match your search. Try a different search term.</p>
                <Button
                  onClick={clearSearch}
                  variant="outline"
                  className="mt-4"
                >
                  Clear Search
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredEarnedBadges.map(badge => (
                <Card key={badge.badge_id}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <BadgeIcon badge={badge} size="lg" showTooltip={false} />
                        <CardTitle className="text-lg">{badge.metadata.name}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => {
                            setSelectedBadge(badge);
                            setIsShareModalOpen(true);
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Switch
                          checked={badge.is_displayed !== false}
                          onCheckedChange={() => toggleBadgeDisplay(badge.badge_id, badge.is_displayed !== false)}
                        />
                      </div>
                    </div>
                    <CardDescription className="mt-2">
                      {badge.metadata.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {badge.metadata.xp_bonus > 0 && (
                        <div className="flex items-center gap-2 text-sm">
                          <Sparkles className="h-4 w-4 text-yellow-500" />
                          <span>+{badge.metadata.xp_bonus} XP Bonus</span>
                        </div>
                      )}
                      {(badge.metadata.earnings_boost_percent || 0) > 0 && (
                        <div className="flex items-center gap-2 text-sm">
                          <Award className="h-4 w-4 text-green-500" />
                          <span>+{badge.metadata.earnings_boost_percent}% Earnings Boost</span>
                        </div>
                      )}
                      {badge.progress !== undefined && badge.progress_target !== undefined && (
                        <div className="w-full mt-2">
                          <div className="flex justify-between text-xs mb-1">
                            <span>{badge.progress}</span>
                            <span>{badge.progress_target}</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className="h-2 rounded-full bg-primary"
                              style={{ width: `${Math.min(100, (badge.progress / badge.progress_target) * 100)}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="text-xs text-muted-foreground">
                    Earned {typeof badge.awarded_at === 'string' ? formatDistanceToNow(new Date(badge.awarded_at), { addSuffix: true }) : 'recently'}
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="available">
          <Card>
            <CardContent className="pt-6">
              {loadingAllBadges ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-center gap-3 p-3 border rounded-lg animate-pulse">
                      <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 w-1/3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : allBadges.length === 0 ? (
                <div className="text-center py-8">
                  <p>No available badges found. Try refreshing the page.</p>
                  <Button
                    onClick={() => fetchAllBadges()}
                    variant="outline"
                    className="mt-4"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Badges
                  </Button>
                </div>
              ) : filteredAvailableBadges.length === 0 && searchQuery ? (
                <div className="text-center py-8">
                  <p>No available badges match your search. Try a different search term.</p>
                  <Button
                    onClick={clearSearch}
                    variant="outline"
                    className="mt-4"
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredAvailableBadges.map(badge => (
                      <div key={badge.badge_id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <BadgeIcon
                          badge={badge}
                          size="md"
                          showTooltip={false}
                        />
                        <div className="flex-1">
                          <h3 className="font-medium">{badge.metadata.name}</h3>
                          <p className="text-sm text-muted-foreground">{badge.metadata.description}</p>

                          {badge.progress !== undefined && badge.progress_target !== undefined && (
                            <div className="w-full mt-2">
                              <div className="flex justify-between text-xs mb-1">
                                <span>{badge.progress}</span>
                                <span>{badge.progress_target}</span>
                              </div>
                              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div
                                  className="h-2 rounded-full bg-primary"
                                  style={{ width: `${Math.min(100, (badge.progress / badge.progress_target) * 100)}%` }}
                                />
                              </div>
                            </div>
                          )}

                          <div className="flex items-center gap-4 mt-2">
                            {badge.metadata.xp_bonus > 0 && (
                              <div className="flex items-center gap-1 text-xs text-yellow-500">
                                <Sparkles className="h-3 w-3" />
                                <span>+{badge.metadata.xp_bonus} XP</span>
                              </div>
                            )}

                            {(badge.metadata.earnings_boost_percent || 0) > 0 && (
                              <div className="flex items-center gap-1 text-xs text-green-500">
                                <Award className="h-3 w-3" />
                                <span>+{badge.metadata.earnings_boost_percent}% Earnings</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="xp">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-yellow-500" />
                XP Activity
              </CardTitle>
              <CardDescription>
                Track your XP gains in the Agent Pioneer program
              </CardDescription>
            </CardHeader>
            <CardContent>
              <XpLogs searchQuery={searchQuery} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
