'use client'

import { useState } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Card } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, ThumbsUp } from 'lucide-react'
import { cn } from '@/lib/utils'

const mockReviews = [
  {
    id: 'review_1',
    productName: 'Instantly',
    rating: 5,
    review: 'Instantly helped me scale cold outreach without burning domains.',
    createdAt: 'April 6, 2025',
    user_id: 'user_1',
    comments: [
      {
        id: 'c1',
        role: 'agent',
        user: 'founderX',
        comment: 'Did this work well in Europe too?',
        emoji: '🇪🇺',
        likes: 2,
      },
      {
        id: 'c2',
        role: 'brand',
        user: 'SmartleadSupport',
        comment: 'We offer great deliverability in EU as well!',
        emoji: '✅',
        likes: 3,
      },
      {
        id: 'c3',
        role: 'user',
        user: 'otherUser123',
        comment: 'How’s the inbox rate been for you?',
        emoji: '🤔',
        likes: 1,
      },
    ],
  },
]

export default function ReviewsPage() {
  const [commentText, setCommentText] = useState('')
  const [selectedEmoji, setSelectedEmoji] = useState('👍')
  const [comments, setComments] = useState(mockReviews[0].comments)
  const [collapsed, setCollapsed] = useState(true)

  const currentUser = {
    user: 'goman3917',
    role: 'user',
    user_id: 'user_1',
  }

  const emojiOptions = ['👍', '✅', '❤️', '💡', '🤔', '🔥']

  const handleAddComment = () => {
    if (!commentText.trim()) return
    const newComment = {
      id: `c${comments.length + 1}`,
      user: currentUser.user,
      role: currentUser.role,
      comment: commentText,
      emoji: selectedEmoji,
      likes: 0,
    }
    setComments((prev) => [...prev, newComment])
    setCommentText('')
    setSelectedEmoji('👍')
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold mb-4">Your Reviews</h2>

      <Accordion type="multiple" className="w-full">
        {mockReviews.map((review, idx) => {
          const visibleComments = collapsed ? comments.slice(0, 2) : comments

          return (
            <AccordionItem key={review.id} value={`review-${idx}`}>
              <AccordionTrigger className="flex flex-col items-start gap-1">
                <div className="text-sm font-medium">{review.productName}</div>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < review.rating ? 'text-yellow-500' : 'text-gray-300'}`}
                      fill={i < review.rating ? 'currentColor' : 'none'}
                    />
                  ))}
                </div>
                <div className="text-xs text-muted-foreground">{review.createdAt}</div>
              </AccordionTrigger>

              <AccordionContent>
                <Card className="p-4 mt-2 space-y-4 bg-muted/30">
                  <p className="text-sm text-muted-foreground">{review.review}</p>

                  {/* Comments */}
                  <div className="space-y-2">
                    <div className="text-xs font-semibold text-muted-foreground">
                      💬 Comments ({comments.length})
                    </div>

                    {visibleComments.map((c) => (
                      <div
                        key={c.id}
                        className="flex items-start gap-2 bg-muted rounded-md p-3 text-sm"
                      >
                        {/* Avatar */}
                        <div className="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center font-bold text-xs">
                          {c.user.charAt(0).toUpperCase()}
                        </div>

                        <div className="flex-1 space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="font-medium">@{c.user}</div>
                            <div className="flex gap-1 text-xs text-muted-foreground items-center">
                              {c.emoji} <ThumbsUp className="h-3 w-3" /> {c.likes}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground lowercase">
                            <Badge variant="outline">{c.role}</Badge>
                          </div>
                          <p>{c.comment}</p>
                        </div>
                      </div>
                    ))}

                    {comments.length > 2 && (
                      <Button
                        variant="link"
                        size="sm"
                        className="text-xs text-muted-foreground mt-1"
                        onClick={() => setCollapsed((prev) => !prev)}
                      >
                        {collapsed ? 'Show more comments' : 'Hide comments'}
                      </Button>
                    )}
                  </div>

                  {/* Add Comment */}
                  <form
                    onSubmit={(e) => {
                      e.preventDefault()
                      handleAddComment()
                    }}
                    className="space-y-2"
                  >
                    <Textarea
                      placeholder="Write a comment..."
                      value={commentText}
                      onChange={(e) => setCommentText(e.target.value)}
                      rows={2}
                    />
                    <div className="flex justify-between items-center">
                      <div className="flex gap-2">
                        {emojiOptions.map((emoji) => (
                          <button
                            key={emoji}
                            type="button"
                            onClick={() => setSelectedEmoji(emoji)}
                            className={cn(
                              'px-2 py-1 rounded-full text-xl transition',
                              selectedEmoji === emoji
                                ? 'bg-primary text-white'
                                : 'bg-muted'
                            )}
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                      <Button size="sm" type="submit">
                        Reply
                      </Button>
                    </div>
                  </form>
                </Card>
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>
    </div>
  )
}
