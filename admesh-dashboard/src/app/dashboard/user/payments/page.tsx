"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useCreditsContext } from "@/contexts/credits-context";
import { centsToDollars, formatCurrency } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  // CardFooter, // Commented out - used in Purchase Credits tab
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import WithdrawModal from "@/components/WithdrawModal";
import {
  CheckCircle,
  Clock,
  TrendingUp,
  DollarSign,
  // CreditCard, // Commented out - used in Purchase Credits tab
  // Loader2, // Commented out - used in Purchase Credits tab
  ArrowUpRight
} from "lucide-react";
// import { toast } from "sonner"; // Uncomment if needed for future toast notifications
import { format } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Credit packages - commented out as Purchase Credits tab is removed
// const creditPackages = [
//   { id: 'basic', name: 'Basic', credits: 10, price: 5, popular: false },
//   { id: 'standard', name: 'Standard', credits: 50, price: 20, popular: true },
//   { id: 'premium', name: 'Premium', credits: 100, price: 35, popular: false },
// ];

interface Earning {
  id: string;
  amount: number;
  currency: string;
  timestamp: string;
  brand_id: string;
  offer_id: string;
  event_type: string;
  intent_type: string;
  brand_name?: string;
  offer_title?: string;
  user_earning?: number;
}

export default function UserPaymentsPage() {
  const { user } = useAuth();
  const { credits, refreshCredits } = useCreditsContext();

  // Debug credits value
  useEffect(() => {
    console.log('Credits in payments page:', credits, typeof credits);
  }, [credits]);
  const [conversions, setConversions] = useState<
    { created_at: string; reward: number; status: string }[]
  >([]);
  const [earnings, setEarnings] = useState<Earning[]>([]);
  const [pendingTotal, setPendingTotal] = useState<number>(0);
  const [payoutMethod, setPayoutMethod] = useState<{ email?: string; provider?: string } | null>(null);
  const [withdrawOpen, setWithdrawOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [earningsLoading, setEarningsLoading] = useState(true);
  const [creditSpends, setCreditSpends] = useState<
    { id?: string; type: string; created_at: string; credits: number }[]
  >([]);
  const [totalCreditSpends, setTotalCreditSpends] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  // Fixed page size of 10 items
  const pageSize = 10;
  const [activeTab, setActiveTab] = useState("overview");
  // Purchase Credits tab state variables - commented out
  // const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  // const [purchaseLoading, setPurchaseLoading] = useState(false);
  // const [purchaseComplete, setPurchaseComplete] = useState(false);
  const [timeRange, setTimeRange] = useState("30d");
  const [earningsStats, setEarningsStats] = useState({
    totalEarnings: 0,
    thisMonth: 0,
    lastMonth: 0,
    avgPerConversion: 0,
  });

  // Function to fetch credit spends with pagination
  const fetchCreditSpends = useCallback(async (page: number = 1) => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credit-spends?page=${page}&page_size=${pageSize}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      if (!res.ok) {
        throw new Error(`Failed to fetch credit spends: ${res.status}`);
      }

      const data = await res.json();
      setCreditSpends(data.credit_spends || []);
      setTotalCreditSpends(data.total || 0);
      setTotalPages(data.total_pages || 1);
      setCurrentPage(data.page || 1);
    } catch (error) {
      console.error('Error fetching credit spends:', error);
    }
  }, [user, pageSize]);

  // Function to handle page changes
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchCreditSpends(newPage);
    }
  };

  useEffect(() => {
    if (!user) return;

    const fetchCreditData = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credits`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        );
        const data = await res.json();

        if (data?.pending_total != null) {
          setPendingTotal(data.pending_total);
        }

        // Set total credit spends for pagination info
        if (data?.total_credit_spends != null) {
          setTotalCreditSpends(data.total_credit_spends);
          // Calculate total pages
          setTotalPages(Math.ceil(data.total_credit_spends / pageSize));
        }

        // For backward compatibility, use the credit_spends from this endpoint
        // until we fetch the paginated data
        if (data?.credit_spends) {
          setCreditSpends(data.credit_spends);
        }
      } catch (error) {
        console.error('Error fetching credit data:', error);
      }
    };

    fetchCreditData();
    // Fetch the first page of credit spends
    fetchCreditSpends(1);
    refreshCredits(); // Refresh credits from the context
  }, [user, refreshCredits, fetchCreditSpends, pageSize]);

  useEffect(() => {
    if (!user) return;
    setLoading(true);

    const fetchData = async () => {
      try {
        const token = await user.getIdToken();
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credits`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          }
        );

        const data = await response.json();
        setConversions(data.conversions || []);
        setPayoutMethod(data.payout_method);

        // Generate chart data from conversions
        processDataForChart(data.conversions || []);
      } catch (err) {
        console.error("Error fetching payment data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  // Fetch user earnings
  useEffect(() => {
    if (!user) return;
    setEarningsLoading(true);

    const fetchEarnings = async () => {
      try {
        const token = await user.getIdToken();
        let response;

        // Try the new endpoint first
        try {
          response = await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/earnings/user-earnings/${user.uid}?time_range=${timeRange}`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!response.ok) {
            // If the new endpoint fails, fall back to the original endpoint
            console.log("Falling back to original endpoint");
            response = await fetch(
              `${process.env.NEXT_PUBLIC_API_BASE_URL}/earnings/user/${user.uid}?time_range=${timeRange}`,
              {
                headers: {
                  Authorization: `Bearer ${token}`,
                },
              }
            );
          }
        } catch (error) {
          console.error("Error with new endpoint, falling back to original:", error);
          // Fall back to the original endpoint
          response = await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/earnings/user/${user.uid}?time_range=${timeRange}`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
        }

        if (!response.ok) {
          throw new Error("Failed to fetch earnings");
        }

        const data = await response.json();
        setEarnings(data.earnings || []);

        // Calculate stats
        const totalEarnings = data.earnings?.reduce(
          (sum: number, e: Earning) => sum + (e.user_earning || 0),
          0
        ) || 0;

        // Calculate this month's earnings
        const now = new Date();
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const thisMonthEarnings = data.earnings?.reduce(
          (sum: number, e: Earning) => {
            const earningDate = new Date(e.timestamp);
            return earningDate >= thisMonthStart ? sum + (e.user_earning || 0) : sum;
          },
          0
        ) || 0;

        // Calculate last month's earnings
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        const lastMonthEarnings = data.earnings?.reduce(
          (sum: number, e: Earning) => {
            const earningDate = new Date(e.timestamp);
            return earningDate >= lastMonthStart && earningDate <= lastMonthEnd
              ? sum + (e.user_earning || 0)
              : sum;
          },
          0
        ) || 0;

        // Calculate average per conversion
        const avgPerConversion = data.earnings?.length > 0
          ? totalEarnings / data.earnings.length
          : 0;

        setEarningsStats({
          totalEarnings,
          thisMonth: thisMonthEarnings,
          lastMonth: lastMonthEarnings,
          avgPerConversion,
        });
      } catch (error) {
        console.error("Error fetching earnings:", error);
      } finally {
        setEarningsLoading(false);
      }
    };

    fetchEarnings();
  }, [user, timeRange]);

  // Format currency values (cents to dollars)
  const formatCurrencyValue = (amount: number, currency: string = "USD") => {
    // Convert from cents to dollars first, then format
    const dollars = centsToDollars(amount);
    return formatCurrency(dollars, currency);
  };

  // Format timestamp to handle different formats including Firestore timestamps
  const formatTimestamp = (timestamp: string | number | { seconds: number; nanoseconds: number } | Date | unknown): string => {
    if (!timestamp) return 'Unknown date';

    try {
      // Check if timestamp is a Firestore timestamp object with seconds and nanoseconds
      if (timestamp && typeof timestamp === 'object' && 'seconds' in timestamp) {
        // Convert Firestore timestamp to Date
        return new Date((timestamp as { seconds: number }).seconds * 1000).toLocaleString();
      }

      // Check if timestamp is a string that might be an ISO date
      if (typeof timestamp === 'string') {
        // Try to parse the ISO date string
        const date = new Date(timestamp);
        // Check if the date is valid
        if (!isNaN(date.getTime())) {
          return date.toLocaleString();
        }
        return 'Invalid date format';
      }

      // If it's a number (unix timestamp in seconds or milliseconds)
      if (typeof timestamp === 'number') {
        // If it's in seconds (Firestore), convert to milliseconds
        if (timestamp < 10000000000) {
          return new Date(timestamp * 1000).toLocaleString();
        }
        // If it's already in milliseconds
        return new Date(timestamp).toLocaleString();
      }

      // If it's already a Date object
      if (timestamp instanceof Date) {
        return timestamp.toLocaleString();
      }

      // If we can't determine the format, try to convert directly
      return new Date(timestamp as unknown as string | number).toLocaleString();
    } catch (error) {
      console.error('Error formatting timestamp:', error, timestamp);
      return 'Invalid date';
    }
  };

  const processDataForChart = (convs: { created_at: string; reward: number }[]) => {
    // Group by date and sum rewards
    const groupedByDate = convs.reduce((acc: Record<string, { date: string; amount: number }>, curr) => {
      const date = curr.created_at?.split("T")[0];
      if (!date) return acc;

      if (!acc[date]) {
        acc[date] = { date, amount: 0 };
      }

      if (curr.reward && !isNaN(curr.reward)) {
        acc[date].amount += Number(curr.reward);
      }

      return acc;
    }, {});

    // Convert to array and sort by date
    return Object.values(groupedByDate).sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  };

  const lifetimeEarnings = conversions
    .filter((c) => c.status === "confirmed")
    .reduce((sum, c) => sum + (c.reward || 0), 0);

  const confirmedTotal = conversions
    .filter((c) => c.status === "confirmed")
    .reduce((sum, c) => sum + (c.reward || 0), 0);

  const conversionRate = conversions.length
    ? (
        (conversions.filter((c) => c.status === "confirmed").length /
          conversions.length) *
        100
      ).toFixed(1)
    : 0;

  // Purchase Credits tab function - commented out
  // const handlePurchase = async () => {
  //   if (!selectedPackage || !user) return;

  //   const packageDetails = creditPackages.find(pkg => pkg.id === selectedPackage);
  //   if (!packageDetails) return;

  //   setPurchaseLoading(true);

  //   try {
  //     // Simulate API call to purchase credits
  //     await new Promise(resolve => setTimeout(resolve, 1500));

  //     // Add credits to user account
  //     const token = await user.getIdToken();
  //     const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credits/add`, {
  //       method: 'POST',
  //       headers: {
  //         'Authorization': `Bearer ${token}`,
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ amount: packageDetails.credits }),
  //     });

  //     if (!response.ok) {
  //       throw new Error('Failed to add credits');
  //     }

  //     // Show success message
  //     setPurchaseComplete(true);
  //     refreshCredits();
  //     toast.success(`Successfully purchased ${packageDetails.credits} credits!`);

  //     // Reset after a delay
  //     setTimeout(() => {
  //       setPurchaseComplete(false);
  //       setSelectedPackage(null);
  //     }, 3000);
  //   } catch (error) {
  //     console.error('Error purchasing credits:', error);
  //     toast.error('Failed to purchase credits. Please try again.');
  //   } finally {
  //     setPurchaseLoading(false);
  //   }
  // };

  const renderSkeleton = () => (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="w-full">
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-4 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-5 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    </>
  );

  return (
    <div className="p-4 md:p-6 lg:p-8 max-w-7xl mx-auto space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">My Wallet & Rewards</h1>
      </div>

      <Tabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-3 md:grid-cols-3 w-full md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="earnings">Earnings</TabsTrigger>
          {/* Purchase Credits tab commented out */}
          {/* <TabsTrigger value="purchase">Purchase Credits</TabsTrigger> */}
          <TabsTrigger value="payouts">Payouts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          {loading ? (
            renderSkeleton()
          ) : (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 pb-2">
                    <CardTitle className="flex items-center gap-2 text-green-900">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      Available Rewards
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p className="text-3xl font-bold text-green-600">
                      {credits !== null ? (typeof credits === 'number' ? credits.toFixed(0) : String(credits)) : '--'} Credits
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Available for use
                    </p>
                  </CardContent>
                </Card>

                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 pb-2">
                    <CardTitle className="flex items-center gap-2 text-amber-900">
                      <Clock className="h-5 w-5 text-amber-600" />
                      Pending Rewards
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p className="text-3xl font-bold text-amber-600">
                      {typeof pendingTotal === 'number' ? pendingTotal.toFixed(0) : '0'} Credits
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Awaiting approval
                    </p>
                    <p className="text-xs text-muted-foreground mt-1 italic">
      Est.total value: ${(pendingTotal * 0.01).toFixed(2)} – ${(pendingTotal * 0.1).toFixed(2)} USD.
    </p>
    <p className="text-xs text-muted-foreground mt-1 italic">
      Terms and conditions apply.
    </p>
           
                  </CardContent>
                </Card>

                <Card className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 pb-2">
                    <CardTitle className="flex items-center gap-2 text-blue-900">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      Lifetime Stats
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <p className="text-3xl font-bold text-blue-600">
                      {typeof lifetimeEarnings === 'number' ? lifetimeEarnings.toFixed(0) : '0'} Credits
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm text-muted-foreground">
                        Conversion rate
                      </span>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700"
                      >
                        {conversionRate}%
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-sm text-muted-foreground">
                        Total transactions
                      </span>
                      <Badge
                        variant="outline"
                        className="bg-blue-50 text-blue-700"
                      >
                        {conversions.length}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Reward Activity</CardTitle>
                  <CardDescription>
                    Track your reward usage history.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {creditSpends.length > 0 ? (
                    <>
                      <div className="space-y-4 mb-4">
                        {creditSpends.map((entry, i) => (
                          <div
                            key={entry.id || i}
                            className="flex items-start justify-between border-b pb-3"
                          >
                            <div>
                              <p className="font-medium text-sm text-gray-800 dark:text-gray-200">
                                {entry.type === "query" ? "Query Recommendation" : entry.type}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {formatTimestamp(entry.created_at)}
                              </p>
                            </div>
                            <div className={`text-sm font-semibold ${entry.type === "xp_conversion" ? "text-green-600 dark:text-green-400" : "text-indigo-600 dark:text-indigo-400"}`}>
                              {entry.type === "xp_conversion" ? `+${entry.credits} credits` : `-${entry.credits} credits`}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Pagination Controls - Always show if there are any entries */}
                      {totalCreditSpends > 0 && (
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-2">
                          <div className="text-sm text-gray-500">
                            Showing {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, totalCreditSpends)} of {totalCreditSpends} entries
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(currentPage - 1)}
                              disabled={currentPage === 1}
                            >
                              Previous
                            </Button>
                            <div className="text-sm font-medium px-2">
                              {currentPage} / {totalPages}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePageChange(currentPage + 1)}
                              disabled={currentPage === totalPages}
                            >
                              Next
                            </Button>
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 dark:text-gray-400">
                        No reward activity yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="earnings" className="space-y-6 mt-6">
          {earningsLoading ? (
            renderSkeleton()
          ) : (
            <>
              {/* Earnings Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Total Earnings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrencyValue(earningsStats.totalEarnings)}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Lifetime earnings
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      This Month
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrencyValue(earningsStats.thisMonth)}
                    </div>
                    <div className="flex items-center text-xs text-green-600 mt-1">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      <span>Current period</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Last Month
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrencyValue(earningsStats.lastMonth)}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Previous period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">
                      Avg. Per Conversion
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrencyValue(earningsStats.avgPerConversion)}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Per successful conversion
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Time Range Selector */}
              <div className="flex justify-end mb-4">
                <div className="inline-flex rounded-md shadow-sm">
                  <Button
                    variant={timeRange === "7d" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTimeRange("7d")}
                    className="rounded-l-md rounded-r-none"
                  >
                    7 Days
                  </Button>
                  <Button
                    variant={timeRange === "30d" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTimeRange("30d")}
                    className="rounded-none border-l-0 border-r-0"
                  >
                    30 Days
                  </Button>
                  <Button
                    variant={timeRange === "90d" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setTimeRange("90d")}
                    className="rounded-r-md rounded-l-none"
                  >
                    90 Days
                  </Button>
                </div>
              </div>

              {/* Earnings Table */}
              <Card>
                <CardHeader>
                  <CardTitle>Earnings History</CardTitle>
                  <CardDescription>
                    Your earnings from conversions and recommendations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Brand</TableHead>
                        <TableHead>Offer</TableHead>
                        <TableHead>Intent Type</TableHead>
                        <TableHead>Event Type</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {earnings.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-6">
                            No earnings found for the selected time period.
                          </TableCell>
                        </TableRow>
                      ) : (
                        earnings.map((earning) => (
                          <TableRow key={earning.id}>
                            <TableCell>
                              {format(new Date(earning.timestamp), "MMM d, yyyy")}
                            </TableCell>
                            <TableCell>{earning.brand_name || earning.brand_id}</TableCell>
                            <TableCell>{earning.offer_title || earning.offer_id}</TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {earning.intent_type}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="bg-green-50 text-green-700">
                                {earning.event_type}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrencyValue(earning.user_earning || 0)}
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        {/* Purchase Credits tab content commented out */}
        {/* <TabsContent value="purchase" className="space-y-6 mt-6">
          <div className="mb-8">
            <p className="text-muted-foreground">
              Rewards are used to power your AI chat interactions. Each message costs 1 reward credit.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {creditPackages.map((pkg) => (
              <Card
                key={pkg.id}
                className={`relative ${selectedPackage === pkg.id ? 'border-primary ring-2 ring-primary' : ''} ${pkg.popular ? 'border-yellow-400' : ''}`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-yellow-400 text-black px-3 py-1 rounded-full text-xs font-bold">
                    MOST POPULAR
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{pkg.name} Package</CardTitle>
                  <CardDescription>{pkg.credits} Rewards</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold mb-4">${pkg.price}</p>
                  <p className="text-muted-foreground text-sm">
                    ${(pkg.price / pkg.credits).toFixed(2)} per reward
                  </p>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    variant={selectedPackage === pkg.id ? "default" : "outline"}
                    onClick={() => setSelectedPackage(pkg.id)}
                  >
                    {selectedPackage === pkg.id ? 'Selected' : 'Select'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="flex justify-center">
            <Button
              size="lg"
              disabled={!selectedPackage || purchaseLoading || purchaseComplete}
              onClick={handlePurchase}
              className="min-w-[200px]"
            >
              {purchaseLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : purchaseComplete ? (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Purchase Complete
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Purchase Rewards
                </>
              )}
            </Button>
          </div>

          <div className="mt-12 text-center text-sm text-muted-foreground">
            <p>This is a demo payment page. No actual payment will be processed.</p>
            <p>In a production environment, this would integrate with a payment processor like Stripe.</p>
          </div>
        </TabsContent> */}

        <TabsContent value="payouts" className="space-y-6 mt-6">
          {/* Payouts content */}
          <Card>
            <CardHeader>
              <CardTitle>Withdraw Earnings & Rewards</CardTitle>
              <CardDescription>
                Request a payout for your confirmed earnings and rewards.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4 bg-muted rounded-lg">
                <div>
                  <p className="text-sm font-medium">Available for withdrawal</p>
                  <p className="text-2xl font-bold">
                    {formatCurrencyValue(earningsStats.totalEarnings)} + {typeof confirmedTotal === 'number' ? confirmedTotal.toFixed(0) : '0'} Credits
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Combined earnings and rewards
                  </p>
                </div>
                {/* <Button
                  onClick={() => setWithdrawOpen(true)}
                  disabled={confirmedTotal < 10 && earningsStats.totalEarnings <= 0}
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  Withdraw
                </Button> */}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payout History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center h-64 text-center p-6">
                <DollarSign className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-700">
                  No payouts yet
                </h3>
                <p className="text-sm text-gray-500 max-w-md mt-2">
                  Once you withdraw your funds, your payout history will appear
                  here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <WithdrawModal
        open={withdrawOpen}
        onClose={() => setWithdrawOpen(false)}
        balance={centsToDollars(confirmedTotal + earningsStats.totalEarnings)}
        defaultEmail={payoutMethod?.email || ""}
        onWithdraw={async (amountInCents: number) => {
          if (!user) return;
          // Note: amountInCents is already converted to cents in the WithdrawModal component
          await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/user/payments/withdraw`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ uid: user.uid, amount: amountInCents }),
            }
          );
        }}
      />
    </div>
  );
}

