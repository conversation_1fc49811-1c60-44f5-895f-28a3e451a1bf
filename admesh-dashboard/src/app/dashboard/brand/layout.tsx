"use client";

import { ReactNode, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import BrandOnboardingGuard from "@/components/BrandOnboardingGuard";
import { useAuth } from "@/hooks/use-auth";

interface BrandLayoutProps {
  children: ReactNode;
}

export default function BrandLayout({ children }: BrandLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, onboardingStatus } = useAuth();
  const isOnboardingPage = pathname?.includes("/dashboard/brand/onboarding") || false;

  // Check if user is on onboarding page but has already completed onboarding
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (isOnboardingPage && user) {
        // First check if we already have the status from Firebase claims
        if (onboardingStatus === "completed") {
          // If onboarding is completed, redirect to offers page
          router.replace("/dashboard/brand/offers");
          return;
        }

        // If we don't have the status from claims, check with the API
        try {
          const token = await user.getIdToken();
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/onboarding/status`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();

            // If onboarding is completed, redirect to offers page
            if (data.onboarding_status === "completed") {
              router.replace("/dashboard/brand/offers");
            }
          }
        } catch (error) {
          console.error("Error checking onboarding status:", error);
        }
      }
    };

    checkOnboardingStatus();
  }, [isOnboardingPage, user, onboardingStatus, router]);

  // Skip the guard for the onboarding page itself
  if (isOnboardingPage) {
    return <>{children}</>;
  }

  // Apply the guard for all other brand pages
  return (
    <BrandOnboardingGuard>
      {children}
    </BrandOnboardingGuard>
  );
}
