"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Form section component with animations
function FormSection({ title, description, children }: { title: string; description: string; children: React.ReactNode }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.4 }}
    >
      <Card className="w-full mx-auto border border-border/50 shadow-xl overflow-hidden bg-card/95 backdrop-blur-md dark:bg-card/80 dark:border-border/30">
        <CardHeader className="pb-3 sm:pb-4 bg-muted/10 dark:bg-muted/5 border-b border-border/10 px-4 sm:px-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.4 }}
          >
            <CardTitle className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary via-blue-500 to-primary bg-clip-text text-transparent">{title}</CardTitle>
            <CardDescription className="text-sm sm:text-base text-muted-foreground mt-1">{description}</CardDescription>
          </motion.div>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6 pt-4 sm:pt-6 w-full px-4 sm:px-6">
          {children}
        </CardContent>
      </Card>
    </motion.div>
  );
}

export default FormSection;
