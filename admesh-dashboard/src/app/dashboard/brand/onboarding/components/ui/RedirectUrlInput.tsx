"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";

// Isolated input component for redirect URL to prevent re-renders affecting accordion
interface RedirectUrlInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
}

const REDIRECT_URL_STORAGE_KEY = 'admesh_redirect_url';

const RedirectUrlInput = ({ value, onChange, error }: RedirectUrlInputProps) => {
  // Use local state to prevent parent re-renders on every keystroke
  const [localValue, setLocalValue] = useState(() => {
    // Initialize from props or local storage if available
    if (typeof window !== 'undefined') {
      const savedUrl = localStorage.getItem(REDIRECT_URL_STORAGE_KEY);
      // Use saved URL if it exists and the current value is empty
      if (savedUrl && (!value || value === "")) {
        // Notify parent component about the stored value immediately
        // This ensures the parent component has the correct value right away
        onChange(savedUrl);
        return savedUrl;
      }
    }
    return value;
  });

  // Update local value when prop value changes
  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value);
    }
  }, [value, localValue]);

  // Save to local storage and update parent state
  const saveValue = (newValue: string) => {
    // Save to local storage
    if (typeof window !== 'undefined') {
      localStorage.setItem(REDIRECT_URL_STORAGE_KEY, newValue);
    }

    // Always update parent state to ensure consistency
    onChange(newValue);
  };

  // Handle blur event to update parent state only when focus is lost
  const handleBlur = () => {
    saveValue(localValue);
  };

  // Handle key press to update parent state when Enter is pressed
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      saveValue(localValue);
    }
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);

    // Save to local storage on every change
    // This ensures the URL is saved even if the user navigates away without blur/enter
    if (typeof window !== 'undefined') {
      localStorage.setItem(REDIRECT_URL_STORAGE_KEY, newValue);
    }

    // Also update parent component state immediately
    onChange(newValue);
  };

  return (
    <div>
      <Input
        placeholder="https://yourbrand.com/signup"
        value={localValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={error ? 'border-red-500' : ''}
      />
      {localValue && (
        <p className="text-xs text-muted-foreground mt-1">
          <span className="text-green-600 dark:text-green-400">✓</span> URL saved automatically
        </p>
      )}
    </div>
  );
};

export default RedirectUrlInput;
