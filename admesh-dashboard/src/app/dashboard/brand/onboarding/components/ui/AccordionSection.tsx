"use client";

import { ChevronDown, ChevronUp } from "lucide-react";

// Accordion section for tracking steps
interface AccordionSectionProps {
  id: string;
  title: string;
  isExpanded: boolean;
  onToggle: (id: string) => void;
  stepNumber: number;
  bgColorClass?: string;
  textColorClass?: string;
  children: React.ReactNode;
}

const AccordionSection = ({
  id,
  title,
  isExpanded,
  onToggle,
  stepNumber,
  bgColorClass = "bg-card dark:bg-card/80",
  textColorClass = "text-foreground",
  children
}: AccordionSectionProps) => {
  return (
    <div className={`${bgColorClass} border border-border/30 rounded-xl shadow-sm mb-4 overflow-hidden transition-all duration-300`} id={id}>
      <div
        className={`flex items-center justify-between p-4 cursor-pointer ${isExpanded ? 'border-b border-border/20' : ''}`}
        onClick={() => {
          onToggle(id);
        }}
      >
        <h3 className={`font-medium text-base flex items-center ${textColorClass}`}>
          <div className={`${bgColorClass === "bg-card dark:bg-card/80" ? "bg-primary/10 dark:bg-primary/20" : bgColorClass.replace('bg-', 'bg-').replace('/20', '/50')} rounded-full w-6 h-6 flex items-center justify-center ${textColorClass} font-bold mr-2`}>
            {stepNumber}
          </div>
          {title}
        </h3>
        <div className="flex items-center">
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="p-4" onClick={(e) => e.stopPropagation()}>
          {children}
        </div>
      )}
    </div>
  );
};

export default AccordionSection;
