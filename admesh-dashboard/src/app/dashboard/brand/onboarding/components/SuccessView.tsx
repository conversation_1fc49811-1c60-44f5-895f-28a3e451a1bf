"use client";

import { motion } from "framer-motion";
import Confetti from "./ui/Confetti";
import { useEffect } from "react";

interface SuccessViewProps {
  productExisted: boolean;
}

const SuccessView = ({ productExisted }: SuccessViewProps) => {
  // Add useEffect to reload the page when the component mounts
  useEffect(() => {
    // Reload the page after a short delay to allow animations to play
    const reloadTimer = setTimeout(() => {
      window.location.reload();
    }, 2000); // 2 seconds delay before reload

    // Clean up the timer if component unmounts
    return () => clearTimeout(reloadTimer);
  }, []);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-background to-background/95 dark:from-background dark:to-background/95">
      <Confetti />
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="text-center max-w-xl px-6 py-10 bg-card/80 dark:bg-card/50 backdrop-blur-md rounded-2xl shadow-xl border border-border/30"
      >
        <motion.div
          animate={{ rotate: [0, 10, -10, 0], scale: [1, 1.2, 1] }}
          transition={{ duration: 0.8, repeat: 1, repeatType: "reverse" }}
          className="text-7xl mb-6 inline-block"
        >
          🎉
        </motion.div>
        <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-primary via-blue-500 to-primary bg-clip-text text-transparent">All set! You&apos;re ready to go</h1>

        {productExisted && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-4 p-4 bg-blue-50/80 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800/30 rounded-lg text-blue-800 dark:text-blue-300"
          >
            <p className="font-medium">Your product already exists in our system</p>
            <p className="text-sm mt-1">
              Once onboarding is complete, you&apos;ll be able to see all analytics for this product in your dashboard.
            </p>
          </motion.div>
        )}

        <p className="text-muted-foreground mb-2">Reloading page...</p>
        <p className="text-sm text-muted-foreground mt-4 mb-6">
          We&apos;ve set up tracking for your offers. You can view your implementation examples under <code className="bg-muted dark:bg-muted/50 px-2 py-1 rounded text-xs">Dashboard → Offers → Edit → Tracking</code>.
        </p>
        <div className="flex justify-center">
          <div className="w-24 h-1.5 bg-muted dark:bg-muted/30 rounded-full relative overflow-hidden shadow-inner">
            <div className="absolute top-0 left-0 h-full w-full bg-gradient-to-r from-primary to-blue-500 animate-pulse" />
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SuccessView;
