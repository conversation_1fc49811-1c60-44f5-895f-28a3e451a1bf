"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { Label } from "@/components/ui/label";
import PixelImplementationExamples from "@/components/PixelImplementationExamples";
import FormSection from "../ui/FormSection";
import AnimatedContainer from "../ui/AnimatedContainer";
import AccordionSection from "../ui/AccordionSection";
import RedirectUrlInput from "../ui/RedirectUrlInput";
import { Tracking } from "@/types/onboarding";

interface TrackingStepProps {
  tracking: Tracking;
  setTracking: (tracking: Tracking | ((prev: Tracking) => Tracking)) => void;
  errors: Record<string, string>;
  onComplete: () => void;
  onBack: () => void;
  loading: boolean;
  productTitle?: string;
  handleTestConversion: () => void;
  isTestingConversion: boolean;
  checkTestConversions: () => void;
  isCheckingConversions: boolean;
  testConversionExists: boolean;
}

const TrackingStep = ({
  tracking,
  setTracking,
  errors,
  onComplete,
  onBack,
  loading,
  productTitle = "",
  handleTestConversion,
  isTestingConversion,
  checkTestConversions,
  isCheckingConversions,
  testConversionExists
}: TrackingStepProps) => {
  const [expandedSection, setExpandedSection] = useState<string>("how-it-works");

  const toggleSection = (sectionId: string, forceExpand: boolean = false) => {
    if (expandedSection === sectionId && !forceExpand) {
      // Special case: If we're in the test-setup section and a test conversion exists,
      // don't collapse the section even if it's clicked again
      if (sectionId === "test-setup" && testConversionExists) {
        return;
      }
      return;
    }

    setExpandedSection(sectionId);

    // Scroll to the section
    setTimeout(() => {
      const sectionElement = document.getElementById(sectionId);
      if (sectionElement) {
        sectionElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  return (
    <AnimatedContainer dataStep={4}>
      <FormSection
        title="Set Up Conversion Tracking"
        description="Track when users complete your desired action to measure your campaign's success."
      >
        <div className="grid gap-6 w-full">
          {/* Section 1: How Conversion Tracking Works */}
          <AccordionSection
            id="how-it-works"
            title="How Conversion Tracking Works"
            isExpanded={expandedSection === "how-it-works"}
            onToggle={toggleSection}
            stepNumber={1}
            bgColorClass="bg-card dark:bg-card/80"
            textColorClass="text-foreground"
          >
            <div className="flex flex-col space-y-2">
              <div className="flex items-center">
                <div className="bg-primary/10 dark:bg-primary/20 rounded-full w-8 h-8 flex items-center justify-center text-primary font-bold mr-3">1</div>
                <div className="flex-1 bg-muted/30 dark:bg-muted/20 p-3 rounded-lg">
                  <p className="text-sm font-medium">User clicks on your AdMesh Link</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-primary/10 dark:bg-primary/20 rounded-full w-8 h-8 flex items-center justify-center text-primary font-bold mr-3">2</div>
                <div className="flex-1 bg-muted/30 dark:bg-muted/20 p-3 rounded-lg">
                  <p className="text-sm font-medium">They&apos;re sent to your landing page with a tracking ID</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-primary/10 dark:bg-primary/20 rounded-full w-8 h-8 flex items-center justify-center text-primary font-bold mr-3">3</div>
                <div className="flex-1 bg-muted/30 dark:bg-muted/20 p-3 rounded-lg">
                  <p className="text-sm font-medium">User completes your desired action (signup)</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-primary/10 dark:bg-primary/20 rounded-full w-8 h-8 flex items-center justify-center text-primary font-bold mr-3">4</div>
                <div className="flex-1 bg-muted/30 dark:bg-muted/20 p-3 rounded-lg">
                  <p className="text-sm font-medium">Our tracking code on your thank-you page records the conversion</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="bg-primary/10 dark:bg-primary/20 rounded-full w-8 h-8 flex items-center justify-center text-primary font-bold mr-3">5</div>
                <div className="flex-1 bg-muted/30 dark:bg-muted/20 p-3 rounded-lg">
                  <p className="text-sm font-medium">The conversion amount is released from your budget and the successful conversion is logged</p>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-end mt-6">
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  onClick={() => toggleSection("redirect-url", true)}
                  className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
                >
                  <div className="relative z-10 flex items-center justify-center">
                    Next <ArrowRight className="ml-2 w-4 h-4" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
                </Button>
              </motion.div>
            </div>
          </AccordionSection>

          {/* Section 2: Redirect URL */}
          <AccordionSection
            id="redirect-url"
            title="Redirect URL"
            isExpanded={expandedSection === "redirect-url"}
            onToggle={toggleSection}
            stepNumber={2}
            bgColorClass="bg-blue-50 dark:bg-blue-900/20"
            textColorClass="text-blue-800 dark:text-blue-300"
          >
            <div className="mb-4 p-3 bg-blue-100/50 dark:bg-blue-800/30 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                This is the page where users will land when they click on your AdMesh offer. We need this to properly track conversions from AdMesh traffic.
              </p>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Redirect URL</Label>
              <RedirectUrlInput
                value={tracking.redirect_url}
                onChange={(value) => {
                  setTracking(prev => ({
                    ...prev,
                    redirect_url: value
                  }));
                }}
                error={errors.redirect_url}
              />
              {errors.redirect_url && (
                <p className="text-xs text-red-500 mt-1">{errors.redirect_url}</p>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  variant="outline"
                  onClick={() => toggleSection("how-it-works", true)}
                  className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
                >
                  <div className="flex items-center">
                    <ArrowLeft className="mr-2 w-4 h-4" /> Back
                  </div>
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  onClick={() => {
                    // Validate redirect URL
                    if (!tracking.redirect_url) {
                      return;
                    }
                    toggleSection("tracking-code", true);
                  }}
                  className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
                >
                  <div className="relative z-10 flex items-center justify-center">
                    Next <ArrowRight className="ml-2 w-4 h-4" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
                </Button>
              </motion.div>
            </div>
          </AccordionSection>

          {/* Section 3: Add Tracking Code */}
          <AccordionSection
            id="tracking-code"
            title="Add Tracking Code to Your Thank-You Page"
            isExpanded={expandedSection === "tracking-code"}
            onToggle={toggleSection}
            stepNumber={3}
            bgColorClass="bg-green-50 dark:bg-green-900/20"
            textColorClass="text-green-800 dark:text-green-300"
          >
            <p className="text-sm text-green-700 dark:text-green-300/90 mb-4">
              After a user completes your desired action (signup), they should see a confirmation or thank-you page.
              <strong className="text-green-800 dark:text-green-200"> Add our tracking code to this page</strong> to record the conversion.
            </p>

            <div className="bg-white dark:bg-black/20 rounded-lg p-4 border border-green-200 dark:border-green-800/30">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-green-800 dark:text-green-300">Choose your implementation method:</h4>
              </div>

              <div className="mt-4">
                <PixelImplementationExamples productSlug={productTitle ? productTitle.toLowerCase().replace(/[^a-z0-9]+/g, '-') : 'your-product'} />
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  variant="outline"
                  onClick={() => toggleSection("redirect-url", true)}
                  className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
                >
                  <div className="flex items-center">
                    <ArrowLeft className="mr-2 w-4 h-4" /> Back
                  </div>
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  onClick={() => toggleSection("test-setup", true)}
                  className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
                >
                  <div className="relative z-10 flex items-center justify-center">
                    Next <ArrowRight className="ml-2 w-4 h-4" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
                </Button>
              </motion.div>
            </div>
          </AccordionSection>

          {/* Section 4: Test Setup */}
          <AccordionSection
            id="test-setup"
            title="Test Your Conversion Tracking"
            isExpanded={expandedSection === "test-setup"}
            onToggle={toggleSection}
            stepNumber={4}
            bgColorClass="bg-purple-50 dark:bg-purple-900/20"
            textColorClass="text-purple-800 dark:text-purple-300"
          >
            <p className="text-sm text-purple-700 dark:text-purple-300/90 mb-4">
              Let&apos;s make sure your tracking is set up correctly. Follow these steps to test your conversion tracking:
            </p>

            <div className="space-y-4">
              <div className="bg-white dark:bg-black/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                <h4 className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">Step 1: Add the tracking code</h4>
                <p className="text-sm text-purple-700 dark:text-purple-300/90">
                  Make sure you&apos;ve added the tracking code to your thank-you page as shown in the previous step.
                </p>
              </div>

              <div className="bg-white dark:bg-black/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                <h4 className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">Step 2: Test a conversion</h4>
                <p className="text-sm text-purple-700 dark:text-purple-300/90 mb-3">
                  Click the button below to simulate a conversion. This will open a new tab that will redirect to your landing page.
                  Complete the signup process to test if the conversion is tracked correctly.
                </p>
                <Button
                  onClick={handleTestConversion}
                  disabled={isTestingConversion || !tracking.redirect_url}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {isTestingConversion ? "Testing..." : "Test Conversion"}
                </Button>
                {!tracking.redirect_url && (
                  <p className="text-xs text-red-500 mt-2">Please enter a redirect URL first</p>
                )}
              </div>

              <div className="bg-white dark:bg-black/20 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                <h4 className="text-sm font-medium text-purple-800 dark:text-purple-300 mb-2">Step 3: Verify the test conversion</h4>
                <p className="text-sm text-purple-700 dark:text-purple-300/90 mb-3">
                  After completing the test conversion, click the button below to check if it was recorded successfully.
                </p>
                <Button
                  onClick={checkTestConversions}
                  disabled={isCheckingConversions}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  {isCheckingConversions ? "Checking..." : "Check Test Conversion"}
                </Button>

                {testConversionExists && (
                  <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg text-green-800 dark:text-green-300">
                    <p className="text-sm font-medium">✅ Test conversion detected!</p>
                    <p className="text-xs mt-1">Your tracking is set up correctly. You can now complete the setup.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  variant="outline"
                  onClick={() => toggleSection("tracking-code", true)}
                  className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
                >
                  <div className="flex items-center">
                    <ArrowLeft className="mr-2 w-4 h-4" /> Back
                  </div>
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
                <Button
                  onClick={onComplete}
                  disabled={loading || !testConversionExists}
                  title={testConversionExists ? "Complete setup" : "Please complete a test conversion first"}
                  className="w-full sm:w-auto px-6 py-2 rounded-full relative overflow-hidden group shadow-md hover:shadow-lg"
                >
                  <div className="relative z-10 flex items-center justify-center">
                    Complete Setup <ArrowRight className="ml-2 w-4 h-4" />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-500 opacity-90 group-hover:opacity-100 transition-all duration-300"></div>
                </Button>
              </motion.div>
            </div>
          </AccordionSection>

          {/* Back button to return to offer step */}
          <div className="flex justify-start mt-4">
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} className="w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={onBack}
                className="w-full sm:w-auto border-primary/20 hover:border-primary/40 dark:border-primary/30 dark:hover:border-primary/50 transition-all duration-300 rounded-full px-5"
              >
                <div className="flex items-center">
                  <ArrowLeft className="mr-2 w-4 h-4" /> Back to Offer Setup
                </div>
              </Button>
            </motion.div>
          </div>
        </div>
      </FormSection>
    </AnimatedContainer>
  );
};

export default TrackingStep;
