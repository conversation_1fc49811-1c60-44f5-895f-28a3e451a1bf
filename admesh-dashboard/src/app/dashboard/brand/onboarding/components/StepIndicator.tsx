"use client";

import { motion } from "framer-motion";
import { Building, Package, Gift, Plug, CheckCircle2 } from "lucide-react";

// Enhanced Step progress indicator with visual timeline and animations
function StepIndicator({ step }: { step: number }) {
  const steps = [
    { name: "<PERSON>", icon: <Building className="w-4 h-4 sm:w-5 sm:h-5" />, description: "Company details" },
    { name: "Product", icon: <Package className="w-4 h-4 sm:w-5 sm:h-5" />, description: "Product information" },
    { name: "Offer", icon: <Gift className="w-4 h-4 sm:w-5 sm:h-5" />, description: "Conversion goals" },
    { name: "Tracking", icon: <Plug className="w-4 h-4 sm:w-5 sm:h-5" />, description: "Implementation" }
  ];

  return (
    <div className="mb-10 sm:mb-12 px-1">
      {/* Desktop timeline (hidden on small screens) */}
      <div className="hidden sm:block">
        <div className="flex justify-between items-center relative mb-6">
          {/* Timeline track */}
          <div className="absolute top-7 left-0 right-0 h-1 bg-muted dark:bg-muted/30 z-0">
            {/* Animated progress overlay */}
            <motion.div
              className="h-full bg-gradient-to-r from-primary to-blue-500 rounded-full"
              initial={{ width: `${((step - 1) / (steps.length - 1)) * 100}%` }}
              animate={{ width: `${((step - 1) / (steps.length - 1)) * 100}%` }}
              transition={{ duration: 0.8, ease: "easeInOut" }}
            />
          </div>

          {/* Step indicators */}
          {steps.map((s, i) => (
            <motion.div
              key={i}
              initial={{ y: -10, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: i * 0.15, duration: 0.5 }}
              className={`flex flex-col items-center z-10 relative px-2 ${
                i < step
                  ? "text-primary"
                  : i === step - 1
                    ? "text-primary"
                    : "text-muted-foreground"
              }`}
            >
              {/* Step number bubble with icon */}
              <motion.div
                className={`step-tab rounded-full p-3 mb-3 transform transition-all duration-300 ${
                  i < step
                    ? "bg-primary text-primary-foreground shadow-lg shadow-primary/20 dark:shadow-primary/10 active"
                    : i === step - 1
                      ? "bg-primary/10 dark:bg-primary/20 border-2 border-primary shadow-md active"
                      : "bg-muted dark:bg-muted/30 shadow-sm"
                }`}
                whileHover={{ scale: 1.1, y: -3 }}
                whileTap={{ scale: 0.95 }}
              >
                {i < step ? <CheckCircle2 className="w-5 h-5" /> : s.icon}
              </motion.div>

              {/* Step name */}
              <span className={`text-sm font-semibold mb-1 ${
                i < step
                  ? "text-primary"
                  : i === step - 1
                    ? "text-primary"
                    : "text-muted-foreground"
              }`}>{s.name}</span>

              {/* Step description */}
              <span className="text-xs text-muted-foreground">{s.description}</span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Mobile timeline (visible only on small screens) */}
      <div className="sm:hidden">
        <div className="flex justify-between items-start relative">
          {/* Vertical timeline track */}
          <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-muted dark:bg-muted/30 z-0 h-full" />

          <div className="w-full">
            {steps.map((s, i) => (
              <motion.div
                key={i}
                initial={{ x: -10, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: i * 0.15, duration: 0.5 }}
                className={`flex items-start mb-6 relative ${
                  i < step
                    ? "text-primary"
                    : i === step - 1
                      ? "text-primary"
                      : "text-muted-foreground"
                }`}
              >
                {/* Step indicator with progress */}
                <div className="relative z-10 mr-4">
                  <motion.div
                    className={`rounded-full p-2 transform transition-all duration-300 ${
                      i < step
                        ? "bg-primary text-primary-foreground shadow-lg shadow-primary/20 dark:shadow-primary/10"
                        : i === step - 1
                          ? "bg-primary/10 dark:bg-primary/20 border-2 border-primary shadow-md"
                          : "bg-muted dark:bg-muted/30 shadow-sm"
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {i < step ? <CheckCircle2 className="w-4 h-4" /> : s.icon}
                  </motion.div>

                  {/* Progress indicator for current step */}
                  {i === step - 1 && (
                    <motion.div
                      className="absolute -top-1 -left-1 -right-1 -bottom-1 rounded-full border-2 border-primary"
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: [1, 1.1, 1], opacity: 1 }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    />
                  )}
                </div>

                {/* Step content */}
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className={`text-sm font-semibold ${
                      i < step
                        ? "text-primary"
                        : i === step - 1
                          ? "text-primary"
                          : "text-muted-foreground"
                    }`}>{s.name}</span>

                    {/* Status indicator */}
                    {i < step && (
                      <span className="ml-2 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-0.5 rounded-full">
                        Completed
                      </span>
                    )}
                    {i === step - 1 && (
                      <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-0.5 rounded-full">
                        Current
                      </span>
                    )}
                  </div>
                  <span className="text-xs text-muted-foreground">{s.description}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Progress percentage indicator */}
      <div className="flex justify-between items-center mt-2 px-1">
        <div className="text-xs text-muted-foreground">
          <span className="font-medium text-primary">{Math.round(((step - 1) / (steps.length - 1)) * 100)}%</span> completed
        </div>
        <div className="text-xs text-muted-foreground">
          Step <span className="font-medium text-primary">{step}</span> of <span className="font-medium">{steps.length}</span>
        </div>
      </div>
    </div>
  );
}

export default StepIndicator;
