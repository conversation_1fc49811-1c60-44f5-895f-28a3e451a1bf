"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Loader2, Calendar, Package, Tag, Clock } from "lucide-react";
import { toast } from "sonner";
import SubscriptionPlans from "@/components/SubscriptionPlans";

interface Subscription {
  plan_id: string;
  status: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  current_period_start?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  created_at?: string;
  updated_at?: string;
  promo_credit_applied?: boolean;
  promo_credit_amount_cents?: number;
  trust_score?: number;
}

interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  product_listings_limit: number;
  active_offers_per_product_limit: number;
  promo_credit_cents: number;
  features: string[];
}

export default function SubscriptionPage() {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [canceling, setCanceling] = useState(false);
  const [reactivating, setReactivating] = useState(false);
  const [activeTab, setActiveTab] = useState("current");

  // Check for subscription success parameter in URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('subscription_success');
    const plan = urlParams.get('plan');

    if (success === 'true' && plan) {
      toast.success(`Successfully subscribed to ${plan} plan!`, {
        description: "Your subscription is now active."
      });

      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // Fetch subscription data
  const fetchSubscription = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = await user.getIdToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) {
        throw new Error("Failed to fetch subscription data");
      }

      const data = await response.json();
      setSubscription(data.subscription);
      setPlan(data.plan);

    } catch (error) {
      console.error("Error fetching subscription:", error);
      toast.error("Failed to load subscription information");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!user || !subscription?.stripe_subscription_id) return;

    try {
      setCanceling(true);
      const token = await user.getIdToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription/cancel`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to cancel subscription");
      }

      // Close dialog and refresh subscription data
      setCancelDialogOpen(false);
      await fetchSubscription();

      toast.success("Subscription canceled", {
        description: "Your subscription will remain active until the end of the billing period."
      });

    } catch (error) {
      console.error("Error canceling subscription:", error);
      toast.error("Failed to cancel subscription");
    } finally {
      setCanceling(false);
    }
  };

  // Handle subscription reactivation
  const handleReactivateSubscription = async () => {
    if (!user || !subscription?.stripe_subscription_id) return;

    try {
      setReactivating(true);
      const token = await user.getIdToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription/reactivate`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to reactivate subscription");
      }

      // Refresh subscription data
      await fetchSubscription();

      toast.success("Subscription reactivated", {
        description: "Your subscription will continue automatically."
      });

    } catch (error) {
      console.error("Error reactivating subscription:", error);
      toast.error("Failed to reactivate subscription");
    } finally {
      setReactivating(false);
    }
  };

  // Promo credit is now applied automatically during onboarding

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  // Format price for display
  const formatPrice = (cents?: number) => {
    if (!cents) return "$0.00";
    return `$${(cents / 100).toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Subscription Management</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={fetchSubscription}
          disabled={loading}
          className="rounded-full"
          title="Refresh"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className={loading ? "animate-spin" : ""}
          >
            <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
            <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
            <path d="M16 21h5v-5" />
          </svg>
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="current">Current Plan</TabsTrigger>
          <TabsTrigger value="plans">Available Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-6 mt-6">
          {/* Current Plan Details */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>{plan?.name} Plan</CardTitle>
                  <CardDescription>
                    {plan?.price_monthly_cents ? `${formatPrice(plan.price_monthly_cents)}/month` : "Free"}
                  </CardDescription>
                </div>
                <Badge
                  variant={subscription?.status === "active" ? "default" : "destructive"}
                  className="capitalize"
                >
                  {subscription?.status || "Unknown"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Plan Features */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Product Listings:</span>
                    <span>{plan?.product_listings_limit === 0 || plan?.product_listings_limit === -1 ? "Unlimited" : plan?.product_listings_limit}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Offers per Product:</span>
                    <span>{plan?.active_offers_per_product_limit === 0 || plan?.active_offers_per_product_limit === -1 ? "Unlimited" : plan?.active_offers_per_product_limit}</span>
                  </div>
                  {subscription?.trust_score !== undefined && (
                    <div className="flex items-center gap-2 text-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10" />
                      </svg>
                      <span className="font-medium">Trust Score:</span>
                      <div className="flex items-center gap-2">
                        <span>{subscription.trust_score}/100</span>
                        <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${subscription.trust_score}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Current Period:</span>
                    <span>{formatDate(subscription?.current_period_start)} - {formatDate(subscription?.current_period_end)}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Renewal Status:</span>
                    <span>{subscription?.cancel_at_period_end ? "Cancels at period end" : "Auto-renews"}</span>
                  </div>
                </div>
              </div>

              {/* Promo Credit Section removed - credits are now applied automatically during onboarding */}

              {/* Subscription Actions */}
              <div className="flex flex-col sm:flex-row gap-2 pt-4">
                {subscription?.stripe_subscription_id && (
                  <>
                    {subscription.cancel_at_period_end ? (
                      <Button
                        variant="outline"
                        onClick={handleReactivateSubscription}
                        disabled={reactivating}
                      >
                        {reactivating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Reactivating...
                          </>
                        ) : (
                          "Reactivate Subscription"
                        )}
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => setCancelDialogOpen(true)}
                      >
                        Cancel Subscription
                      </Button>
                    )}
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="mt-6">
          <SubscriptionPlans />
        </TabsContent>
      </Tabs>

      {/* Cancel Subscription Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Subscription</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel your subscription? You&apos;ll still have access to your current plan until the end of the billing period.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCancelDialogOpen(false)}
              disabled={canceling}
            >
              Keep Subscription
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelSubscription}
              disabled={canceling}
            >
              {canceling ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Canceling...
                </>
              ) : (
                "Cancel Subscription"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
