"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, MousePointerClick, DollarSign, Package, Tag } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import SubscriptionStatusCard from "@/components/SubscriptionStatusCard";

interface DashboardStats {
  totalOffers: number;
  activeOffers: number;
  totalProducts: number;
  totalClicks: number;
  totalConversions: number;
  totalSpent: number;
  walletBalance: number;
}

export default function BrandDashboard() {
  const { user } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalOffers: 0,
    activeOffers: 0,
    totalProducts: 0,
    totalClicks: 0,
    totalConversions: 0,
    totalSpent: 0,
    walletBalance: 0
  });
  const [, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const token = await user.getIdToken();

        // Fetch offers data
        const offersRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        // Fetch wallet data
        const walletRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!offersRes.ok || !walletRes.ok) {
          throw new Error("Failed to fetch dashboard data");
        }

        const offersData = await offersRes.json();
        const walletData = await walletRes.json();

        // Calculate stats
        const activeOffers = offersData.offers.filter((offer: { active: boolean }) => offer.active).length;
        const totalProducts = new Set(offersData.offers.map((offer: { product_id: string }) => offer.product_id)).size;
        const totalClicks = offersData.totals.clicks || 0;
        const totalConversions = offersData.totals.conversions || 0;
        const totalSpent = walletData.total_spent || 0;
        const walletBalance = walletData.wallet_balance || 0;

        setStats({
          totalOffers: offersData.offers.length,
          activeOffers,
          totalProducts,
          totalClicks,
          totalConversions,
          totalSpent,
          walletBalance
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  const cards = [
    {
      title: "Active Offers",
      value: stats.activeOffers,
      icon: <Tag className="h-5 w-5 text-blue-500" />,
      onClick: () => router.push("/dashboard/brand/offers")
    },
    {
      title: "Products",
      value: stats.totalProducts,
      icon: <Package className="h-5 w-5 text-purple-500" />,
      onClick: () => router.push("/dashboard/brand/products")
    },
    {
      title: "Total Clicks",
      value: stats.totalClicks,
      icon: <MousePointerClick className="h-5 w-5 text-green-500" />,
      onClick: () => router.push("/dashboard/brand/analytics")
    },
    {
      title: "Conversions",
      value: stats.totalConversions,
      icon: <ArrowUpRight className="h-5 w-5 text-orange-500" />,
      onClick: () => router.push("/dashboard/brand/analytics")
    }
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Dashboard</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Metric Cards */}
        {cards.map((card, index) => (
          <Card
            key={index}
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={card.onClick}
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              {card.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Wallet Balance */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push("/dashboard/brand/billing")}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Wallet Balance
            </CardTitle>
            <DollarSign className="h-5 w-5 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.walletBalance)}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Total spent: {formatCurrency(stats.totalSpent)}
            </p>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/dashboard/brand/offers/new")}
            >
              <Tag className="mr-2 h-4 w-4" />
              Create New Offer
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/dashboard/brand/products/new")}
            >
              <Package className="mr-2 h-4 w-4" />
              Add New Product
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/dashboard/brand/billing")}
            >
              <DollarSign className="mr-2 h-4 w-4" />
              Add Funds
            </Button>
          </CardContent>
        </Card>

        {/* Subscription Status */}
        <SubscriptionStatusCard />
      </div>
    </div>
  );
}
