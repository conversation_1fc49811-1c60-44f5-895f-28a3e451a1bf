"use client";

import { useEffect, useState } from "react";
import { db } from "@/lib/firebase";
import {
  collection,
  getDocs,
  query,
  where,
  Timestamp,
  QueryConstraint,
} from "firebase/firestore";
import { useAuth } from "@/hooks/use-auth";
import { subDays } from "date-fns";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  TrendingUp,
  HandCoins,
  DollarSign,
  BarChart3,
} from "lucide-react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

export default function AnalyticsPage() {
  const { user } = useAuth();
  const [range, setRange] = useState("all");
  const [stats, setStats] = useState({
    totalClicks: 0,
    totalConversions: 0,
    totalOffers: 0,
    totalSpent: 0,
  });
  const [offers, setOffers] = useState<
    { id: string; title: string; clicks: number; conversions: number; ctr: string; spent: string; status: string }[]
  >([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) return;

    const fetchAnalytics = async () => {
      setLoading(true);
      const filters: QueryConstraint[] = [where("brand_id", "==", user.uid)];

      if (range !== "all") {
        const now = new Date();
        const cutoff =
          range === "7d" ? subDays(now, 7) : subDays(now, 30);
        filters.push(where("created_at", ">=", Timestamp.fromDate(cutoff)));
      }

      const q = query(collection(db, "offers"), ...filters);
      const snapshot = await getDocs(q);

      let totalClicks = 0;
      let totalConversions = 0;
      let totalSpent = 0;
      const offersData: {
        id: string;
        title: string;
        clicks: number;
        conversions: number;
        ctr: string;
        spent: string;
        status: string;
      }[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        const clicks = data.click_count || 0;
        const conversions = data.conversion_count || 0;
        const spent = data.total_spent || 0;

        totalClicks += clicks;
        totalConversions += conversions;
        totalSpent += spent;

        offersData.push({
          id: doc.id,
          title: data.title || "Untitled",
          clicks,
          conversions,
          ctr: clicks ? ((conversions / clicks) * 100).toFixed(1) : "0.0",
          spent: `$${spent.toFixed(2)}`,
          status: data.active ? "Active" : "Paused",
        });
      });

      setStats({
        totalClicks,
        totalConversions,
        totalOffers: snapshot.size,
        totalSpent,
      });

      setOffers(offersData);
      setLoading(false);
    };

    fetchAnalytics();
  }, [user, range]);

  const cards = [
    {
      title: "Total Clicks",
      value: stats.totalClicks,
      icon: <TrendingUp className="h-5 w-5 text-blue-500" />,
    },
    {
      title: "Conversions",
      value: stats.totalConversions,
      icon: <HandCoins className="h-5 w-5 text-green-500" />,
    },
    {
      title: "Offers Posted",
      value: stats.totalOffers,
      icon: <BarChart3 className="h-5 w-5 text-violet-500" />,
    },
    {
      title: "Total Spent",
      value: `$${stats.totalSpent}`,
      icon: <DollarSign className="h-5 w-5 text-yellow-500" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Top Bar */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytics Overview</h2>
          <p className="text-muted-foreground text-sm">
            Monitor your offer performance.
          </p>
        </div>

        {/* Time Range Filter */}
        <Select value={range} onValueChange={setRange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="All Time" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Metric Cards */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        {cards.map((card, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              {card.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Offer-Level Table */}
      <Card className="mt-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-semibold text-gray-900">
            Offer-Level Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Clicks</TableHead>
                <TableHead>Conversions</TableHead>
                <TableHead>CTR</TableHead>
                <TableHead>Spent</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-6">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : offers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-6">
                    No data available for this range.
                  </TableCell>
                </TableRow>
              ) : (
                offers.map((offer) => (
                  <TableRow key={offer.id}>
                    <TableCell className="font-medium">{offer.title}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          offer.status === "Active"
                            ? "bg-green-100 text-green-700 border-green-200"
                            : "bg-gray-100 text-gray-600 border-gray-200"
                        }
                      >
                        {offer.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{offer.clicks}</TableCell>
                    <TableCell>{offer.conversions}</TableCell>
                    <TableCell>{offer.ctr}%</TableCell>
                    <TableCell>{offer.spent}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
