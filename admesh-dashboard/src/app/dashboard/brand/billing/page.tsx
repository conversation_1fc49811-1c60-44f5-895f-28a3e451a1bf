"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { centsToDollars, formatCurrency as formatCurrencyUtil } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  CreditCard,
  CircleDollarSign,
} from "lucide-react";

export default function BillingPage() {
  const { user } = useAuth();
  const [walletData, setWalletData] = useState({
    total_available_balance: 0,
    total_promo_available_balance: 0,
    total_promo_balance_spent: 0,
    total_balance_spent: 0,
    total_budget_allocated: 0
  });
  const [transactions, setTransactions] = useState<
    { id: string; type: string; amount: number; timestamp: Date | null; offer_title?: string; description?: string; reference_id?: string; reference_type?: string; category?: string; balance_after?: number; source?: string }[]
  >([]);
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [addAmount, setAddAmount] = useState("");
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for success parameter in URL (from Stripe redirect)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');

    if (success === 'true') {
      alert('Payment successful! Your wallet has been updated.');
      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (canceled === 'true') {
      alert('Payment canceled.');
      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  // Function to fetch billing data
  const fetchBillingData = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();

      const [walletRes, txRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/transactions`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      if (!walletRes.ok) {
        throw new Error(`Failed to fetch wallet data: ${walletRes.status}`);
      }

      if (!txRes.ok) {
        throw new Error(`Failed to fetch transaction data: ${txRes.status}`);
      }

      const walletData = await walletRes.json();
      const txData = await txRes.json();

      // Define types for different timestamp formats
      type FirestoreTimestamp = { seconds: number; nanoseconds?: number };
      type AlternativeTimestamp = { _seconds: number };
      type TimestampFormat = string | FirestoreTimestamp | AlternativeTimestamp;

      // Type guard functions
      const isFirestoreTimestamp = (timestamp: unknown): timestamp is FirestoreTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && 'seconds' in timestamp;

      const isAlternativeTimestamp = (timestamp: unknown): timestamp is AlternativeTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && '_seconds' in timestamp;

      const normalizedTx = txData.transactions?.map((tx: { id: string; type: string; amount: number; timestamp: TimestampFormat; offer_title?: string }) => {
        // Handle different timestamp formats
        let parsedTimestamp = null;

        if (tx.timestamp) {
          if (typeof tx.timestamp === "string") {
            // ISO string format
            parsedTimestamp = new Date(tx.timestamp);
          } else if (typeof tx.timestamp === "object") {
            if (isFirestoreTimestamp(tx.timestamp)) {
              // Firestore timestamp format with seconds
              const seconds = typeof tx.timestamp.seconds === "number"
                ? tx.timestamp.seconds
                : parseInt(tx.timestamp.seconds);

              const nanoseconds = tx.timestamp.nanoseconds
                ? (typeof tx.timestamp.nanoseconds === "number"
                  ? tx.timestamp.nanoseconds
                  : parseInt(tx.timestamp.nanoseconds)) / 1000000
                : 0;

              parsedTimestamp = new Date(seconds * 1000 + nanoseconds);
            } else if (isAlternativeTimestamp(tx.timestamp)) {
              // Alternative Firestore format
              parsedTimestamp = new Date(tx.timestamp._seconds * 1000);
            }
          }
        }

        return {
          ...tx,
          timestamp: parsedTimestamp
        };
      });

      // Set all the data from the wallet collection
      setWalletData({
        total_available_balance: walletData.total_available_balance ?? 0,
        total_promo_available_balance: walletData.total_promo_available_balance ?? 0,
        total_promo_balance_spent: walletData.total_promo_balance_spent ?? 0,
        total_balance_spent: walletData.total_balance_spent ?? 0,
        total_budget_allocated: walletData.total_budget_allocated ?? 0
      });
      setTransactions(normalizedTx ?? []);
    } catch (err) {
      console.error("🔥 Error fetching billing data:", err);
      setError(err instanceof Error ? err.message : 'Failed to load billing data');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch billing data when user changes or after successful payment
  useEffect(() => {
    if (!user) return;
    fetchBillingData();
  }, [user, fetchBillingData]);

  const handleStripeCheckout = async () => {
    if (!user || !addAmount) {
      alert('Please enter a valid amount');
      return;
    }

    // Parse the amount and validate it
    const amount = parseFloat(addAmount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    // Make sure the amount has at most 2 decimal places
    const roundedAmount = Math.round(amount * 100) / 100;
    if (roundedAmount !== amount) {
      setAddAmount(roundedAmount.toString());
    }

    setIsRedirecting(true);

    try {
      // Convert dollar amount to cents for the API
      // Make sure to use Math.round to get integer values
      const creditAmountCents = Math.round(amount * 100);
      const stripeFee = Math.round((amount * 0.029 + 0.3) * 100);
      const totalAmountCents = Math.round(creditAmountCents + stripeFee); // Ensure this is an integer

      const token = await user.getIdToken();

      // Log the values we're sending to the API for debugging
      console.log('Sending to API:', {
        amount: Math.floor(totalAmountCents),
        credit_amount: Math.floor(creditAmountCents),
        amount_type: typeof Math.floor(totalAmountCents),
        credit_amount_type: typeof Math.floor(creditAmountCents)
      });

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/create-checkout-session`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          // Send amounts in cents to the API as integers
          amount: Math.floor(totalAmountCents), // Use Math.floor to ensure integer value
          credit_amount: Math.floor(creditAmountCents), // Use Math.floor to ensure integer value
        }),
      });

      if (!res.ok) {
        // Try to parse the error response
        try {
          const errorData = await res.json();
          console.error('API Error:', errorData);
          throw new Error(`Failed to create checkout session: ${errorData.detail || res.status}`);
        } catch {
          // If we can't parse the error response, just use the status code
          throw new Error(`Failed to create checkout session: ${res.status}`);
        }
      }

      const data = await res.json();

      if (data.session_url) {
        window.location.href = data.session_url;
      } else {
        throw new Error("Stripe session URL missing");
      }
    } catch (err) {
      console.error("❌ Stripe checkout error:", err);
      alert(err instanceof Error ? err.message : 'Failed to create checkout session');
      setIsRedirecting(false);
    }
  };

  // Format currency values
  // All monetary values are stored in cents in the database
  const formatCurrency = (value: number) => {
    // Convert from cents to dollars first, then format
    const dollars = centsToDollars(value);
    return formatCurrencyUtil(dollars);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Billing</h1>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={fetchBillingData}
            disabled={isLoading}
            className="rounded-full"
            title="Refresh"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={isLoading ? "animate-spin" : ""}
            >
              <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
              <path d="M3 3v5h5" />
              <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
              <path d="M16 21h5v-5" />
            </svg>
          </Button>
          <Button variant="outline" onClick={() => setShowAddFunds(true)}>
            <CreditCard className="w-4 h-4 mr-2" />
            Add Funds
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground text-sm">
        Track your offer budgets and spending activity.
      </p>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p>{error}</p>
          <Button
            variant="link"
            className="text-red-700 p-0 h-auto text-sm"
            onClick={fetchBillingData}
          >
            Try again
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Available Balance</CardTitle>
            <CreditCard className="w-5 h-5 text-purple-500" />
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Available balance is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_available_balance)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Total Spent</CardTitle>
            <CircleDollarSign className="w-5 h-5 text-red-500" />
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Total spent is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_balance_spent)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Budget Allocated</CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Budget allocated is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_budget_allocated)}
          </CardContent>
        </Card>
      </div>



      <Card>
        <CardHeader>
          <CardTitle className="text-base font-semibold text-gray-900">Transaction History</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">Loading transactions...</p>
            </div>
          ) : transactions.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              <p>No transactions yet</p>
              <p className="text-sm mt-1">Add funds to your wallet to get started</p>
            </div>
          ) : (
            <ul className="divide-y">
              {transactions.map((tx) => (
                <li key={tx.id} className="p-4 text-sm hover:bg-gray-50">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      {/* Legacy transaction types */}
                      {tx.type === "add_funds" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Added funds</span>
                        </>
                      )}
                      {tx.type === "recharge_offer" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/></svg>
                          </span>
                          <span>Recharged &quot;{tx.offer_title || 'Offer'}&quot;</span>
                        </>
                      )}
                      {tx.type === "spend" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Spent on campaign</span>
                        </>
                      )}

                      {/* New transaction types */}
                      {tx.type === "credit" && tx.category === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit added"}</span>
                        </>
                      )}
                      {tx.type === "debit" && tx.category === "budget_allocation" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2" /><line x1="2" x2="22" y1="10" y2="10" /></svg>
                          </span>
                          <span>
                            {tx.description || "Budget allocated"}
                            {tx.offer_title && <> for &quot;{tx.offer_title}&quot;</>}
                          </span>
                        </>
                      )}
                      {tx.type === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit"}</span>
                        </>
                      )}
                      {!tx.type && !tx.category && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-gray-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4M12 16h.01"/></svg>
                          </span>
                          <span>{tx.description || "Transaction"}</span>
                        </>
                      )}
                    </span>
                    <span className="font-medium text-right">
                      {/* Transaction amounts are stored in cents in the database, formatCurrency converts to dollars */}
                      {formatCurrency(tx.amount || 0)}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 ml-8">
                    {tx.timestamp
                      ? tx.timestamp.toLocaleString(undefined, {
                          dateStyle: "medium",
                          timeStyle: "short",
                        })
                      : "Just now"}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      {showAddFunds && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md space-y-4">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold">Add Funds to Wallet</h2>
              <button
                onClick={() => setShowAddFunds(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 6 6 18M6 6l12 12"/></svg>
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-blue-800">
              <p>Funds added to your wallet can be used to fund your offers and campaigns.</p>
            </div>

            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">Amount (USD)</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  id="amount"
                  type="number"
                  min="1"
                  step="0.01"
                  placeholder="0.00"
                  value={addAmount}
                  onChange={(e) => {
                    // Ensure we're getting a valid number
                    const value = e.target.value;
                    if (value === '' || !isNaN(parseFloat(value))) {
                      setAddAmount(value);
                    }
                  }}
                  className="w-full border px-7 py-2 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {parseFloat(addAmount) > 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                <div className="flex justify-between text-sm">
                  <span>Amount:</span>
                  <span>{formatCurrencyUtil(parseFloat(addAmount))}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-500">
                  <span>Processing fee:</span>
                  <span>{formatCurrencyUtil(parseFloat(addAmount) * 0.029 + 0.3)}</span>
                </div>
                <div className="border-t border-gray-200 mt-2 pt-2 flex justify-between font-medium">
                  <span>Total:</span>
                  <span>{formatCurrencyUtil(parseFloat(addAmount) + parseFloat(addAmount) * 0.029 + 0.3)}</span>
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  <span>Note: All amounts are stored in cents in the database</span>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2 pt-2">
              <Button variant="outline" onClick={() => setShowAddFunds(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleStripeCheckout}
                disabled={isRedirecting || !addAmount || parseFloat(addAmount) <= 0}
                className="min-w-[100px]"
              >
                {isRedirecting ? "Redirecting..." : "Checkout"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
