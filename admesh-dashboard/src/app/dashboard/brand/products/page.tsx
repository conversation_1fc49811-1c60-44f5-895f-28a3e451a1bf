"use client";

import {  useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Plus,
  Gift,
  Sparkles,
  Tag,
  Pencil,
  PlusCircle,
  Search,
  ArrowUpDown,
  Loader2,
  ArrowUpCircle,
} from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useSubscription } from "@/hooks/use-subscription";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  Toolt<PERSON>Trigger,
} from "@/components/ui/tooltip";
// Removed unused 'Tabs', 'TabsContent', 'TabsList', and 'TabsTrigger'

interface Product {
  id: string;
  title: string;
  description: string | null;
  status: "active" | "inactive";
  is_ai_powered: boolean;
  has_free_tier: boolean;
  keywords: string[];
  view_count: number;
  clicks: number;
  conversions: number;
  created_at: number;
  category?: string;
}

export default function ProductsPage() {
  const { user } = useAuth();
  const { plan, loading: loadingSubscription } = useSubscription();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  const [canAddMoreProducts, setCanAddMoreProducts] = useState(false);

  // Fetch products
  useEffect(() => {
    if (!user) return;

    const fetchProducts = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();

        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/brand/all`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!res.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await res.json();
        setProducts(data.products || []);
      } catch (err) {
        console.error("❌ Error fetching products from API:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user]);

  // Check if user has active offers
  useEffect(() => {
    if (!user) return;

    const checkActiveOffers = async () => {
      try {
        const token = await user.getIdToken();
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (res.ok) {
          const data = await res.json();
          const activeOffers = data.offers.filter((offer: { active: boolean }) => offer.active);
          setHasActiveOffer(activeOffers.length > 0);
        }
      } catch (err) {
        console.error("Error checking active offers:", err);
      }
    };

    checkActiveOffers();
  }, [user]);

  // Check if user can add more products based on subscription
  useEffect(() => {
    if (!user || loadingSubscription || loading) return;

    // Get product limit from subscription plan
    const productLimit = plan?.product_listings_limit || 1; // Default to 1 if plan not loaded
    const isUnlimited = productLimit === -1 || productLimit === 0;

    // Check if user has reached their product limit
    if (isUnlimited || products.length < productLimit) {
      setCanAddMoreProducts(true);
    } else {
      setCanAddMoreProducts(false);
    }
  }, [user, plan, products, loading, loadingSubscription]);

  // Get unique categories from products
  const categories = ["all", ...new Set(products.map(p => p.category).filter(Boolean))];

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = categoryFilter === "all" || product.category === categoryFilter;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      if (sortBy === "newest") {
        return (new Date(b.created_at).getTime()) - (new Date(a.created_at).getTime());
      } else if (sortBy === "oldest") {
        return (new Date(a.created_at).getTime()) - (new Date(b.created_at).getTime());
      } else if (sortBy === "alphabetical") {
        return a.title.localeCompare(b.title);
      }
      return 0;
    });

  return (
    <div className="space-y-8 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Your Products</h2>
          <p className="text-muted-foreground mt-1">
            Manage all tools you&apos;ve submitted to AdMesh.
          </p>
          {!loading && !loadingSubscription && (
            <div className="text-sm text-muted-foreground mt-1">
              {plan?.product_listings_limit === -1 || plan?.product_listings_limit === 0 ? (
                <span>Unlimited products allowed with your {plan.name} plan</span>
              ) : (
                <span>
                  {products.length} of {plan?.product_listings_limit || 1} products used
                  {products.length >= (plan?.product_listings_limit || 1) && (
                    <span className="text-red-500 ml-1">
                      (limit reached)
                    </span>
                  )}
                </span>
              )}
            </div>
          )}
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div>
                <Button
                  onClick={() => router.push("/dashboard/brand/products/new")}
                  size="lg"
                  className="shrink-0"
                  disabled={!canAddMoreProducts}
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Add Product
                </Button>
              </div>
            </TooltipTrigger>
            {!canAddMoreProducts && (
              <TooltipContent>
                <p>You&apos;ve reached your product limit. <br />Upgrade your subscription to add more products.</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products..."
              className="pl-10 w-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category || ""}>
                  {category === "all" ? "All Categories" : category || "Uncategorized"}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="shrink-0">
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy("newest")}>
                Newest First
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("oldest")}>
                Oldest First
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("alphabetical")}>
                Alphabetical
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : filteredProducts.length === 0 ? (
        <div className="bg-muted/40 border rounded-lg py-16 text-center">
          <div className="max-w-sm mx-auto">
            <h3 className="text-lg font-medium">No products found</h3>
            <p className="text-muted-foreground mt-2">
              {products.length === 0
                ? "Get started by adding your first product to AdMesh."
                : "Try adjusting your search or filters."}
            </p>
            {products.length === 0 && (
              canAddMoreProducts ? (
                <Button onClick={() => router.push("/dashboard/brand/products/new")} className="mt-4">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Product
                </Button>
              ) : (
                <Button onClick={() => router.push("/dashboard/brand/subscription")} className="mt-4">
                  <ArrowUpCircle className="w-4 h-4 mr-2" />
                  Upgrade to Add Products
                </Button>
              )
            )}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden bg-white hover:shadow-md transition-all border">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg font-semibold line-clamp-1">{product.title}</CardTitle>
                  <Badge variant={product.status === "active" ? "default" : "secondary"} className="text-xs">
                    {product.status === "active" ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {product.category || "Uncategorized"}
                </p>
              </CardHeader>

              <CardContent className="pb-2">
                {product.description && (
                  <p className="text-sm text-gray-600 line-clamp-2 h-10">
                    {product.description}
                  </p>
                )}

                <div className="flex flex-wrap gap-2 mt-3">
                  {product.is_ai_powered && (
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                      <Sparkles className="h-3 w-3 mr-1" />
                      AI Powered
                    </Badge>
                  )}

                  {product.has_free_tier && (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <Gift className="h-3 w-3 mr-1" />
                      Free Tier
                    </Badge>
                  )}

                  {product.keywords?.slice(0, 2).map((keyword, i) => (
                    <Badge key={i} variant="outline" className="text-gray-600 border-gray-200">
                      <Tag className="h-3 w-3 mr-1" />
                      {keyword}
                    </Badge>
                  ))}

                  {product.keywords?.length > 2 && (
                    <Badge variant="outline" className="text-gray-600 border-gray-200">
                      +{product.keywords.length - 2} more
                    </Badge>
                  )}
                </div>

                {/* <div className="flex items-center gap-4 mt-4">
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium text-gray-900">{product.view_count || 0}</span> views
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium text-gray-900">{product.clicks || 0}</span> clicks
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <span className="font-medium text-gray-900">{product.conversions || 0}</span> conversions
                  </div>
                </div> */}
              </CardContent>

              <CardFooter className="border-t bg-gray-50 pt-4">
                <div className="flex gap-2 w-full">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => router.push(`/dashboard/brand/products/${product.id}/edit`)}
                  >
                    <Pencil className="h-4 w-4 mr-1" />
                    Edit
                  </Button>

                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      if (hasActiveOffer) {
                        toast.error("You can only have one active offer at a time. Please deactivate your existing offer before creating a new one.");
                      } else {
                        router.push(`/dashboard/brand/offers/new?productId=${product.id}`);
                      }
                    }}
                  >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    Create Offer
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

    </div>
  );
}