"use client";

import { useState, Suspense } from "react";
import { useRouter } from "next/navigation";
import BrandContent from "@/components/BrandContent";
import FloatingRoleSwitcher from "@/components/FloatingRoleSwitcher";
import Footer from "@/components/Footer";
import { AnimatePresence, motion } from "framer-motion";

type Role = "user" | "brand" | "agent" | null;

export default function Home() {
  // Set the default role to "brand" so it shows BrandContent by default
  const [selectedRole, setSelectedRole] = useState<Role>("brand");
  const router = useRouter();

  // Handle role selection
  const handleRoleSelect = (role: Role) => {
    if (role === "agent") {
      router.push("/agents");
      return;
    } else if (role === "user") {
      router.push("/users");
      return;
    } else if (role === "brand") {
      // Stay on root page for brands content
      setSelectedRole("brand");
      // Scroll to top when role is selected
      window.scrollTo({ top: 0, behavior: "smooth" });
      return;
    }

    setSelectedRole(role);
    // Scroll to top when role is selected
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <div className="min-h-screen flex flex-col">
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedRole}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-grow"
        >
          <Suspense fallback={<div>Loading...</div>}>
            <BrandContent onRoleChange={handleRoleSelect} />
          </Suspense>
        </motion.div>
      </AnimatePresence>

      {/* Floating Role Switcher - always show on the homepage */}
      <FloatingRoleSwitcher
        currentRole={selectedRole}
        onRoleChange={handleRoleSelect}
      />

      <Footer />
    </div>
  );
}
