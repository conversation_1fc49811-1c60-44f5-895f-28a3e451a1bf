"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";

export default function TestConversionPage() {
  const [clickId, setClickId] = useState("");
  const [isAdmesh, setIsAdmesh] = useState(false);
  // Track if this is a test conversion
  const [isTest, setIsTest] = useState(false);
  const [clickVerified, setClickVerified] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [pixelFired, setPixelFired] = useState(false);
  const [forceFireChecked, setForceFireChecked] = useState(false);

  const [status, setStatus] = useState<{
    message: string;
    type: "success" | "error" | "info";
  } | null>(null);

  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.useadmesh.com";

  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);

      // Support both old and new parameter formats
      const clickParam = params.get("utm_click_id") || params.get("clickId") || params.get("click_id");
      if (clickParam) {
        setClickId(clickParam);
        verifyClick(clickParam);
      } else {
        setStatus({
          message: "No click ID found. Please make sure you are redirected from a valid recommendation.",
          type: "error",
        });
      }

      // Support both old and new parameter formats
      if (params.get("utm_source") === "admesh" || params.get("admesh") === "true") setIsAdmesh(true);
      if (params.get("test") === "true") setIsTest(true);
    }
  }, []);

  // Verify the click ID - in a real implementation, this would check with the backend
  // For test purposes, we just simulate a delay and always succeed
  const verifyClick = async (clickIdToVerify: string) => {
    console.log(`Verifying click ID: ${clickIdToVerify}`);
    setIsVerifying(true);
    try {
      await new Promise((r) => setTimeout(r, 1000)); // simulate delay
      setClickVerified(true);
      setStatus({
        message: "Click verified successfully. You can now fire the conversion pixel.",
        type: "success",
      });
    } catch {
      setStatus({
        message: "Click verification failed. Try again or use force fire.",
        type: "error",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const fireConversion = async () => {
    if (!clickId) {
      setStatus({ message: "Click ID is required", type: "error" });
      return;
    }

    if (!isAdmesh && !forceFireChecked) {
      setStatus({
        message: "Conversion not fired: admesh=true param missing. Use force fire to override.",
        type: "error",
      });
      return;
    }

    if (!clickVerified) {
      if (forceFireChecked) {
        // If force fire is checked, we'll proceed without verification
        console.log("Force fire enabled, skipping verification");
      } else {
        // Try to verify the click ID first
        await verifyClick(clickId);

        // If verification failed, show error and return
        if (!clickVerified) {
          setStatus({
            message: "Conversion not fired: click not verified. Use force fire to override.",
            type: "error",
          });
          return;
        }
      }
    }

    try {
      setStatus({ message: "Firing pixel...", type: "info" });

      // Use the isTest state to determine if this is a test conversion
      const pixelUrl = `${apiBaseUrl}/conversion/pixel?utm_click_id=${clickId}&test=${isTest ? 'true' : 'false'}&verified=true`;

      // Use fetch call for test conversions
      const response = await fetch(pixelUrl);
      if (!response.ok) throw new Error("Pixel tracking failed");

      // For demonstration purposes, also show how a pixel would be used in production
      // but don't actually load it to avoid duplicate conversions
      console.log("In production, a pixel image would be used like this:");
      console.log(`<img src="${pixelUrl}" width="1" height="1" style="display:none" />`);


      setPixelFired(true);
      setStatus({
        message: "Conversion pixel fired successfully!",
        type: "success",
      });
    } catch (error: Error | unknown) {
      setStatus({
        message: `Error firing pixel: ${error instanceof Error ? error.message : 'Unknown error'}`,
        type: "error",
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl text-primary">AdMesh Conversion Test</CardTitle>
          <CardDescription>Simulate and verify a conversion event using click ID.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col gap-4">
            <Input
              value={clickId}
              onChange={(e) => setClickId(e.target.value)}
              placeholder="Enter Click ID"
              className="max-w-md"
            />
            <Button onClick={fireConversion} disabled={isVerifying}>
              {isVerifying ? "Verifying..." : "Fire Conversion Pixel"}
            </Button>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="force-fire"
                checked={forceFireChecked}
                onChange={(e) => setForceFireChecked(e.target.checked)}
              />
              <label htmlFor="force-fire" className="text-sm text-muted-foreground">
                Force fire conversion (override safety checks)
              </label>
            </div>
          </div>

          {clickId && (
            <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-100">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${clickVerified ? "bg-green-500" : "bg-amber-500"}`} />
                <span className="text-sm font-medium">Click ID: <span className="font-mono">{clickId}</span></span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {clickVerified ? "✅ Verified" : isVerifying ? "⏳ Verifying..." : "⚠️ Not verified"}
              </p>
            </div>
          )}

          {status && (
            <Alert variant={status.type === "success" ? "default" : "destructive"}>
              {status.type === "success" ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{status.type.charAt(0).toUpperCase() + status.type.slice(1)}</AlertTitle>
              <AlertDescription>{status.message}</AlertDescription>
            </Alert>
          )}

          {pixelFired && (
            <div className="bg-muted p-4 rounded-md text-xs">
              <h4 className="font-medium mb-2">Pixel Fired Using:</h4>
              <ul className="space-y-1">
                <li><strong>GET:</strong> {`${apiBaseUrl}/conversion/pixel?utm_click_id=${clickId}&test=${isTest ? 'true' : 'false'}&verified=true`}</li>
                <li className="text-muted-foreground"><em>Note: In production, this would be loaded as a 1x1 pixel image</em></li>
                <li className="mt-2 text-muted-foreground"><strong>Alternative method:</strong> POST to `/conversion/log` with same utm_click_id</li>
              </ul>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">Only use click IDs generated from AdMesh click flow.</p>
        </CardFooter>
      </Card>
    </div>
  );
}
