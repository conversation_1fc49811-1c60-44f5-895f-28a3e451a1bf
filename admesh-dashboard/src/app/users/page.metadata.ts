import { Metadata } from "next";
import { generateMetadata } from "../metadata";

// Users page specific metadata
export const metadata: Metadata = generateMetadata(
  "Your Personal Agent That Discovers and Earns For You",
  "Every query you ask grows your personal agent. It finds products, unlocks offers, and earns you rewards over time. Join the Agent Pioneer Program and unlock exclusive benefits.",
  "/users",
  undefined // Let the generateMetadata function create the static OG image URL
);
