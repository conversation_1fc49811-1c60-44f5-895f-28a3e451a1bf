// Split into two files: layout.tsx (server component) and ClientLayout.tsx (client component)
// This is the server component that handles metadata
import "./globals.css";
import { Metadata } from "next";
import { metadata as siteMetadata } from "./metadata";
import ClientLayout from "@/components/ClientLayout";

// Export metadata for the root layout
export const metadata: Metadata = {
  ...siteMetadata,
  // Additional metadata can be added here if needed
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Favicon - comprehensive setup for all browsers and devices */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16.png" />
        <link rel="shortcut icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/favicon.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="theme-color" content="#000000" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <meta name="apple-mobile-web-app-title" content="AdMesh" />
        {/* Canonical URL will be set by individual page metadata */}

        {/* Schema.org JSON-LD structured data - Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "AdMesh",
              url: "https://www.useadmesh.com",
              logo: "https://www.useadmesh.com/logo.svg",
              sameAs: [
                "https://twitter.com/useadmesh",
                "https://linkedin.com/company/useadmesh"
              ],
              description: siteMetadata.description
            })
          }}
        />

        {/* Schema.org JSON-LD structured data - WebSite with Sitelinks */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              name: "AdMesh",
              alternateName: ["AdMesh - Promote your products inside AI", "useadmesh", "admesh"],
              url: "https://www.useadmesh.com",
              description: siteMetadata.description,
              publisher: {
                "@type": "Organization",
                name: "AdMesh",
                alternateName: ["useadmesh", "admesh"],
                url: "https://www.useadmesh.com",
                logo: {
                  "@type": "ImageObject",
                  url: "https://www.useadmesh.com/logo.svg",
                  width: 200,
                  height: 200
                },
                sameAs: [
                  "https://twitter.com/useadmesh",
                  "https://github.com/GouniManikumar12/admesh-typescript",
                  "https://github.com/GouniManikumar12/admesh-python"
                ],
                contactPoint: {
                  "@type": "ContactPoint",
                  contactType: "customer service"
                }
              },
              potentialAction: {
                "@type": "SearchAction",
                target: {
                  "@type": "EntryPoint",
                  urlTemplate: "https://www.useadmesh.com/search?q={search_term_string}"
                },
                "query-input": "required name=search_term_string"
              },
              mainEntity: {
                "@type": "SoftwareApplication",
                name: "AdMesh",
                applicationCategory: "BusinessApplication",
                operatingSystem: "Web",
                description: "AI-powered product placement platform that connects brands with users through AI agents",
                offers: {
                  "@type": "Offer",
                  price: "0",
                  priceCurrency: "USD",
                  description: "Free to get started"
                }
              }
            })
          }}
        />

        {/* Schema.org JSON-LD structured data - SiteNavigationElement */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "ItemList",
              itemListElement: [
                {
                  "@type": "SiteNavigationElement",
                  position: 1,
                  name: "For Brands",
                  description: "Connect with high-intent users looking for your products",
                  url: "https://www.useadmesh.com/"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 2,
                  name: "For Agents",
                  description: "Join the AdMesh Agent Pioneer Program",
                  url: "https://www.useadmesh.com/agents"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 3,
                  name: "For Users",
                  description: "Discover AI tools and earn rewards",
                  url: "https://www.useadmesh.com/users"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 4,
                  name: "Pricing",
                  description: "AdMesh pricing plans for brands",
                  url: "https://www.useadmesh.com/#pricing"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 5,
                  name: "Benefits",
                  description: "Benefits of using AdMesh for your brand",
                  url: "https://www.useadmesh.com/#benefits"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 6,
                  name: "Pioneer Program",
                  description: "Join the AdMesh Agent Pioneer Program",
                  url: "https://www.useadmesh.com/agents#pioneer-program"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 7,
                  name: "Terms",
                  description: "AdMesh Terms of Service",
                  url: "https://www.useadmesh.com/terms"
                },
                {
                  "@type": "SiteNavigationElement",
                  position: 8,
                  name: "Privacy",
                  description: "AdMesh Privacy Policy",
                  url: "https://www.useadmesh.com/privacy"
                }
              ]
            })
          }}
        />

        {/* Schema.org JSON-LD structured data - BreadcrumbList */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  name: "Home",
                  item: "https://www.useadmesh.com"
                },
                {
                  "@type": "ListItem",
                  position: 2,
                  name: "For Brands",
                  item: "https://www.useadmesh.com/"
                },
                {
                  "@type": "ListItem",
                  position: 3,
                  name: "For Agents",
                  item: "https://www.useadmesh.com/agents"
                },
                {
                  "@type": "ListItem",
                  position: 4,
                  name: "For Users",
                  item: "https://www.useadmesh.com/users"
                },
                {
                  "@type": "ListItem",
                  position: 5,
                  name: "Pricing",
                  item: "https://www.useadmesh.com/#pricing"
                },
                {
                  "@type": "ListItem",
                  position: 6,
                  name: "Benefits",
                  item: "https://www.useadmesh.com/#benefits"
                },
                {
                  "@type": "ListItem",
                  position: 7,
                  name: "Pioneer Program",
                  item: "https://www.useadmesh.com/agents#pioneer-program"
                },
                {
                  "@type": "ListItem",
                  position: 8,
                  name: "Join Now",
                  item: "https://www.useadmesh.com/brands#cta"
                },
                {
                  "@type": "ListItem",
                  position: 9,
                  name: "Join Now",
                  item: "https://www.useadmesh.com/agents#pioneer-program"
                },
                {
                  "@type": "ListItem",
                  position: 10,
                  name: "Join Now",
                  item: "https://www.useadmesh.com/users#cta-section"
                }
              ]
            })
          }}
        />

        {/* Schema.org JSON-LD structured data - Organization for brand recognition */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "@id": "https://www.useadmesh.com/#organization",
              name: "AdMesh",
              alternateName: ["useadmesh", "admesh", "AdMesh Platform", "AdMesh AI"],
              url: "https://www.useadmesh.com",
              logo: {
                "@type": "ImageObject",
                url: "https://www.useadmesh.com/logo.svg",
                width: 200,
                height: 200
              },
              description: "AdMesh is an AI-powered product placement platform that connects brands with users through AI agents. Official website for AdMesh, useadmesh, and admesh services.",
              foundingDate: "2024",
              industry: "Artificial Intelligence",
              keywords: ["AI advertising", "product placement", "AI agents", "AdMesh", "useadmesh", "admesh", "AI tools", "monetized intent"],
              sameAs: [
                "https://twitter.com/useadmesh",
                "https://github.com/GouniManikumar12/admesh-typescript",
                "https://github.com/GouniManikumar12/admesh-python"
              ],
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "customer service",
                availableLanguage: "English"
              },
              offers: [
                {
                  "@type": "Offer",
                  name: "AdMesh for Brands",
                  description: "Connect with high-intent users through AI agents",
                  url: "https://www.useadmesh.com/brands",
                  price: "0",
                  priceCurrency: "USD"
                },
                {
                  "@type": "Offer",
                  name: "AdMesh for Agents",
                  description: "Join the Agent Pioneer Program and earn rewards",
                  url: "https://www.useadmesh.com/agents",
                  price: "0",
                  priceCurrency: "USD"
                }
              ],
              brand: {
                "@type": "Brand",
                name: "AdMesh",
                alternateName: ["useadmesh", "admesh"]
              }
            })
          }}
        />
      </head>
      <body suppressHydrationWarning={true}>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
