"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { Menu, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { User } from "firebase/auth";

interface NavItem {
  label: string;
  sectionId: string;
}

interface MobileNavProps {
  items: NavItem[];
  activeSection: string;
  scrollToSection: (sectionId: string) => void;
  onGetStarted: () => void;
  themeColor: "blue" | "purple" | "green" | "black";
  user?: User | null;
}

export default function MobileNav({
  items,
  activeSection,
  scrollToSection,
  onGetStarted,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  themeColor,
  user,
}: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Track scroll position for showing/hiding the nav
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.body.offsetHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleNavItemClick = (sectionId: string) => {
    scrollToSection(sectionId);
    setIsOpen(false);
  };

  // Using black and white theme regardless of themeColor prop

  return (
    <>
      {/* Desktop Navigation */}
      <motion.nav
        initial={{ y: -100 }}
        animate={{ y: scrollProgress > 5 ? 0 : -100 }}
        transition={{ duration: 0.3 }}
        className="fixed top-0 left-0 right-0 z-50 hidden md:flex justify-center py-4 backdrop-blur-md bg-background/80 shadow-sm border-b border-border"
      >
        <div className="container mx-auto flex items-center justify-between px-4 sm:px-6">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image src="/logo.svg" alt="AdMesh Logo" width={48} height={48} />
            <span className="text-lg font-bold tracking-tight font-inter text-foreground">AdMesh</span>
          </Link>

          {/* Navigation Items */}
          <div className="flex gap-8 items-center">
            {items.map((item) => (
              <button
                key={item.sectionId}
                onClick={() => scrollToSection(item.sectionId)}
                className={`text-sm font-medium hover:text-foreground transition-colors ${
                  activeSection === item.sectionId ? "text-foreground" : "text-muted-foreground"
                }`}
              >
                {item.label}
              </button>
            ))}
            {user ? (
              <Link href="/dashboard">
                <Button
                  size="sm"
                  className="bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  Dashboard
                </Button>
              </Link>
            ) : (
              <Button
                size="sm"
                className="bg-primary hover:bg-primary/90 text-primary-foreground"
                onClick={onGetStarted}
              >
                Get Started
              </Button>
            )}
          </div>
        </div>
      </motion.nav>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        {/* Mobile Nav Toggle Button */}
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{
            opacity: scrollProgress > 5 ? 1 : 0,
            scale: scrollProgress > 5 ? 1 : 0.8,
            pointerEvents: scrollProgress > 5 ? "auto" : "none"
          }}
          transition={{ duration: 0.2 }}
          onClick={toggleMenu}
          className="fixed top-4 right-4 z-50 w-10 h-10 rounded-full bg-card shadow-md flex items-center justify-center border border-border"
        >
          {isOpen ? <X size={20} className="text-foreground" /> : <Menu size={20} className="text-foreground" />}
        </motion.button>

        {/* Mobile Menu Overlay */}
        {isOpen && (
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={() => setIsOpen(false)}
          />
        )}

        {/* Mobile Menu */}
        {isOpen && (
          <div
            className="fixed top-0 right-0 bottom-0 w-3/4 max-w-xs bg-card z-50 shadow-xl flex flex-col"
          >
              <div className="flex items-center justify-between p-4 border-b border-border">
                {/* Logo */}
                <Link href="/" className="flex items-center">
                  <Image src="/logo.svg" alt="AdMesh Logo" width={36} height={36} />
                  <span className="text-lg font-bold tracking-tight font-inter text-foreground">AdMesh</span>
                </Link>
                <button onClick={toggleMenu} className="text-foreground">
                  <X size={24} />
                </button>
              </div>

              <div className="flex flex-col p-4 gap-4">
                {items.map((item) => (
                  <button
                    key={item.sectionId}
                    onClick={() => handleNavItemClick(item.sectionId)}
                    className={`py-3 px-4 text-left rounded-md ${
                      activeSection === item.sectionId
                        ? "bg-muted text-foreground font-medium"
                        : "text-muted-foreground hover:bg-muted/50"
                    }`}
                  >
                    {item.label}
                  </button>
                ))}
                <div className="mt-4 pt-4 border-t border-border">
                  {user ? (
                    <Link href="/dashboard" onClick={() => setIsOpen(false)}>
                      <Button
                        className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                      >
                        Dashboard
                      </Button>
                    </Link>
                  ) : (
                    <Button
                      className="w-full bg-primary hover:bg-primary/90 text-primary-foreground"
                      onClick={() => {
                        onGetStarted();
                        setIsOpen(false);
                      }}
                    >
                      Get Started
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
      </div>
    </>
  );
}
