"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2 } from "lucide-react";
import { toast } from "sonner";

interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  price_annual_cents?: number;
  billing_cycle: "monthly" | "annual" | "free";
  product_listings_limit: number;
  active_offers_per_product_limit: number;
  promo_credit_cents: number;
  visibility_boost: string;
  analytics_level: string;
  conversion_reports: string;
  agent_match_priority: string;
  badge_type?: string;
  support_level: string;
  agent_outreach_tools: boolean;
  multi_user_access: boolean;
  multi_user_limit: number;
  cpa_optimization: boolean;
  features: string[];
}

interface Subscription {
  plan_id: string;
  billing_cycle: "monthly" | "annual" | "free";
  status: string;
  stripe_subscription_id?: string;
  cancel_at_period_end?: boolean;
  current_period_end?: string;
}

// No props needed for this component
export default function SubscriptionPlans() {
  const { user } = useAuth();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [filteredPlans, setFilteredPlans] = useState<Plan[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  // Removed upgrading state since we're disabling the upgrade functionality
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");

  // Fetch plans and current subscription
  useEffect(() => {
    const fetchPlans = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const token = await user.getIdToken();

        // Fetch plans
        const plansRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription/plans`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!plansRes.ok) {
          throw new Error("Failed to fetch subscription plans");
        }

        const plansData = await plansRes.json();
        setPlans(plansData.plans);

        // Fetch current subscription
        const subRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!subRes.ok) {
          throw new Error("Failed to fetch subscription data");
        }

        const subData = await subRes.json();
        setSubscription(subData.subscription);

        // Set initial billing cycle based on current subscription
        if (subData.subscription?.billing_cycle === "annual") {
          setBillingCycle("annual");
        }

      } catch (error) {
        console.error("Error fetching subscription data:", error);
        toast.error("Failed to load subscription information");
      } finally {
        setLoading(false);
      }
    };

    fetchPlans();
  }, [user]);

  // Filter plans based on billing cycle
  useEffect(() => {
    if (plans.length === 0) return;

    // Always include free plan
    const freePlan = plans.find(p => p.id === "free");

    // Filter paid plans based on billing cycle
    const paidPlans = plans.filter(p =>
      p.id !== "free" &&
      (p.billing_cycle === billingCycle ||
       (p.id.includes("_annual") && billingCycle === "annual") ||
       (!p.id.includes("_annual") && billingCycle === "monthly"))
    );

    // Sort plans: free first, then starter, then growth
    const sortedPlans = [];
    if (freePlan) sortedPlans.push(freePlan);

    // Add starter plan
    const starterPlan = paidPlans.find(p =>
      billingCycle === "monthly" ? p.id === "starter" : p.id === "starter_annual"
    );
    if (starterPlan) sortedPlans.push(starterPlan);

    // Add growth plan
    const growthPlan = paidPlans.find(p =>
      billingCycle === "monthly" ? p.id === "growth" : p.id === "growth_annual"
    );
    if (growthPlan) sortedPlans.push(growthPlan);

    setFilteredPlans(sortedPlans);
  }, [plans, billingCycle]);

  // Handle plan upgrade - commented out since we're using "Coming Soon" button
  /*
  const handleUpgrade = async (planId: string) => {
    if (!user) return;

    // Don't allow upgrading to current plan
    if (subscription && subscription.plan_id === planId) {
      toast.info("You are already on this plan");
      return;
    }

    // Free plan doesn't need checkout
    if (planId === "free") {
      toast.info("Please contact support to downgrade to the free plan");
      return;
    }

    try {
      // Upgrade logic removed as we're using "Coming Soon" button
      const token = await user.getIdToken();

      // Extract the base plan ID without the annual suffix
      const basePlanId = planId.replace("_annual", "");

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription/create-checkout`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({
          plan_id: basePlanId,
          billing_cycle: billingCycle
        })
      });

      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }

      const data = await response.json();

      if (data.session_url) {
        // Redirect to Stripe checkout
        window.location.href = data.session_url;
      } else {
        throw new Error("No checkout URL returned");
      }

    } catch (error) {
      console.error("Error upgrading plan:", error);
      toast.error("Failed to upgrade plan");
    }
  };
  */

  // Format price for display
  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`;
  };

  // Format product limit for display
  const formatLimit = (limit: number) => {
    return limit === 0 || limit === -1 ? "Unlimited" : limit.toString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Billing Cycle Toggle */}
      <div className="flex justify-center mb-8">
        <div className="inline-flex items-center p-1 bg-muted rounded-lg">
          <button
            className={`px-4 py-2 text-sm rounded-md transition-colors ${
              billingCycle === "monthly" ? "bg-background shadow-sm" : "text-muted-foreground"
            }`}
            onClick={() => setBillingCycle("monthly")}
          >
            Monthly
          </button>
          <button
            className={`px-4 py-2 text-sm rounded-md transition-colors ${
              billingCycle === "annual" ? "bg-background shadow-sm" : "text-muted-foreground"
            }`}
            onClick={() => setBillingCycle("annual")}
          >
            Annual <span className="text-xs text-green-500 font-medium">Save 20%</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {filteredPlans.map((plan) => {
          const isCurrentPlan = subscription?.plan_id === plan.id;
          const isCanceled = isCurrentPlan && subscription?.cancel_at_period_end;
          const isAnnual = plan.billing_cycle === "annual";

          // Calculate price display
          let priceDisplay;
          if (plan.price_monthly_cents === 0) {
            priceDisplay = "Free";
          } else if (isAnnual && plan.price_annual_cents) {
            // Annual price is for the whole year, so divide by 12 for monthly equivalent
            const monthlyEquivalent = plan.price_annual_cents / 12;
            priceDisplay = `${formatPrice(monthlyEquivalent)}/month`;
          } else {
            priceDisplay = `${formatPrice(plan.price_monthly_cents)}/month`;
          }

          return (
            <Card
              key={plan.id}
              className={`flex flex-col ${isCurrentPlan ? 'border-primary ring-1 ring-primary' : ''}`}
            >
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription className="mt-1">
                      {priceDisplay}
                      {isAnnual && (
                        <span className="block text-xs text-green-500">
                          Billed annually ({formatPrice(plan.price_annual_cents || 0)})
                        </span>
                      )}
                    </CardDescription>
                  </div>
                  {isCurrentPlan && (
                    <Badge variant="outline" className="bg-primary/10 text-primary">
                      Current Plan
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">Products:</div>
                    <div>{formatLimit(plan.product_listings_limit)}</div>

                    <div className="font-medium">Offers per product:</div>
                    <div>{formatLimit(plan.active_offers_per_product_limit)}</div>

                    <div className="font-medium">Promo credit:</div>
                    <div>{formatPrice(plan.promo_credit_cents)} one-time</div>

                    <div className="font-medium">Visibility:</div>
                    <div>{plan.visibility_boost}</div>

                    <div className="font-medium">Analytics:</div>
                    <div>{plan.analytics_level}</div>
                  </div>

                  <div className="pt-4">
                    <h4 className="text-sm font-medium mb-2">Features:</h4>
                    <ul className="space-y-2">
                      {plan.features.map((feature, i) => (
                        <li key={i} className="flex items-start text-sm">
                          <Check className="h-4 w-4 mr-2 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                {isCurrentPlan ? (
                  isCanceled ? (
                    <Button
                      variant="outline"
                      className="w-full"
                      disabled
                    >
                      Cancels on {new Date(subscription?.current_period_end || "").toLocaleDateString()}
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      className="w-full"
                      disabled
                    >
                      Current Plan
                    </Button>
                  )
                ) : (
                  <Button
                    className="w-full"
                    disabled={true}
                  >
                    Coming Soon
                  </Button>
                )}
              </CardFooter>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
