"use client";

import { useEffect, useState, useRef } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { RotateCcw, SendHorizontal, LockKeyhole, ExternalLink, Loader2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { auth } from "@/lib/firebase";
import { onAuthStateChanged, User } from "firebase/auth";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import AuthModal from "@/components/AuthModal";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import Image from "next/image";

const fallbackSuggestions = [
  "Best platforms for freelancers",
  "Compare CRM tools for startups",
  "Top free project management tools",
  "Marketing automation tools for small businesses",
];

export default function ChatBox() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [authOpen, setAuthOpen] = useState(false);
  const [fullscreenMessage, setFullscreenMessage] = useState<{ text: string; role: string } | null>(null);
  const [tempUserId] = useState(() => `user_${Math.random().toString(36).slice(2)}`);
  const [input, setInput] = useState("");
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [messages, setMessages] = useState<{ role: "user" | "bot"; text: string; recommendations?: {
    title: string;
    admesh_link?: string;
    url?: string;
    reason?: string;
    pricing?: string;
    has_free_tier?: boolean;
    trial_days?: number;
    is_ai_powered?: boolean;
    is_open_source?: boolean;
  }[] }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const [hasRedirected, setHasRedirected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const isLoggedIn = !!user && !user.isAnonymous;

  useEffect(() => {
    const unsub = onAuthStateChanged(auth, (u) => setUser(u));
    return () => unsub();
  }, []);

  useEffect(() => {
    fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/suggest`)
      .then((res) => res.json())
      .then((data) => setSuggestions(Array.isArray(data.suggestions) ? data.suggestions : fallbackSuggestions))
      .catch(() => setSuggestions(fallbackSuggestions));
  }, []);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth", block: "nearest" });
  }, [messages]);

  const handleSend = async (msg?: string) => {
    const query = (msg || input).trim();
    if (!query) return;

    setMessages((prev) => [...prev, { role: "user", text: query }]);
    setInput("");
    setIsLoading(true);

    try {
      if (isLoggedIn && !hasRedirected) {
        setHasRedirected(true);
        localStorage.setItem("pendingQuery", query);
        router.push("/dashboard");
        return;
      }
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/openrouter/recommend`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          query,
          user_id: isLoggedIn ? user?.uid : tempUserId,
          agent_id: "ZAinjf9SuPYCk7u8r0ZEeKY2fV42",
        }),
      });

      const json = await res.json();
      const reply = json?.response?.summary || "No response received.";
      const recommendations = json?.response?.recommendations || [];

      setMessages((prev) => [...prev, { role: "bot", text: reply, recommendations }]);

    } catch {
      setMessages((prev) => [...prev, { role: "bot", text: "Something went wrong." }]);
    } finally {
      setIsLoading(false);
    }
  };

  const reset = () => {
    setMessages([]);
    setInput("");
  };

  return (
    <>
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />
      <Dialog open={!!fullscreenMessage} onOpenChange={() => setFullscreenMessage(null)}>
        <DialogContent className="max-w-3xl">
          <div className="whitespace-pre-wrap text-base text-gray-800 dark:text-gray-100">
            {fullscreenMessage?.text}
          </div>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col h-full rounded-xl shadow-lg overflow-hidden mt-4">

        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
          <div className="flex items-center">
            <Image src="/logo.svg" alt="Logo" width={20} height={20} />
            <span className="font-bold text-md font-inter ml-2">AdMesh Agent</span>
          </div>
          {messages.length > 0 && (
            <Button
              onClick={reset}
              variant="ghost"
              size="sm"
              className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 flex items-center gap-1"
            >
              <RotateCcw className="h-4 w-4" /> New Training Session
            </Button>
          )}
        </div>

        {/* Chat Area */}
        <div className="flex-1 overflow-y-auto px-6 py-6 space-y-6 bg-white">
          <AnimatePresence>
            {messages.length === 0 ? (
              <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center space-y-6 px-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <Image src="/logo.svg" alt="Logo" width={60} height={60} />
                </div>
                <h3 className="text-lg font-bold">Train Your Personal Agent</h3>
                <p className="text-sm text-gray-500 max-w-md mx-auto">
                  Ask real-world queries.
                  Every interaction grows your agent and unlocks future offers, rewards, and earnings.
                </p>
                <div className="flex flex-wrap justify-center gap-2 mt-4">
                  {suggestions.map((q, i) => (
                    <motion.button
                      key={i}
                      whileHover={{ scale: 1.03 }}
                      onClick={() => {
                        handleSend(q);
                        setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: "smooth", block: "nearest" }), 100);
                      }}
                      className="px-3 py-1 text-sm bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition shadow-sm"
                    >
                      {q}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            ) : (
              messages.map((m, i) => (
                <motion.div key={i} initial={{ opacity: 0 }} animate={{ opacity: 1 }} className={cn("self-start", m.role === "user" ? "ml-auto text-right" : "")}>
                  <div
                    className={cn(
                      "relative inline-block px-5 py-4 rounded-2xl whitespace-pre-wrap break-words",
                      m.role === "user"
                        ? "bg-black text-white"
                        : "bg-white border border-gray-100 shadow-sm"
                    )}
                  >
                    <div className={cn("text-sm", m.role === "user" ? "text-white" : "text-gray-800")}>
                      {m.text}
                    </div>

                    {Array.isArray(m.recommendations) && m.recommendations.length > 0 && (
                      <div className="mt-5 space-y-4 text-left">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">
                          Opportunities Discovered by Your Agent
                        </h4>
                        {m.recommendations.map((rec, index) => {
                          const showFull = index === 0 || isLoggedIn;
                          if (!showFull && index > 0) return null;

                          return (
                            <div key={index} className="border p-4 rounded-xl bg-white shadow-sm hover:shadow-md transition">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-black">{rec.title}</h4>
                                <a
                                  href={rec.admesh_link || rec.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-xs font-medium text-black flex items-center hover:underline"
                                >
                                  Visit <ExternalLink className="h-3 w-3 ml-1" />
                                </a>
                              </div>
                              <p className="text-sm text-gray-600">{rec.reason}</p>
                            </div>
                          );
                        })}

                        {!isLoggedIn && (m.recommendations ?? []).length > 1 && (
                          <div className="p-4 rounded-xl bg-gray-50 border border-dashed border-gray-200 text-center mt-4">
                            <LockKeyhole className="h-5 w-5 mx-auto text-gray-400 mb-2" />
                            <p className="text-sm text-gray-500 mb-2">
                              Unlock full discovery power.
                              Train your agent and start earning rewards.
                            </p>
                            <Button onClick={() => setAuthOpen(true)} size="sm" className="bg-black text-white rounded-full px-4 text-xs">
                              Sign up to Unlock Your Agent&apos;s Rewards
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              ))
            )}
          </AnimatePresence>

          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mt-3 text-sm text-gray-500 flex items-center gap-2"
            >
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 bg-black rounded-full animate-pulse" />
                <div className="h-2 w-2 bg-black rounded-full animate-pulse delay-150" />
                <div className="h-2 w-2 bg-black rounded-full animate-pulse delay-300" />
              </div>
              <span>Your agent is exploring opportunities for you...</span>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="px-6 py-4 bg-white border-t border-gray-100">
          <form onSubmit={(e) => { e.preventDefault(); handleSend(); }} className="flex items-end gap-3">
            <div className="flex-1 relative">
              <Textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Train your agent — ask a real-world query..."
                className="resize-none min-h-[52px] max-h-[120px] text-sm px-4 py-3 rounded-xl border-gray-200 focus:ring-2 focus:ring-black focus:border-transparent transition-all"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (input.trim()) handleSend();
                  }
                }}
              />
            </div>
            <Button
              type="submit"
              disabled={!input.trim() || isLoading}
              className="h-10 w-10 rounded-full bg-black hover:bg-gray-800 text-white shadow-sm hover:shadow-md transition-all"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : <SendHorizontal className="h-4 w-4" />}
            </Button>
          </form>
        </div>
      </div>
    </>
  );
}
