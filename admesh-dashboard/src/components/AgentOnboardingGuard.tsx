"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

interface AgentOnboardingGuardProps {
  children: React.ReactNode;
}

export default function AgentOnboardingGuard({ children }: AgentOnboardingGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, role, onboardingStatus, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Skip the guard if not an agent user
    if (role !== "agent") {
      setIsLoading(false);
      return;
    }

    // Skip if already on the onboarding page
    if (pathname === "/dashboard/agent/onboarding") {
      setIsLoading(false);
      return;
    }

    const checkOnboardingStatus = async () => {
      if (authLoading) return;

      // If we already have the onboarding status from the auth hook
      if (onboardingStatus !== null) {
        // If onboarding is not completed, redirect to the onboarding page
        if (onboardingStatus !== "completed") {
          router.push("/dashboard/agent/onboarding");
        } else if (pathname === "/dashboard/agent") {
          // If completed and on the main agent dashboard, redirect to queries
          router.push("/dashboard/agent/queries/");
        }
        setIsLoading(false);
        return;
      }

      // If we don't have the status from claims, check with the API
      if (user) {
        try {
          const token = await user.getIdToken();
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/onboarding/status`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            const isCompleted = data.onboarding_status === "completed";

            // If onboarding is not completed, redirect to the onboarding page
            if (!isCompleted) {
              router.push("/dashboard/agent/onboarding");
            } else if (pathname === "/dashboard/agent") {
              // If completed and on the main agent dashboard, redirect to queries
              router.push("/dashboard/agent/queries/");
            }
          } else {
            // If API call fails, redirect to the onboarding page to be safe
            router.push("/dashboard/agent/onboarding");
          }
        } catch (error) {
          console.error("Error checking agent onboarding status:", error);
          // If API call fails, redirect to the onboarding page to be safe
          router.push("/dashboard/agent/onboarding");
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user, role, router, onboardingStatus, authLoading, pathname]);

  // Show loading spinner while checking onboarding status
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Checking onboarding status...</p>
      </div>
    );
  }

  // If we're here, either onboarding is completed or we're not an agent
  return <>{children}</>;
}
