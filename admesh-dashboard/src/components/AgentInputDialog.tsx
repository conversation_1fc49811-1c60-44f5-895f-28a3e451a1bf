"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { <PERSON>rk<PERSON> } from "lucide-react";

interface AgentInputDialogProps {
  open: boolean;
  onClose: () => void;
  data: {
    url: string;
    fields: string[];
    title: string;
  } | null;
  onSubmit: (url: string, inputs: Record<string, string>) => Promise<void>;
}

export default function AgentInputDialog({ open, onClose, data, onSubmit }: AgentInputDialogProps) {
  const [inputs, setInputs] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setInputs((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!data) return;

    // Check if all required fields are filled
    const missingFields = data.fields.filter(field => !inputs[field]);
    if (missingFields.length > 0) {
      toast.error(`Please fill all required fields: ${missingFields.join(", ")}`);
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(data.url, inputs);
      toast.success("Information submitted to agent", {
        description: "The agent is processing your request",
        icon: <Sparkles className="h-4 w-4" />
      });
      onClose();
    } catch (error) {
      console.error("Error submitting agent inputs:", error);
      toast.error("Failed to submit information", {
        description: "Please try again or visit the site manually"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset inputs when dialog opens with new data
  if (open && data && Object.keys(inputs).length === 0) {
    const initialInputs: Record<string, string> = {};
    data.fields.forEach(field => {
      initialInputs[field] = "";
    });
    setInputs(initialInputs);
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Agent Needs Information</DialogTitle>
        </DialogHeader>

        {data && (
          <div className="py-4">
            <p className="text-sm text-gray-500 mb-4">
              To sign up for <span className="font-medium">{data.title}</span>, the agent needs the following information:
            </p>

            <div className="space-y-4">
              {data.fields.map((field) => (
                <div key={field} className="space-y-2">
                  <Label htmlFor={field} className="capitalize">
                    {field.replace(/_/g, " ")}
                  </Label>
                  <Input
                    id={field}
                    type={field.includes("password") ? "password" : field.includes("email") ? "email" : "text"}
                    value={inputs[field] || ""}
                    onChange={(e) => handleInputChange(field, e.target.value)}
                    placeholder={`Enter your ${field.replace(/_/g, " ")}`}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSubmitting ? (
              <>
                <span>Processing</span>
                <div className="ml-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              </>
            ) : (
              <>Submit</>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
