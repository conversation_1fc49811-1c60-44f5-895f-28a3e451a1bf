"use client";

import Link from "next/link";
import { FaLinkedin } from "react-icons/fa"; // Optional: for LinkedIn icon

export default function Footer() {
  return (
    <footer className="border-t bg-gradient-to-b from-slate-50 to-blue-50 py-8 text-sm text-muted-foreground">
      <div className="mx-auto max-w-6xl px-6 flex flex-col sm:flex-row items-center justify-between gap-6">
        {/* Copyright section */}
        <div className="text-center sm:text-left text-gray-700">
          © {new Date().getFullYear()} <span className="font-semibold text-primary">AdMesh</span>. All rights reserved.
        </div>

        {/* Links section */}
        <div className="flex items-center gap-6">
        <a
            href="https://www.linkedin.com/company/admesh"
            target="_blank"
            rel="noopener noreferrer"
            className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700 flex items-center gap-1"
          >
            <FaLinkedin className="h-4 w-4" /> LinkedIn
          </a>
          <Link
            href="/terms"
            className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
          >
            Terms
          </Link>
          <Link
            href="/privacy"
            className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
          >
            Privacy
          </Link>
          <a
            href="mailto:<EMAIL>"
            className="hover:text-primary transition-colors duration-300 ease-in-out text-gray-700"
          >
            Contact
          </a>

        </div>
      </div>
    </footer>
  );
}
