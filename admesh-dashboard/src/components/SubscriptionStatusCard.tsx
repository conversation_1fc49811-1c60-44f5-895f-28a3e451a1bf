"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, CreditCard, AlertCircle, Calendar, Package, Tag } from "lucide-react";
import { toast } from "sonner";

interface Subscription {
  plan_id: string;
  billing_cycle: "monthly" | "annual" | "free";
  status: string;
  stripe_subscription_id?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  promo_credit_applied?: boolean;
}

interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  price_annual_cents?: number;
  billing_cycle: "monthly" | "annual" | "free";
  product_listings_limit: number;
  active_offers_per_product_limit: number;
  promo_credit_cents: number;
  visibility_boost: string;
  analytics_level: string;
  conversion_reports: string;
  agent_match_priority: string;
  badge_type?: string;
  support_level: string;
  agent_outreach_tools: boolean;
  multi_user_access: boolean;
  multi_user_limit: number;
  cpa_optimization: boolean;
}

export default function SubscriptionStatusCard() {
  const { user } = useAuth();
  const router = useRouter();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [applyingPromoCredit, setApplyingPromoCredit] = useState(false);

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscription = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const token = await user.getIdToken();

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!response.ok) {
          throw new Error("Failed to fetch subscription data");
        }

        const data = await response.json();
        setSubscription(data.subscription);
        setPlan(data.plan);

      } catch (error) {
        console.error("Error fetching subscription:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();
  }, [user]);

  // Handle applying promo credit
  const handleApplyPromoCredit = async () => {
    if (!user) return;

    try {
      setApplyingPromoCredit(true);
      const token = await user.getIdToken();

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription/apply-promo-credit`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error("Failed to apply promo credit");
      }

      const data = await response.json();

      // Refresh subscription data
      const subResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (subResponse.ok) {
        const subData = await subResponse.json();
        setSubscription(subData.subscription);
        setPlan(subData.plan);
      }

      toast.success("Promo credit applied", {
        description: `$${data.amount/100} has been added to your wallet.`
      });

    } catch (error) {
      console.error("Error applying promo credit:", error);
      toast.error("Failed to apply promo credit");
    } finally {
      setApplyingPromoCredit(false);
    }
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  // Format price for display
  const formatPrice = (cents?: number) => {
    if (!cents) return "$0.00";
    return `$${(cents / 100).toFixed(2)}`;
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Subscription</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (!subscription || !plan) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-sm">Subscription</CardTitle>
          <Badge
            variant={subscription.status === "active" ? "default" : "destructive"}
            className="capitalize"
          >
            {subscription.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="font-medium">{plan.name} Plan</div>
          <div>
            {plan.price_monthly_cents === 0 ? (
              "Free"
            ) : plan.billing_cycle === "annual" && plan.price_annual_cents ? (
              <div className="text-right">
                <div>{formatPrice(plan.price_annual_cents / 12)}/mo</div>
                <div className="text-xs text-muted-foreground">
                  Billed annually
                </div>
              </div>
            ) : (
              `${formatPrice(plan.price_monthly_cents)}/mo`
            )}
          </div>
        </div>

        {subscription.billing_cycle !== "free" && (
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center gap-1">
              <CreditCard className="h-3.5 w-3.5 text-muted-foreground" />
              <span>Billing:</span>
            </div>
            <div className="capitalize">{subscription.billing_cycle}</div>
          </div>
        )}

        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center gap-1">
            <Package className="h-3.5 w-3.5 text-muted-foreground" />
            <span>Products:</span>
          </div>
          <div>{plan.product_listings_limit === 0 || plan.product_listings_limit === -1 ? "Unlimited" : plan.product_listings_limit}</div>
        </div>

        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center gap-1">
            <Tag className="h-3.5 w-3.5 text-muted-foreground" />
            <span>Offers per product:</span>
          </div>
          <div>{plan.active_offers_per_product_limit === 0 || plan.active_offers_per_product_limit === -1 ? "Unlimited" : plan.active_offers_per_product_limit}</div>
        </div>

        {subscription.current_period_end && (
          <div className="flex justify-between items-center text-sm">
            <div className="flex items-center gap-1">
              <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
              <span>Renews:</span>
            </div>
            <div>
              {subscription.cancel_at_period_end
                ? "Cancels on " + formatDate(subscription.current_period_end)
                : formatDate(subscription.current_period_end)
              }
            </div>
          </div>
        )}

        {/* Promo Credit Section */}
        {plan.promo_credit_cents > 0 && !subscription.promo_credit_applied && (
          <div className="pt-2">
            <div className="text-xs bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-2">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-800 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Promo Credit Available</p>
                  <p className="mt-1">{formatPrice(plan.promo_credit_cents)} in promotional credit available.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleApplyPromoCredit}
                    disabled={applyingPromoCredit}
                    className="w-full mt-2"
                  >
                    {applyingPromoCredit ? (
                      <>
                        <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                        Applying...
                      </>
                    ) : (
                      "Apply Promo Credit"
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => router.push("/dashboard/brand/subscription")}
        >
          Manage Subscription
        </Button>
      </CardFooter>
    </Card>
  );
}
