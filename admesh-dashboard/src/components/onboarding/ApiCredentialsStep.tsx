"use client";

// Updated ApiCredentialsStep component with conditional production key generation and test logic
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Code, Copy, Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { ApiCredentialsStepProps } from "@/types/onboarding";

// Define a type for the API key response
// interface ApiKeyResponse {
//   id: string;
//   agent_id: string;
//   key: string;
//   type: string;
//   created_at: number;
//   last_used: number | null;
//   is_active: boolean;
//   name: string;
// }

/**
 * ApiCredentialsStep component for managing API keys and testing API endpoints
 */
const ApiCredentialsStep = ({
  agentId,
  testApiKey,
  productionApiKey,
  onComplete,
  isLoading,
  onTestKeyGenerated,
  onProductionKeyGenerated
}: ApiCredentialsStepProps) => {
  const [isTestingApi, setIsTestingApi] = useState(false);
  const [testQuery, setTestQuery] = useState("Best AI tools for content creation");
  const [testResults, setTestResults] = useState<Record<string, unknown> | null>(null);
  const [testStatus, setTestStatus] = useState<"idle" | "loading" | "success" | "error">("idle");
  const [showProductionKey, setShowProductionKey] = useState(false);
  const [isGeneratingTestKey, setIsGeneratingTestKey] = useState(false);
  const [isGeneratingProductionKey, setIsGeneratingProductionKey] = useState(false);
  const [isLoadingKeys, setIsLoadingKeys] = useState(false);
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "https://api.useadmesh.com";

  // Automatically fetch API keys when component mounts
  useEffect(() => {
    const fetchApiKeys = async () => {
      // If we already have a test key, no need to fetch
      if (testApiKey) return;

      setIsLoadingKeys(true);

      try {
        // Get the user's auth token
        const user = await import("firebase/auth").then(module => {
          const { getAuth } = module;
          return getAuth().currentUser;
        });

        if (!user) {
          throw new Error("User not authenticated");
        }

        const token = await user.getIdToken();

        // Check if keys already exist for this agent
        const response = await fetch(`${apiBaseUrl}/agent/onboarding/data`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();

          // If we have a test key, use it
          if (data.api_keys && data.api_keys.test?.key) {
            const existingKey = data.api_keys.test.key;
            const existingKeyId = data.api_keys.test.id;

            if (onTestKeyGenerated) {
              onTestKeyGenerated({ key: existingKey, id: existingKeyId });
            }

            // Set default test query
            setTestQuery("Best AI tools for content creation");
          }

          // If we have a production key, use it
          if (data.api_keys && data.api_keys.production?.key) {
            const existingKey = data.api_keys.production.key;
            const existingKeyId = data.api_keys.production.id;

            if (onProductionKeyGenerated) {
              onProductionKeyGenerated({ key: existingKey, id: existingKeyId });
            }

            setShowProductionKey(true);
          }
        }
      } catch (error) {
        console.error("Error fetching API keys:", error);
      } finally {
        setIsLoadingKeys(false);
      }
    };

    fetchApiKeys();
  }, [testApiKey, onTestKeyGenerated, onProductionKeyGenerated, apiBaseUrl]);

  // Function to test the API endpoint with a specific key
  const testApiEndpointWithKey = async (apiKeyToUse: string) => {
    if (!testQuery.trim()) {
      toast.error("Please enter a query");
      return;
    }
    setIsTestingApi(true);
    setTestStatus("loading");
    try {
      const response = await fetch(`${apiBaseUrl}/openrouter/recommend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${apiKeyToUse}`,
        },
        body: JSON.stringify({ query: testQuery, agent_id: agentId }),
      });
      const data = await response.json();
      setTestResults(data);
      if (response.ok) {
        setTestStatus("success");
        toast.success("API test successful!");
      } else {
        setTestStatus("error");
        toast.error("API test failed: " + (data.error || "Unknown error"));
      }
    } catch (error) {
      setTestStatus("error");
      setTestResults({ error: error instanceof Error ? error.message : "Unknown error" });
      toast.error("API test failed");
    } finally {
      setIsTestingApi(false);
    }
  };

  // Function to generate an API key (test or production)
  const generateApiKey = async (keyType: "test" | "production") => {
    const isTest = keyType === "test";
    if (isTest) {
      setIsGeneratingTestKey(true);
    } else {
      setIsGeneratingProductionKey(true);
    }

    try {
      // Get the user's auth token
      const user = await import("firebase/auth").then(module => {
        const { getAuth } = module;
        return getAuth().currentUser;
      });

      if (!user) {
        throw new Error("User not authenticated");
      }

      const token = await user.getIdToken();

      // First, check if a key already exists for this agent
      const checkResponse = await fetch(`${apiBaseUrl}/agent/onboarding/data`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (checkResponse.ok) {
        const data = await checkResponse.json();

        // If we already have a key of this type, use it instead of generating a new one
        if (data.api_keys && data.api_keys[keyType]?.key) {
          const existingKey = data.api_keys[keyType].key;
          const existingKeyId = data.api_keys[keyType].id;

          if (isTest) {
            if (onTestKeyGenerated) {
              onTestKeyGenerated({ key: existingKey, id: existingKeyId });
            }
            toast.success("Retrieved existing test API key");
            setTestQuery("Best AI tools for content creation");
          } else {
            if (onProductionKeyGenerated) {
              onProductionKeyGenerated({ key: existingKey, id: existingKeyId });
            }
            toast.success("Retrieved existing production API key");
            setShowProductionKey(true);
          }

          if (isTest) {
            setIsGeneratingTestKey(false);
          } else {
            setIsGeneratingProductionKey(false);
          }

          return;
        }
      }

      // If no existing key was found, generate a new one
      const response = await fetch(`${apiBaseUrl}/api-keys/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          agent_id: agentId,
          type: keyType,
          name: `${keyType.charAt(0).toUpperCase() + keyType.slice(1)} API Key`
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `Failed to generate ${keyType} API key`);
      }

      const keyData = await response.json();
      const newKey = keyData.key;
      const keyId = keyData.id;

      // Update the agent document with the API key ID
      try {
        // Update the agent document with the API key ID
        await fetch(`${apiBaseUrl}/agent/update-api-key`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          },
          body: JSON.stringify({
            agent_id: agentId,
            key_type: keyType,
            key_id: keyId
          })
        });
      } catch (error) {
        console.error(`Error updating agent with API key ID: ${error}`);
        // Continue even if this fails, as the key was still created
      }

      // Update the appropriate state with the new key
      if (isTest) {
        // Call the callback to update the parent component state if provided
        if (onTestKeyGenerated) {
          onTestKeyGenerated({ key: newKey, id: keyId });
        }

        toast.success("Test API key generated successfully");

        // Automatically prepare for testing with the new key
        setTestQuery("Best AI tools for content creation");

        // If no callback is provided, reload the page to get the updated key
        if (!onTestKeyGenerated) {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      } else {
        // Call the callback to update the parent component state if provided
        if (onProductionKeyGenerated) {
          onProductionKeyGenerated({ key: newKey, id: keyId });
        }

        toast.success("Production API key generated successfully");
        setShowProductionKey(true);

        // If no callback is provided, reload the page to get the updated key
        if (!onProductionKeyGenerated) {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      }
    } catch (error) {
      console.error(`Error generating ${keyType} API key:`, error);
      toast.error(`Failed to generate ${keyType} API key: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      if (isTest) {
        setIsGeneratingTestKey(false);
      } else {
        setIsGeneratingProductionKey(false);
      }
    }
  };

  // Generate test API key
  const generateTestKey = () => generateApiKey("test");

  // Generate production API key
  const generateProductionKey = () => generateApiKey("production");

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <div className="mx-auto bg-primary/10 dark:bg-primary/20 w-16 h-16 rounded-full flex items-center justify-center mb-4">
          <Code className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-2xl font-bold">API Credentials</h2>
        <p className="text-muted-foreground">
          Set up and test your API keys to integrate with AdMesh
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Test API Key</Label>
          {testApiKey ? (
            <div className="relative">
              <Input
                value={testApiKey}
                readOnly
                className="h-12 pr-20 font-mono text-sm"
              />
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-2 top-2"
                onClick={() => {
                  navigator.clipboard.writeText(testApiKey);
                  toast.success("Test API Key copied to clipboard");
                }}
              >
                <Copy className="h-4 w-4 mr-1" /> Copy
              </Button>
            </div>
          ) : isLoadingKeys ? (
            <div className="flex items-center justify-center py-3 border rounded-md">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              <span className="text-sm">Retrieving API keys...</span>
            </div>
          ) : (
            <Button
              onClick={generateTestKey}
              disabled={isGeneratingTestKey}
              className="w-full"
            >
              {isGeneratingTestKey ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                </>
              ) : (
                <>Generate Test API Key</>
              )}
            </Button>
          )}
          <p className="text-xs text-muted-foreground">
            Use this key for testing. It won&apos;t affect your production data.
          </p>
        </div>

        {testApiKey && (
          <div className="space-y-2 border rounded-lg p-4 bg-card/50">
            <Label>Test API Endpoint</Label>
            <p className="text-xs text-muted-foreground mb-2">
              Try out the /recommend endpoint with your test API key
            </p>

            <div className="space-y-2">
              <Label htmlFor="testQuery">Query</Label>
              <Input
                id="testQuery"
                value={testQuery}
                onChange={(e) => setTestQuery(e.target.value)}
                placeholder="Enter a test query"
                className="h-10"
              />
            </div>

            <div className="text-xs text-muted-foreground mt-2">
              <p>Endpoint: <code className="bg-muted px-1 py-0.5 rounded">{apiBaseUrl}/openrouter/recommend</code></p>
              <p className="mt-1">Required parameters:</p>
              <ul className="list-disc list-inside ml-2">
                <li><code className="bg-muted px-1 py-0.5 rounded">query</code>: The user&apos;s search query</li>
                <li><code className="bg-muted px-1 py-0.5 rounded">agent_id</code>: Your agent ID</li>
              </ul>
            </div>

            <Button
              onClick={() => testApiEndpointWithKey(testApiKey)}
              disabled={isTestingApi}
              className="w-full mt-2"
              variant="secondary"
            >
              {isTestingApi ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Testing...
                </>
              ) : (
                <>Test Endpoint</>
              )}
            </Button>

            {testResults && (
              <Accordion type="single" collapsible className="mt-2">
                <AccordionItem value="test-results">
                  <AccordionTrigger className="text-sm">
                    {testStatus === "success" ? (
                      <span className="text-green-600 dark:text-green-400 flex items-center">
                        <CheckCircle className="h-4 w-4 mr-1" /> Test Successful
                      </span>
                    ) : testStatus === "error" ? (
                      <span className="text-red-600 dark:text-red-400 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" /> Test Failed
                      </span>
                    ) : (
                      "View Test Results"
                    )}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="bg-muted p-2 rounded-md overflow-auto max-h-60">
                      <pre className="text-xs whitespace-pre-wrap">
                        {JSON.stringify(testResults, null, 2)}
                      </pre>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </div>
        )}

        {testStatus === "success" && !showProductionKey && !productionApiKey && (
          <Button
            onClick={generateProductionKey}
            disabled={isGeneratingProductionKey}
            className="w-full"
            variant="default"
          >
            {isGeneratingProductionKey ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
              </>
            ) : (
              <>Generate Production API Key</>
            )}
          </Button>
        )}

        {(showProductionKey || productionApiKey) && (
          <div className="mt-4 border p-4 rounded-md">
            <Label>Production API Key</Label>
            <div className="relative">
              <Input
                value={productionApiKey}
                readOnly
                className="h-12 pr-20 font-mono text-sm"
              />
              <Button
                size="sm"
                variant="ghost"
                className="absolute right-2 top-2"
                onClick={() => {
                  navigator.clipboard.writeText(productionApiKey);
                  toast.success("Production API Key copied to clipboard");
                }}
              >
                <Copy className="h-4 w-4 mr-1" /> Copy
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Keep this private and secure. Use this key for production environments.
            </p>

            <div className="mt-2">
              <Label>Test Production Endpoint</Label>
              <Input
                value={testQuery}
                onChange={(e) => setTestQuery(e.target.value)}
                placeholder="Enter query"
                className="h-10 mt-1"
              />
              <Button
                onClick={() => testApiEndpointWithKey(productionApiKey)}
                className="w-full mt-2"
                variant="secondary"
              >
                Test Production Endpoint
              </Button>
            </div>

            <Alert className="mt-2" variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Costs May Apply</AlertTitle>
              <AlertDescription>
                Testing production key may incur charges. Use only if you&apos;re ready.
              </AlertDescription>
            </Alert>
          </div>
        )}

        <div className="space-y-2">
          <Label>SDK Repositories</Label>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => window.open("https://github.com/GouniManikumar12/admesh-python", "_blank")}
            >
              <Code className="h-4 w-4" />
              Python SDK
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              onClick={() => window.open("https://github.com/GouniManikumar12/admesh-typescript", "_blank")}
            >
              <Code className="h-4 w-4" />
              TypeScript SDK
            </Button>
          </div>
        </div>
      </div>

      {testApiKey && testStatus !== "success" && (
        <div className="text-center text-sm text-amber-600 dark:text-amber-400 mb-2">
          <p>You can go to the dashboard now, or test your API key first.</p>
        </div>
      )}

      <Button
        onClick={onComplete}
        disabled={isLoading || !testApiKey}
        className="w-full h-12"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...
          </>
        ) : (
          "Go to Dashboard"
        )}
      </Button>
    </motion.div>
  );
};

export default ApiCredentialsStep;
