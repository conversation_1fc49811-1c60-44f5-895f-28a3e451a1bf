"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ExternalLink, RefreshCw, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface SignupAttempt {
  user_id: string;
  url: string;
  timestamp: string;
  status: string;
  session_id?: string;
  fields?: string[];
  message?: string;
  screenshot_path?: string;
}

export default function SignupAttemptHistory() {
  const { user } = useAuth();
  const [attempts, setAttempts] = useState<SignupAttempt[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAttempts = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/signup-attempts/${user.uid}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (!res.ok) {
        throw new Error(`Failed to fetch signup attempts: ${res.status}`);
      }

      const data = await res.json();
      setAttempts(data);
    } catch (err) {
      console.error("Error fetching signup attempts:", err);
      setError("Failed to load signup history. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      fetchAttempts();
    }
  }, [user, fetchAttempts]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return (
          <Badge className="bg-green-500 hover:bg-green-600">
            <CheckCircle className="h-3 w-3 mr-1" /> Success
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-red-500 hover:bg-red-600">
            <XCircle className="h-3 w-3 mr-1" /> Error
          </Badge>
        );
      case "input_required":
        return (
          <Badge className="bg-blue-500 hover:bg-blue-600">
            <AlertCircle className="h-3 w-3 mr-1" /> Input Required
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Signup Attempts</CardTitle>
          <CardDescription>Please sign in to view your signup attempt history.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Signup Attempts</CardTitle>
          <CardDescription>History of your automated signup attempts</CardDescription>
        </div>
        <Button variant="outline" size="sm" onClick={fetchAttempts} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex flex-col space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
                <Skeleton className="h-4 w-1/4" />
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : attempts.length === 0 ? (
          <div className="text-center py-4 text-gray-500">No signup attempts found.</div>
        ) : (
          <div className="space-y-4">
            {attempts
              .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
              .map((attempt, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium truncate max-w-[200px] sm:max-w-[300px]">
                        {new URL(attempt.url).hostname}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(attempt.timestamp), { addSuffix: true })}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(attempt.status)}
                      <Button variant="ghost" size="sm" asChild>
                        <a href={attempt.url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </div>
                  {attempt.message && (
                    <p className="text-sm mt-2">{attempt.message}</p>
                  )}
                  {attempt.fields && attempt.fields.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">Required fields:</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {attempt.fields.map((field) => (
                          <Badge key={field} variant="outline" className="text-xs">
                            {field}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
