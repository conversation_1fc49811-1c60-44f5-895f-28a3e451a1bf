"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { BadgeData } from "@/components/BadgeIcon";
import { BADGE_ICONS, BADGE_COLORS } from "@/lib/badge-utils";
import { Award, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import {
  FacebookShareButton,
  FacebookIcon,
  LinkedinShareButton,
  LinkedinIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon
} from 'next-share';

interface ShareBadgeModalProps {
  badge: BadgeData;
  isOpen: boolean;
  onClose: () => void;
}

export default function ShareBadgeModal({ badge, isOpen, onClose }: ShareBadgeModalProps) {
  const [copied, setCopied] = useState(false);
  const shareUrl = typeof window !== 'undefined'
    ? `${window.location.origin}/badges/share/${badge.badge_id}`
    : '';

  const IconComponent = BADGE_ICONS[badge.badge_type] || Award;
  const bgColor = BADGE_COLORS[badge.badge_type] || "bg-gray-500";

  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    toast.success("Link copied to clipboard!");
    setTimeout(() => setCopied(false), 2000);
  };

  // Share text for social media
  const shareTitle = `I just earned the ${badge.metadata.name} badge on AdMesh!`;
  const shareText = `${badge.metadata.description} Check out my achievement:`;
  const hashtags = ['AdMesh', 'Achievement', 'Badge'];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Badge</DialogTitle>
          <DialogDescription>
            Share your &quot;{badge.metadata.name}&quot; badge with others
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-center py-4">
          <div className={`${bgColor} h-20 w-20 rounded-full flex items-center justify-center text-white`}>
            <IconComponent className="h-10 w-10" />
          </div>
        </div>

        <div className="text-center mb-4">
          <h3 className="font-semibold text-lg">{badge.metadata.name}</h3>
          <p className="text-sm text-muted-foreground">{badge.metadata.description}</p>
        </div>

        <div className="grid gap-4">
          <div className="grid grid-cols-3 gap-2">
            <div className="flex flex-col items-center justify-center h-24 p-2">
              <TwitterShareButton
                url={shareUrl}
                title={shareTitle}
                hashtags={hashtags}
              >
                <TwitterIcon size={40} round />
                <span className="text-xs mt-2">Twitter</span>
              </TwitterShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <LinkedinShareButton
                url={shareUrl}
                title={shareTitle}
                summary={shareText}
              >
                <LinkedinIcon size={40} round />
                <span className="text-xs mt-2">LinkedIn</span>
              </LinkedinShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <FacebookShareButton
                url={shareUrl}
                quote={`${shareTitle} ${shareText}`}
                hashtag={hashtags[0]}
              >
                <FacebookIcon size={40} round />
                <span className="text-xs mt-2">Facebook</span>
              </FacebookShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <WhatsappShareButton
                url={shareUrl}
                title={`${shareTitle} ${shareText}`}
                separator=" - "
              >
                <WhatsappIcon size={40} round />
                <span className="text-xs mt-2">WhatsApp</span>
              </WhatsappShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <RedditShareButton
                url={shareUrl}
                title={shareTitle}
              >
                <RedditIcon size={40} round />
                <span className="text-xs mt-2">Reddit</span>
              </RedditShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <EmailShareButton
                url={shareUrl}
                subject={shareTitle}
                body={shareText}
              >
                <EmailIcon size={40} round />
                <span className="text-xs mt-2">Email</span>
              </EmailShareButton>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="grid flex-1 gap-2">
              <Label htmlFor="link" className="sr-only">
                Link
              </Label>
              <Input
                id="link"
                defaultValue={shareUrl}
                readOnly
                className="h-9"
              />
            </div>
            <Button type="submit" size="sm" className="px-3" onClick={copyToClipboard}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              <span className="sr-only">Copy</span>
            </Button>
          </div>
        </div>

        <DialogFooter className="sm:justify-start">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
