"use client";

import { useState } from "react";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Loader2, Mail, User, ChevronRight } from "lucide-react";
import AuthModal from "@/components/AuthModal";

export default function WaitlistForm() {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [authOpen, setAuthOpen] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setEmailError(null);

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email || !role) {
      toast.error("Please enter your email and select a role.");
      return;
    }

    if (!emailRegex.test(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }

    setSubmitted(true);

    try {
      const res = await fetch(
        "https://us-central1-admesh-9560c.cloudfunctions.net/submitWaitlist",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email, role }),
        }
      );

      if (res.status === 409) {
        toast.error("This email is already on the waitlist.");
        return;
      }

      if (res.status === 429) {
        toast.error("Too many attempts. Please wait and try again later.");
        return;
      }

      if (!res.ok) {
        const errorMessage = await res.text();
        throw new Error(errorMessage || "Waitlist submission failed");
      }

      setEmail("");
      setRole("");
      toast.success("You're on the waitlist!");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("Something went wrong.");
      }
    } finally {
      setTimeout(() => setSubmitted(false), 400);
    }
  };

  return (
    <section
      id="waitlist"
      className="w-full bg-gradient-to-b from-blue-100 to-blue-50 py-24 px-4"
    >
      <div className="mx-auto max-w-2xl">
        <div className="text-center mb-10">
          <span className="inline-block px-3 py-1 text-sm font-medium rounded-full bg-blue-50 text-blue-600 mb-4">
            Early Access
          </span>
          <h2 className="text-3xl font-bold sm:text-4xl mb-4 text-gray-800">
            Join the AdMesh Waitlist
          </h2>
          <p className="text-gray-600 max-w-lg mx-auto">
            Be among the first to launch with AdMesh. We&apos;re inviting select agents, brands, and curious builders to our beta program.
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Role Selector */}
            <div>
              <Label
                htmlFor="role"
                className="text-sm font-medium mb-2 flex items-center gap-2 text-gray-700"
              >
                <User className="h-4 w-4 text-blue-500" />
                I am a...
              </Label>
              <Select value={role} onValueChange={setRole}>
                <SelectTrigger
                  className="py-4 px-6 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  aria-label="Select your role"
                >
                  <SelectValue placeholder="Select your role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="agent">Agent</SelectItem>
                  <SelectItem value="brand">Brand</SelectItem>
                  <SelectItem value="user">Curious User</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Conditional Rendering */}
            {role === "user" ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                <p className="text-blue-700 mb-4">
                  Curious to explore? Create a free AdMesh account and start your journey.
                </p>
                <Button
                  type="button"
                  onClick={() => setAuthOpen(true)}
                  className="w-full py-4 bg-indigo-600 hover:bg-indigo-700 text-white text-lg font-medium flex items-center justify-center gap-2"
                >
                  Sign Up
                  <ChevronRight className="h-5 w-5" />
                </Button>
              </div>
            ) : role ? (
              <>
                {/* Email Input */}
                <div>
                  <Label
                    htmlFor="email"
                    className="text-sm font-medium mb-2 flex items-center gap-2 text-gray-700"
                  >
                    <Mail className="h-4 w-4 text-blue-500" />
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="py-4 px-6 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  />
                  {emailError && (
                    <p className="mt-2 text-sm text-red-500">{emailError}</p>
                  )}
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full py-6 bg-indigo-600 hover:bg-indigo-700 text-white text-lg font-medium flex items-center justify-center gap-2"
                  disabled={submitted}
                >
                  {submitted ? (
                    <span className="flex items-center justify-center gap-2">
                      <Loader2 className="h-5 w-5 animate-spin" />
                      Submitting...
                    </span>
                  ) : (
                    <>
                      Join Waitlist
                      <ChevronRight className="h-5 w-5" />
                    </>
                  )}
                </Button>
              </>
            ) : null}
          </form>

          <div className="mt-6 pt-6 border-t border-gray-100 text-center">
            <p className="text-sm text-gray-500">
              By joining, you&apos;ll be first to know when we launch and get priority access.
            </p>
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal open={authOpen} onClose={() => setAuthOpen(false)} />
    </section>
  );
}
