"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { ExternalLink, Gift, Sparkles, DollarSign } from "lucide-react";
import { Recommendation } from "./ChatWindow";
import { useAuth } from "@/hooks/use-auth";

// Helper function to append tracking parameters to AdMesh links
function appendTrackingParams(url: string, rec_id: string, session_id: string, agent_id: string, user_id: string) {
  // Check if the URL already has query parameters
  const hasQueryParams = url.includes('?');
  const separator = hasQueryParams ? '&' : '?';

  // Construct the final URL with tracking parameters
  const finalUrl = `${url}${separator}rec_id=${encodeURIComponent(rec_id)}&session_id=${encodeURIComponent(session_id)}&agent_id=${encodeURIComponent(agent_id)}&user_id=${encodeURIComponent(user_id)}`;

  return finalUrl;
}

interface CompareDialogProps {
    open: boolean;
    onClose: () => void;
    recommendations: Recommendation[];
    followupSuggestions?: string[];
    onAskFollowup: (query: string) => void;
    sessionId?: string;
    recommendationId?: string;
  }


export default function CompareDialog({
  open,
  onClose,
  recommendations,
  followupSuggestions,
  onAskFollowup,
  sessionId = "",
  recommendationId = ""
}: CompareDialogProps) {
  const [input, setInput] = useState("");
  const { user } = useAuth();
  const agentId = process.env.NEXT_PUBLIC_AGENT_ID || "admesh-website";

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="w-full max-w-[95vw] sm:max-w-6xl rounded-xl overflow-hidden p-0"
        style={{
          margin: "5vh auto",
          maxHeight: "90vh",
          display: "flex",
          flexDirection: "column",
          backgroundColor: "white",
        }}
      >
        {/* Header */}
        <DialogHeader className="px-6 py-4 border-b shrink-0">
          <DialogTitle className="text-lg font-semibold flex items-center">
             Compare Side-by-Side
          </DialogTitle>
        </DialogHeader>

        {/* Scrollable Comparison Cards */}
        <div className="flex-1 overflow-y-auto px-4 pt-6 pb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 w-full">
            {recommendations.map((rec, i) => (
              <div
                key={i}
                className="border rounded-xl p-5 bg-white dark:bg-slate-800 shadow-sm flex flex-col"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-semibold text-lg">{rec.title}</h3>
                  <a
                    href={rec.admesh_link ?
                      appendTrackingParams(
                        rec.admesh_link,
                        recommendationId,
                        sessionId,
                        agentId,
                        user?.uid || ""
                      ) : rec.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-black dark:text-white hover:underline flex items-center bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full"
                  >
                    Visit <ExternalLink className="ml-1 h-3 w-3" />
                  </a>
                </div>

                <div className="space-y-3 mb-4">
                  {rec.pricing && (
                    <div className="flex items-center gap-2 text-black dark:text-white">
                      <DollarSign className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">
                        {typeof rec.pricing === "string"
                          ? rec.pricing
                          : `Monthly: ${rec.pricing.monthly || "N/A"}, Annual: ${rec.pricing.annual || "N/A"}`}
                      </span>
                    </div>
                  )}
                  {rec.has_free_tier && (
                    <div className="flex items-center gap-2 text-black dark:text-white">
                      <Gift className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">Free Tier Available</span>
                    </div>
                  )}
                  {rec.trial_days && (
                    <div className="flex items-center gap-2 text-black dark:text-white">
                      <Sparkles className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm">{rec.trial_days}-day Trial</span>
                    </div>
                  )}
                </div>

                <div className="mt-auto">
                  {rec.features && (
                    <div className="mb-3">
                      <div className="font-medium text-sm mb-1">Features:</div>
                      <ul className="list-disc ml-5 text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        {rec.features.slice(0, 3).map((f, idx) => (
                          <li key={idx}>{f}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {(rec.integrations ?? []).length > 0 && (
                    <div className="mb-3">
                      <div className="font-medium text-sm mb-1">Integrations:</div>
                      <ul className="list-disc ml-5 text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        {(rec.integrations ?? []).slice(0, 2).map((i, idx) => (
                          <li key={idx}>{i}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {rec.security && (
                    <div>
                      <div className="font-medium text-sm mb-1">Security:</div>
                      <ul className="list-disc ml-5 text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        {rec.security.map((s, idx) => (
                          <li key={idx}>{s}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Suggested Follow-up */}
          {(followupSuggestions?.length ?? 0) > 0 && (
            <div className="mt-6">
              <div className="text-sm text-gray-700 dark:text-gray-300 mb-2 font-medium">
                Suggested follow-up questions:
              </div>
              <div className="flex flex-wrap gap-2">
                {(followupSuggestions ?? []).map((q, i) => (
                  <button
                    key={i}
                    onClick={() => setInput(q)}
                    className="text-xs px-3 py-1 rounded-full border border-gray-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-slate-700 transition"
                  >
                    {q}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer Input */}
        <div className="px-6 pt-4 pb-6 border-t bg-white dark:bg-slate-900 dark:border-slate-700 shrink-0">
          <Textarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask a follow-up about these tools..."
            rows={2}
            className="resize-none w-full rounded-lg border-gray-300 dark:border-gray-700 focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-black dark:focus:border-white"
          />
          <div className="flex justify-end mt-3">
          <Button
  disabled={!input.trim()}
  onClick={() => {
    onAskFollowup(input);  // ✅ Send back to ChatWindow
    setInput("");          // optional clear
  }}
  className="bg-black hover:bg-gray-800 text-white font-medium rounded-lg px-5 py-2"
>
  Ask
</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
