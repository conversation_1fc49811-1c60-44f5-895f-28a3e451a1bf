"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

export default function GTMVisualGuide() {
  const [tab, setTab] = useState("step1");

  const steps = [
    {
      id: "step1",
      title: "Create Custom HTML Tag",
      description: "Go to Tags → New → Custom HTML",
      imageSrc: "/images/gtm-step1.png" // Placeholder - you'll need to add these images
    },
    {
      id: "step2",
      title: "Add Pixel Code",
      description: "Paste the AdMesh pixel code in the HTML field",
      imageSrc: "/images/gtm-step2.png"
    },
    {
      id: "step3",
      title: "Configure Trigger",
      description: "Set up a Page View trigger for your thank-you page",
      imageSrc: "/images/gtm-step3.png"
    },
    {
      id: "step4",
      title: "Publish Changes",
      description: "Submit and publish your container",
      imageSrc: "/images/gtm-step4.png"
    }
  ];

  return (
    <div className="border border-border rounded-lg overflow-hidden mt-4">
      <div className="p-4 border-b bg-muted/30">
        <h4 className="text-sm font-medium mb-2">Visual Guide: GTM Implementation</h4>
        <p className="text-xs text-muted-foreground mb-4">
          Follow these steps to set up the AdMesh pixel in Google Tag Manager
        </p>

        <Tabs value={tab} onValueChange={setTab}>
          <TabsList className="grid grid-cols-4 mb-4">
            {steps.map(step => (
              <TabsTrigger key={step.id} value={step.id} className="text-xs">
                {step.title}
              </TabsTrigger>
            ))}
          </TabsList>

          {steps.map(step => (
            <TabsContent key={step.id} value={step.id}>
              <div className="border border-border rounded-md overflow-hidden">
                <div className="p-3 bg-muted/20">
                  <h5 className="text-sm font-medium">{step.title}</h5>
                  <p className="text-xs text-muted-foreground">{step.description}</p>
                </div>
                <div className="p-4 flex justify-center bg-white">
                  {/* Placeholder for actual GTM screenshots */}
                  <div className="bg-gray-200 w-full h-48 flex items-center justify-center rounded">
                    <p className="text-sm text-gray-500">
                      [Screenshot: {step.title}]
                    </p>
                    {/* Uncomment when you have actual images */}
                    {/* <Image
                      src={step.imageSrc}
                      alt={step.title}
                      width={600}
                      height={400}
                      className="rounded"
                    /> */}
                  </div>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
