"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import PioneerOnboardingModal from "./PioneerOnboardingModal";

interface UserOnboardingGuardProps {
  children: React.ReactNode;
}

export default function UserOnboardingGuard({ children }: UserOnboardingGuardProps) {
  const router = useRouter();
  const { user, role, onboardingStatus, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [, setIsPioneerEligible] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Skip the guard if not a user
    if (role !== "user") {
      setIsLoading(false);
      return;
    }

    const checkOnboardingStatus = async () => {
      // Wait for auth to load
      if (authLoading) return;

      // If we already have the onboarding status from the auth hook
      if (onboardingStatus !== null) {
        // If onboarding is not completed, check eligibility for Pioneer program
        if (onboardingStatus === "pending" && user) {
          try {
            const token = await user.getIdToken();
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/pioneer/eligibility`, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });

            if (response.ok) {
              const data = await response.json();
              setIsPioneerEligible(data.isPioneerEligible);

              // Show onboarding modal if eligible
              if (data.isPioneerEligible) {
                setShowOnboarding(true);
              }
            }
          } catch (error) {
            console.error("Error checking pioneer eligibility:", error);
            // If API call fails, we'll show the page anyway
          } finally {
            setIsLoading(false);
          }
        } else {
          // Onboarding is already completed
          setIsLoading(false);
        }
      } else {
        // If we don't have the status, set loading to false
        setIsLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user, role, router, onboardingStatus, authLoading]);

  // Handle closing the onboarding modal
  const handleCloseOnboarding = () => {
    setShowOnboarding(false);
  };

  // Show loading spinner while checking onboarding status
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Checking onboarding status...</p>
      </div>
    );
  }

  return (
    <>
      {children}

      {/* Onboarding Modal */}
      {showOnboarding && (
        <PioneerOnboardingModal
          open={showOnboarding}
          onClose={handleCloseOnboarding}
        />
      )}
    </>
  );
}
