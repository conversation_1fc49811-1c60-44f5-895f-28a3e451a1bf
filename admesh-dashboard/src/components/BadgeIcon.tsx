"use client";

import { useState } from "react";
import { Award, Share2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { BADGE_ICONS, BADGE_COLORS } from "@/lib/badge-utils";
import { Button } from "@/components/ui/button";
import ShareBadgeModal from "@/components/ShareBadgeModal";

export interface BadgeData {
  badge_id: string;
  badge_type: string;
  metadata: {
    name: string;
    description: string;
    icon: string;
    color: string;
    xp_bonus: number;
    earnings_boost_percent?: number;
  };
  awarded_at: string;
  progress?: number;
  progress_target?: number;
  is_displayed?: boolean;
  status?: 'earned' | 'locked';
}

interface BadgeIconProps {
  badge: BadgeData;
  size?: "sm" | "md" | "lg";
  showTooltip?: boolean;
  showShareButton?: boolean;
  className?: string;
}

export default function BadgeIcon({
  badge,
  size = "md",
  showTooltip = true,
  showShareButton = false,
  className
}: BadgeIconProps) {
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const IconComponent = BADGE_ICONS[badge.badge_type] || Award;
  const bgColor = BADGE_COLORS[badge.badge_type] || "bg-gray-500";

  const sizeClasses = {
    sm: "h-7 w-7 p-1.5",
    md: "h-9 w-9 p-2",
    lg: "h-11 w-11 p-2.5"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  };

  const badgeIcon = (
    <div className={cn(
      "rounded-full text-white flex items-center justify-center",
      bgColor,
      sizeClasses[size],
      className
    )}>
      <IconComponent className={iconSizes[size]} />
    </div>
  );

  if (!showTooltip) {
    return badgeIcon;
  }

  return (
    <>
      <ShareBadgeModal
        badge={badge}
        isOpen={isShareModalOpen}
        onClose={() => setIsShareModalOpen(false)}
      />

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {badgeIcon}
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <p className="font-semibold">{badge.metadata.name}</p>
                {showShareButton && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsShareModalOpen(true);
                    }}
                  >
                    <Share2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
            <p className="text-xs text-muted-foreground">{badge.metadata.description}</p>
            {badge.metadata.xp_bonus > 0 && (
              <p className="text-xs text-yellow-500 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-sparkles">
                  <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z" />
                  <path d="M5 3v4" />
                  <path d="M19 17v4" />
                  <path d="M3 5h4" />
                  <path d="M17 19h4" />
                </svg>
                +{badge.metadata.xp_bonus} XP Bonus
              </p>
            )}
            {badge.progress !== undefined && badge.progress_target !== undefined && (
              <div className="w-full mt-1">
                <div className="flex justify-between text-xs mb-1">
                  <span>{badge.progress}</span>
                  <span>{badge.progress_target}</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div
                    className={cn("h-1.5 rounded-full", bgColor)}
                    style={{ width: `${Math.min(100, (badge.progress / badge.progress_target) * 100)}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
    </>
  );
}
