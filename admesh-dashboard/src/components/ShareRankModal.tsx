"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Trophy, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import {
  FacebookShareButton,
  FacebookIcon,
  LinkedinShareButton,
  LinkedinIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon
} from 'next-share';

interface ShareRankModalProps {
  position: number;
  xp: number;
  badgeCount: number;
  isOpen: boolean;
  onClose: () => void;
}

export default function ShareRankModal({ position, xp, badgeCount, isOpen, onClose }: ShareRankModalProps) {
  const [copied, setCopied] = useState(false);
  const shareUrl = typeof window !== 'undefined'
    ? `${window.location.origin}/leaderboard`
    : '';

  const copyToClipboard = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);
    toast.success("Link copied to clipboard!");
    setTimeout(() => setCopied(false), 2000);
  };

  // Format XP with commas
  const formattedXp = xp.toLocaleString();

  // Share text for social media
  const shareTitle = `I'm ranked #${position} on the AdMesh leaderboard!`;
  const shareText = `I've earned ${formattedXp} XP and ${badgeCount} badges. Check out AdMesh:`;
  const hashtags = ['AdMesh', 'Leaderboard', 'Ranking'];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Your Ranking</DialogTitle>
          <DialogDescription>
            Share your position on the AdMesh leaderboard
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-center py-4">
          <div className="bg-amber-500 h-20 w-20 rounded-full flex items-center justify-center text-white">
            <Trophy className="h-10 w-10" />
          </div>
        </div>

        <div className="text-center mb-4">
          <h3 className="font-semibold text-lg">Rank #{position}</h3>
          <p className="text-sm text-muted-foreground">{formattedXp} XP • {badgeCount} Badges</p>
        </div>

        <div className="grid gap-4">
          <div className="grid grid-cols-3 gap-2">
            <div className="flex flex-col items-center justify-center h-24 p-2">
              <TwitterShareButton
                url={shareUrl}
                title={shareTitle}
                hashtags={hashtags}
              >
                <TwitterIcon size={40} round />
                <span className="text-xs mt-2">Twitter</span>
              </TwitterShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <LinkedinShareButton
                url={shareUrl}
                title={shareTitle}
                summary={shareText}
              >
                <LinkedinIcon size={40} round />
                <span className="text-xs mt-2">LinkedIn</span>
              </LinkedinShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <FacebookShareButton
                url={shareUrl}
                quote={`${shareTitle} ${shareText}`}
                hashtag={hashtags[0]}
              >
                <FacebookIcon size={40} round />
                <span className="text-xs mt-2">Facebook</span>
              </FacebookShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <WhatsappShareButton
                url={shareUrl}
                title={`${shareTitle} ${shareText}`}
                separator=" - "
              >
                <WhatsappIcon size={40} round />
                <span className="text-xs mt-2">WhatsApp</span>
              </WhatsappShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <RedditShareButton
                url={shareUrl}
                title={shareTitle}
              >
                <RedditIcon size={40} round />
                <span className="text-xs mt-2">Reddit</span>
              </RedditShareButton>
            </div>

            <div className="flex flex-col items-center justify-center h-24 p-2">
              <EmailShareButton
                url={shareUrl}
                subject={shareTitle}
                body={shareText}
              >
                <EmailIcon size={40} round />
                <span className="text-xs mt-2">Email</span>
              </EmailShareButton>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <div className="grid flex-1 gap-2">
              <Label htmlFor="link" className="sr-only">
                Link
              </Label>
              <Input
                id="link"
                defaultValue={shareUrl}
                readOnly
                className="h-9"
              />
            </div>
            <Button type="submit" size="sm" className="px-3" onClick={copyToClipboard}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              <span className="sr-only">Copy</span>
            </Button>
          </div>
        </div>

        <DialogFooter className="sm:justify-start">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
