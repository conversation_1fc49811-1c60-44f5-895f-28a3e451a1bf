"use client";

import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import BadgeIcon, { BadgeData } from "@/components/BadgeIcon";
import { Award, Facebook, Twitter, Linkedin, Link2 } from "lucide-react";
import { toast } from "sonner";

interface BadgeUnlockedModalProps {
  badge: BadgeData;
  isOpen: boolean;
  onClose: () => void;
}

export default function BadgeUnlockedModal({ badge, isOpen, onClose }: BadgeUnlockedModalProps) {
  const [copied, setCopied] = useState(false);

  if (!badge) return null;

  const shareUrl = `${window.location.origin}/badges/share/${badge.badge_id}`;

  const shareText = `I just unlocked the "${badge.metadata.name}" badge on AdMesh! ${badge.metadata.description}`;

  const shareOnTwitter = () => {
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
    window.open(url, '_blank');
  };

  const shareOnFacebook = () => {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
    window.open(url, '_blank');
  };

  const shareOnLinkedIn = () => {
    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
    window.open(url, '_blank');
  };

  const copyLink = () => {
    navigator.clipboard.writeText(shareUrl).then(() => {
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Award className="h-5 w-5 text-yellow-500" />
            Congratulations!
          </DialogTitle>
          <DialogDescription>
            You&apos;ve unlocked a new badge
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center py-6">
          <div className="mb-4">
            <BadgeIcon badge={badge} size="lg" showTooltip={false} className="h-16 w-16 p-4" />
          </div>
          <h3 className="text-lg font-bold">{badge.metadata.name}</h3>
          <p className="text-center text-muted-foreground mt-2">{badge.metadata.description}</p>

          {badge.metadata.xp_bonus > 0 && (
            <div className="mt-2 text-yellow-500 flex items-center gap-1">
              +{badge.metadata.xp_bonus} XP Bonus
            </div>
          )}
        </div>

        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-3">Share your achievement</h4>
          <div className="flex justify-center gap-3">
            <Button
              variant="outline"
              size="icon"
              onClick={shareOnTwitter}
              className="rounded-full"
            >
              <Twitter className="h-4 w-4" />
              <span className="sr-only">Share on Twitter</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={shareOnFacebook}
              className="rounded-full"
            >
              <Facebook className="h-4 w-4" />
              <span className="sr-only">Share on Facebook</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={shareOnLinkedIn}
              className="rounded-full"
            >
              <Linkedin className="h-4 w-4" />
              <span className="sr-only">Share on LinkedIn</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={copyLink}
              className={`rounded-full ${copied ? 'bg-green-50 text-green-600 border-green-200' : ''}`}
            >
              <Link2 className="h-4 w-4" />
              <span className="sr-only">Copy link</span>
            </Button>
          </div>
        </div>

        <DialogFooter className="flex justify-end gap-2 mt-4">
          <Button
            onClick={onClose}
            className="bg-black hover:bg-gray-800 text-white"
          >
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
