'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { dollarsToCents } from '@/lib/utils'

interface WithdrawModalProps {
  open: boolean
  onClose: () => void
  balance: number
  defaultEmail: string
  onWithdraw?: (amount: number) => Promise<void>
}

export default function WithdrawModal({
  open,
  onClose,
  balance,
  defaultEmail,
  onWithdraw,
}: WithdrawModalProps) {
  const [amount, setAmount] = useState('')
  const [loading, setLoading] = useState(false)

  const handleWithdraw = async () => {
    const value = parseFloat(amount)
    if (isNaN(value) || value <= 0) {
      toast.error('Please enter a valid amount.')
      return
    }
    if (value > balance) {
      toast.error("You don't have enough balance.")
      return
    }

    setLoading(true)
    try {
      // Convert dollars to cents before sending to the backend
      const amountInCents = dollarsToCents(value)
      await onWithdraw?.(amountInCents)
      toast.success('Withdrawal request submitted')
      setAmount('')
      onClose()
    } catch (err) {
      toast.error('Something went wrong. Please try again.')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-sm">
        <DialogHeader>
          <DialogTitle>Withdraw Funds</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Payout Email</Label>
            <Input id="email" value={defaultEmail} disabled />
          </div>

          <div>
            <Label htmlFor="amount">Amount to Withdraw</Label>
            <Input
              id="amount"
              type="number"
              min={0}
              step="0.01"
              placeholder="e.g. 25.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter className="pt-4">
          <Button
            onClick={handleWithdraw}
            disabled={loading || parseFloat(amount || '0') <= 0}
          >
            {loading ? 'Submitting...' : 'Submit Request'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
