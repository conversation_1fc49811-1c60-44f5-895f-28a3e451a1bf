"use client";

import { useEffect } from "react";
import { useBadges } from "@/contexts/badge-context";
import { toast } from "sonner";
import { Award } from "lucide-react";
import { BadgeData } from "@/components/BadgeIcon";

export default function BadgeToastNotification() {
  const { lastAwardedBadge, setLastAwardedBadge } = useBadges();

  // Function to show the share modal
  const handleShare = (badge: BadgeData) => {
    if (typeof window !== 'undefined') {
      const customWindow = window as Window & { __showBadgeShareModal?: (badge: BadgeData) => void };
      if (customWindow.__showBadgeShareModal) {
        customWindow.__showBadgeShareModal(badge);
      }
    }
  };

  // Show toast notification when a new badge is awarded
  useEffect(() => {
    if (lastAwardedBadge) {
      toast.success(
        `You earned a new badge!`,
        {
          description: lastAwardedBadge.metadata.name,
          icon: <Award className="h-4 w-4 text-yellow-500" />,
          action: {
            label: "Share",
            onClick: () => handleShare(lastAwardedBadge),
          },
          duration: 5000,
          onDismiss: () => {
            setLastAwardedBadge(null);
          }
        }
      );
    }
  }, [lastAwardedBadge, setLastAwardedBadge]);

  return null; // This component doesn't render anything
}
