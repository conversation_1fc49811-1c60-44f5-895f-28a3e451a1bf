"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { StepIndicator, BasicInfoStep, ApiCredentialsStep } from "@/components/onboarding";

/**
 * AgentOnboardingContainer - Main container for the agent onboarding process
 * Manages the state and flow between different onboarding steps
 */
export default function AgentOnboardingContainer() {
  const router = useRouter();
  const { user, refreshUser } = useAuth();
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Agent data
  const [agentName, setAgentName] = useState("");
  const [agentType, setAgentType] = useState("");
  const [otherAgentType, setOtherAgentType] = useState("");
  const [agentUrl, setAgentUrl] = useState("");
  const [agentId, setAgentId] = useState("");
  const [testApiKey, setTestApiKey] = useState("");
  const [productionApiKey, setProductionApiKey] = useState("");
  const [apiKeyIds, setApiKeyIds] = useState<{test?: string, production?: string}>({});

  // Load agent data on first render
  useEffect(() => {
    const loadAgentData = async () => {
      if (!user) return;

      try {
        const token = await user.getIdToken();
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/onboarding/data`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();

          // Set agent data from API
          setAgentName(data.name || "");
          setAgentType(data.agent_type || "");
          if (data.agent_type === "Other") {
            setOtherAgentType(data.agent_type || "");
          }
          setAgentUrl(data.agent_url || "");
          setAgentId(data.agent_id || "");

          // Set API keys if available
          if (data.api_keys) {
            setTestApiKey(data.api_keys.test?.key || "");
            setProductionApiKey(data.api_keys.production?.key || "");

            // Set API key IDs if available
            const keyIds: {test?: string, production?: string} = {};
            if (data.api_keys.test?.id) {
              keyIds.test = data.api_keys.test.id;
            }
            if (data.api_keys.production?.id) {
              keyIds.production = data.api_keys.production.id;
            }
            setApiKeyIds(keyIds);
          }

          // Set step based on onboarding status
          if (data.onboarding_status === "integration") {
            setStep(1);

            // If status is integration and test_key exists, automatically update status to completed
            if (data.api_keys && data.api_keys.test?.key) {
              // We have a test key, so we can enable the dashboard button
              console.log("Test key exists, enabling dashboard access");

              // Store the test key and its ID
              setTestApiKey(data.api_keys.test.key);
              if (data.api_keys.test.id) {
                setApiKeyIds(prev => ({ ...prev, test: data.api_keys.test.id }));
              }
            }
          } else if (data.onboarding_status === "completed") {
            // If already completed, redirect to queries
            router.push("/dashboard/agent/queries/");
          }
        } else {
          // If API call fails, generate a random agent ID
          if (!agentId) {
            setAgentId(`agent_${Math.random().toString(36).substring(2, 10)}`);
          }
        }
      } catch (error) {
        console.error("Error loading agent data:", error);
        // If API call fails, generate a random agent ID
        if (!agentId) {
          setAgentId(`agent_${Math.random().toString(36).substring(2, 10)}`);
        }
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadAgentData();
  }, [user, router, agentId]);

  // Handle next step
  const handleNextStep = async () => {
    if (step === 0) {
      // Validate first step
      if (!agentName.trim()) {
        toast.error("Please provide your company name");
        return;
      }

      if (!agentType) {
        toast.error("Please select your product type");
        return;
      }

      if (agentType === "Other" && !otherAgentType.trim()) {
        toast.error("Please specify your product type");
        return;
      }

      if (!agentUrl.trim()) {
        toast.error("Please provide a website URL");
        return;
      }

      setIsLoading(true);

      try {
        if (!user) {
          throw new Error("User not authenticated");
        }

        const token = await user.getIdToken();

        // Prepare agent data
        const agentData = {
          name: agentName.trim(),
          agent_type: agentType === "Other" ? otherAgentType.trim() : agentType,
          agent_url: agentUrl.trim(),
          agent_id: agentId,
          onboarding_status: "integration" // Set to integration for first step
        };

        // Update agent profile
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/update-onboarding`, {
          method: "PATCH",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify(agentData)
        });

        if (!response.ok) {
          throw new Error("Failed to update agent profile");
        }

        // Parse the response
        const responseData = await response.json();

        // If API keys were generated, store them
        if (responseData.api_keys) {
          setTestApiKey(responseData.api_keys.test);
          setProductionApiKey(responseData.api_keys.production);
        }

        // Refresh user data to update onboarding status
        await refreshUser();

        // Move to next step
        setStep(1);
      } catch (error) {
        console.error("Error updating agent profile:", error);
        toast.error("Failed to save your agent profile. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Handle completion of the integration step
  const handleComplete = async () => {
    setIsLoading(true);

    try {
      if (!user) {
        throw new Error("User not authenticated");
      }

      const token = await user.getIdToken();

      // Prepare agent data for completion
      const agentData = {
        agent_id: agentId,
        onboarding_status: "completed", // Set to completed for final step
        api_key_ids: apiKeyIds // Include API key IDs
      };

      // Update agent profile
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/agent/update-onboarding`, {
        method: "PATCH",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify(agentData)
      });

      if (!response.ok) {
        throw new Error("Failed to complete agent onboarding");
      }

      // Parse the response
      const responseData = await response.json();

      // If API keys were generated, store them
      if (responseData.api_keys) {
        setTestApiKey(responseData.api_keys.test);
        setProductionApiKey(responseData.api_keys.production);
      }

      // Refresh user data to update onboarding status
      await refreshUser();

      // Redirect to queries page
      router.push("/dashboard/agent/queries/");
    } catch (error) {
      console.error("Error completing agent onboarding:", error);
      toast.error("Failed to complete your agent setup. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while fetching initial data
  if (isInitialLoading) {
    return (
      <div className="container max-w-2xl mx-auto py-12 px-4">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Loading your agent data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-2xl mx-auto py-12 px-4">
      <Card className="border shadow-lg">
        <CardContent className="pt-6">
          <StepIndicator currentStep={step} totalSteps={2} />

          {step === 0 && (
            <BasicInfoStep
              agentName={agentName}
              setAgentName={setAgentName}
              agentType={agentType}
              setAgentType={setAgentType}
              otherAgentType={otherAgentType}
              setOtherAgentType={setOtherAgentType}
              agentUrl={agentUrl}
              setAgentUrl={setAgentUrl}
              onNext={handleNextStep}
              isLoading={isLoading}
            />
          )}

          {step === 1 && (
            <ApiCredentialsStep
              agentId={agentId}
              testApiKey={testApiKey}
              productionApiKey={productionApiKey}
              onComplete={handleComplete}
              isLoading={isLoading}
              onTestKeyGenerated={(keyInfo) => {
                setTestApiKey(keyInfo.key);
                setApiKeyIds(prev => ({ ...prev, test: keyInfo.id }));
              }}
              onProductionKeyGenerated={(keyInfo) => {
                setProductionApiKey(keyInfo.key);
                setApiKeyIds(prev => ({ ...prev, production: keyInfo.id }));
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
