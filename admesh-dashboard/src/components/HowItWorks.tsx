"use client";

import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, Mouse<PERSON>ointer<PERSON>lick, DollarSign, ArrowR<PERSON> } from "lucide-react";

const steps = [
  {
    title: "Agents Fetch Offers",
    icon: Bo<PERSON>,
    description: "Extensions, GPTs, and AI interfaces pull structured offers matching user intent.",
  },
  {
    title: "Users Convert",
    icon: Mouse<PERSON>ointerClick,
    description: "Users take action — click, buy, or sign up — through trusted agent interfaces.",
  },
  {
    title: "Everyone Gets Paid",
    icon: DollarSign,
    description: "Verified conversions trigger instant earnings for agents, users, and the protocol.",
  },
];

export default function HowItWorks() {
  return (
    <section id="how-it-works" className="w-full bg-gradient-to-b from-blue-100 to-white py-32">
      <div className="container mx-auto px-6 sm:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl mb-4">
            How AdMesh Works
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
            A simple, transparent flow that connects real user intent with monetized outcomes — and shares the value.
          </p>
        </div>

        {/* Steps */}
        <div className="relative max-w-6xl mx-auto">
          {/* Connector line for desktop */}
          <div className="hidden sm:block absolute top-1/2 left-1/2 h-0.5 bg-gray-300 w-[calc(100%-160px)] -translate-x-1/2 -translate-y-1/2 z-0" />

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-10 relative z-10">
            {steps.map((step, idx) => {
              const Icon = step.icon;
              return (
                <div key={idx} className="flex flex-col items-center space-y-6">
                  {/* Step number */}
                  <div className="flex items-center justify-center bg-indigo-600 rounded-full h-16 w-16 border-4 border-indigo-600 text-white font-bold text-lg z-10">
                    {idx + 1}
                  </div>

                  {/* Step Card */}
                  <Card className="w-full bg-white shadow-lg hover:shadow-xl transition-all transform group hover:scale-105">
                    <CardContent className="p-6 text-center">
                      <div className="rounded-full bg-indigo-100 p-4 w-16 h-16 mx-auto mb-6 group-hover:bg-indigo-600 transition-colors">
                        <Icon className="text-indigo-600 h-8 w-8 group-hover:text-white transition-colors" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2 group-hover:text-indigo-600 transition-colors">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 text-sm">{step.description}</p>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Arrow Indicators (Desktop only) */}
          <div className="hidden sm:flex justify-between absolute top-1/2 left-1/2 w-[calc(100%-180px)] -translate-x-1/2 -translate-y-1/2">
            <ArrowRight className="text-indigo-600 h-6 w-6" />
            <ArrowRight className="text-indigo-600 h-6 w-6" />
          </div>
        </div>
      </div>
    </section>
  );
}
