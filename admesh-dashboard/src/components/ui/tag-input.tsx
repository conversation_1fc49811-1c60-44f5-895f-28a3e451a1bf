"use client";

import React, { useState, useRef, KeyboardEvent } from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface KeywordInputProps {
  value: string[];
  onChange: (keywords: string[]) => void;
  placeholder?: string;
  className?: string;
  maxKeywords?: number;
  disabled?: boolean;
}

// Legacy export for backward compatibility
export const TagInput = KeywordInput;

export function KeywordInput({
  value = [],
  onChange,
  placeholder = "Add keywords...",
  className,
  maxKeywords = 10,
  disabled = false,
}: KeywordInputProps) {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;

    // Add keyword on Enter or comma
    if ((e.key === "Enter" || e.key === ",") && inputValue.trim()) {
      e.preventDefault();
      addKeyword(inputValue);
    }
    // Remove last keyword on Backspace if input is empty
    else if (e.key === "Backspace" && !inputValue && value.length > 0) {
      removeKeyword(value.length - 1);
    }
  };

  const addKeyword = (keyword: string) => {
    // Remove commas and trim whitespace
    const cleanedKeyword = keyword.replace(/,/g, "").trim();

    if (!cleanedKeyword) return;

    // Check if keyword already exists or if we've reached the max number of keywords
    if (value.includes(cleanedKeyword) || value.length >= maxKeywords) {
      setInputValue("");
      return;
    }

    const newKeywords = [...value, cleanedKeyword];
    onChange(newKeywords);
    setInputValue("");
  };

  const removeKeyword = (index: number) => {
    if (disabled) return;
    const newKeywords = [...value];
    newKeywords.splice(index, 1);
    onChange(newKeywords);
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  const handleBlur = () => {
    // Add the current input value as a keyword when the input loses focus
    if (inputValue.trim()) {
      addKeyword(inputValue);
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "flex flex-wrap items-center gap-2 p-2 border rounded-md bg-background focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200",
        value.length >= maxKeywords
          ? "border-amber-400"
          : value.length >= maxKeywords - 2
            ? "border-amber-200"
            : "border-input",
        disabled && "bg-muted cursor-not-allowed",
        className
      )}
      onClick={handleContainerClick}
    >
      {value.map((keyword, index) => (
        <div
          key={`${keyword}-${index}`}
          className={cn(
            "flex items-center gap-1 px-2 py-1 rounded-md text-sm transition-all duration-200",
            value.length >= maxKeywords - 2 ? "bg-amber-100 dark:bg-amber-900/30" : "bg-primary/10",
            value.length >= maxKeywords ? "border border-amber-400" : "",
            disabled ? "opacity-50" : "hover:bg-primary/20"
          )}
        >
          <span>{keyword}</span>
          {!disabled && (
            <button
              type="button"
              onClick={() => removeKeyword(index)}
              className="text-muted-foreground hover:text-foreground focus:outline-none"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>
      ))}
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        placeholder={value.length === 0 ? placeholder : ""}
        className={cn(
          "flex-1 min-w-[120px] bg-transparent border-none outline-none text-sm p-1",
          value.length >= maxKeywords && "cursor-not-allowed text-muted-foreground"
        )}
        disabled={disabled || value.length >= maxKeywords}
      />
    </div>
  );
}
