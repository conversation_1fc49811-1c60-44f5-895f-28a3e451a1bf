"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2 } from "lucide-react";
import { toast } from "sonner";


interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  revenue_share_percentage: number;
  attribution_window: string;
  offer_visibility_boost: string;
  monetized_query_usage: string;
  conversion_reports: string;
  support_level: string;
  custom_integrations: boolean;
  multi_agent_teams: boolean;
  features: string[];
}

interface Subscription {
  plan_id: string;
  status: string;
}

// No props needed for this component
export default function AgentSubscriptionPlans() {
  const { user } = useAuth();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState<string | null>(null);

  // Format price from cents to dollars
  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`;
  };

  // Fetch plans and current subscription
  useEffect(() => {
    const fetchPlans = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const token = await user.getIdToken();

        // Fetch available plans
        const plansResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/agent/subscription/plans`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!plansResponse.ok) {
          throw new Error('Failed to fetch plans');
        }

        const plansData = await plansResponse.json();
        setPlans(plansData.plans);

        // Fetch current subscription
        const subscriptionResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/agent/subscription`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!subscriptionResponse.ok) {
          throw new Error('Failed to fetch subscription');
        }

        const subscriptionData = await subscriptionResponse.json();
        setSubscription(subscriptionData.subscription);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching plans:", error);
        toast.error("Failed to load subscription plans");
        setLoading(false);
      }
    };

    fetchPlans();
  }, [user]);

  // Handle plan upgrade
  const handleUpgrade = async (planId: string) => {
    if (!user) return;

    // Don't allow upgrading to current plan
    if (subscription && subscription.plan_id === planId) {
      toast.info("You are already on this plan");
      return;
    }

    // Enterprise plan requires contact
    if (planId === "enterprise") {
      // Open email client with pre-filled email
      window.location.href = "mailto:<EMAIL>?subject=Enterprise%20Plan%20Inquiry&body=I'm%20interested%20in%20learning%20more%20about%20the%20Enterprise%20plan%20for%20agents.";
      return;
    }

    try {
      setUpgrading(planId);
      const token = await user.getIdToken();

      // Call the backend API to update subscription
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/agent/subscription`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ plan_id: planId })
      });

      if (!response.ok) {
        throw new Error('Failed to update subscription');
      }

      const data = await response.json();
      toast.success(`Successfully upgraded to ${data.plan.name} plan!`);

      // Update local state
      setSubscription(data.subscription);
      setUpgrading(null);

      // Refresh the page to show updated data
      window.location.reload();
    } catch (error) {
      console.error("Error upgrading plan:", error);
      toast.error("Failed to upgrade plan");
      setUpgrading(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold mb-4">Choose Your Plan</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {plans.map((plan) => {
          const isCurrentPlan = subscription?.plan_id === plan.id;
          const isEnterprise = plan.id === "enterprise";

          const isPro = plan.id === "pro";
          return (
            <Card
              key={plan.id}
              className={`${isCurrentPlan ? "border-primary" : ""} ${isPro ? "relative shadow-lg" : ""}`}
            >
              {isPro && (
                <div className="absolute -top-3 left-0 right-0 flex justify-center">
                  <Badge className="bg-primary hover:bg-primary">Recommended</Badge>
                </div>
              )}
              <CardHeader>
                <CardTitle>{plan.name}</CardTitle>
                <CardDescription>
                  {plan.price_monthly_cents === 0 && !isEnterprise
                    ? "Free"
                    : isEnterprise
                    ? "Custom Pricing"
                    : `${formatPrice(plan.price_monthly_cents)}/month`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 mb-6">
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <div className="text-sm font-medium mb-2">Revenue Distribution:</div>
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between">
                        <span>Agent:</span>
                        <span className="font-medium text-green-600">{plan.revenue_share_percentage}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>User:</span>
                        <span className="font-medium text-blue-600">10%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>AdMesh:</span>
                        <span className="font-medium text-gray-600">{100 - plan.revenue_share_percentage - 10}%</span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        *When user is present. If no user, their 10% goes to AdMesh.
                      </div>
                    </div>
                  </div>

                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  className="w-full"
                  variant={isCurrentPlan ? "outline" : "default"}
                  disabled={isCurrentPlan || upgrading === plan.id}
                  onClick={() => handleUpgrade(plan.id)}
                >
                  {upgrading === plan.id ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : isCurrentPlan ? (
                    "Current Plan"
                  ) : isEnterprise ? (
                    "Contact Sales"
                  ) : (
                    "Upgrade"
                  )}
                </Button>
              </CardFooter>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
