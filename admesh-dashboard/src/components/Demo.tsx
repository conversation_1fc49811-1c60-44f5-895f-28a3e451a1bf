"use client";

export default function Demo() {
  return (
    <div className="h-full w-full flex flex-col items-center justify-center px-6 py-12 bg-white dark:bg-slate-900">
      <div className="w-full max-w-xl">
        <div className="aspect-video rounded-lg overflow-hidden border bg-gray-100 shadow-sm">
          <video
            src="/demo.mp4"
            autoPlay
            muted
            loop
            playsInline
            className="w-full h-full object-cover"
          />
        </div>
        <p className="text-sm text-center text-muted-foreground mt-4">
          Don’t see anything?{" "}
          <a
            href="https://loom.com"
            target="_blank"
            rel="noopener noreferrer"
            className="underline hover:text-indigo-600"
          >
            Watch on Loom
          </a>
        </p>
      </div>
    </div>
  );
}
