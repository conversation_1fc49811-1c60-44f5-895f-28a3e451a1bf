"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Switch } from "@/components/ui/switch";

export default function CreateProductDialog({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) {
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const [title, setTitle] = useState("");
  const [url, setUrl] = useState("");
  const [description, setDescription] = useState("");
  const [category, setCategory] = useState("");
  const [keywords, setKeywords] = useState("");
  const [audience, setAudience] = useState("");
  const [hasFreeTier, setHasFreeTier] = useState(false);
  const [isAiPowered, setIsAiPowered] = useState(false);
  const [isOpenSource, setIsOpenSource] = useState(false);

  const handleSubmit = async () => {
    if (!user) return;
    setLoading(true);

    try {
      const token = await user.getIdToken();

      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/products/create`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          url,
          description,
          category,
          audience,
          keywords: keywords.split(",").map((keyword) => keyword.trim()),
          has_free_tier: hasFreeTier,
          is_ai_powered: isAiPowered,
          is_open_source: isOpenSource,
          confidence_score: 50.0,
        }),
      });

      if (!res.ok) throw new Error("API error");

      // Reset & close
      setTitle(""); setUrl(""); setDescription("");
      setCategory(""); setKeywords(""); setAudience("");
      setHasFreeTier(false); setIsAiPowered(false); setIsOpenSource(false);
      onClose();
    } catch (err) {
      console.error("❌ Error calling backend:", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add a New Product</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <div>
            <Label>Title</Label>
            <Input value={title} onChange={(e) => setTitle(e.target.value)} required />
          </div>

          <div>
            <Label>Product URL</Label>
            <Input value={url} onChange={(e) => setUrl(e.target.value)} required />
          </div>

          <div>
            <Label>Description</Label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>

          <div>
            <Label>Category</Label>
            <Input value={category} onChange={(e) => setCategory(e.target.value)} />
          </div>

          <div>
            <Label>Audience</Label>
            <Input
              placeholder="e.g. Best for professionals"
              value={audience}
              onChange={(e) => setAudience(e.target.value)}
            />
          </div>

          <div>
            <Label>Keywords (comma separated)</Label>
            <Input value={keywords} onChange={(e) => setKeywords(e.target.value)} />
          </div>

          <div className="flex items-center justify-between">
            <Label>Has Free Tier</Label>
            <Switch checked={hasFreeTier} onCheckedChange={setHasFreeTier} />
          </div>

          <div className="flex items-center justify-between">
            <Label>AI Powered</Label>
            <Switch checked={isAiPowered} onCheckedChange={setIsAiPowered} />
          </div>

          <div className="flex items-center justify-between">
            <Label>Open Source</Label>
            <Switch checked={isOpenSource} onCheckedChange={setIsOpenSource} />
          </div>
        </div>

        <DialogFooter>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Adding..." : "Add Product"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
