export function getIntentUI(intentType: "Compare Products" | "Best for Use Case" | "Budget-Conscious" | "Trial/Demo First") {
    const map: Record<"Compare Products" | "Best for Use Case" | "Budget-Conscious" | "Trial/Demo First", { layout: string; cta: string[] }> = {
      "Compare Products": {
        layout: "side-by-side",
        cta: ["See Key Differences", "Compare Now"],
      },
      "Best for Use Case": {
        layout: "highlight-fit",
        cta: ["Top Match", "Why It's Best"],
      },
      "Budget-Conscious": {
        layout: "value-score",
        cta: ["See Free Plans", "Best Value"],
      },
      "Trial/Demo First": {
        layout: "trial-callout",
        cta: ["Try Free Now", "Explore Sandbox"],
      },
    };
  
    return map[intentType] || {
      layout: "standard",
      cta: ["Learn More"],
    };
  }
  