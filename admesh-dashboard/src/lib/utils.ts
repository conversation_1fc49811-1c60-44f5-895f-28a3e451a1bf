import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Converts a dollar amount to cents for backend storage
 * @param amount - The amount in dollars (can be string or number)
 * @returns The amount in cents as an integer
 */
export function dollarsToCents(amount: string | number): number {
  // Handle empty strings or undefined
  if (amount === '' || amount === undefined || amount === null) {
    return 0;
  }

  // Convert to number if it's a string
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Check if it's a valid number
  if (isNaN(numAmount)) {
    return 0;
  }

  // Convert to cents and round to avoid floating point issues
  return Math.round(numAmount * 100);
}

/**
 * Converts cents to dollars for display
 * @param cents - The amount in cents
 * @returns The amount in dollars as a number with 2 decimal places
 */
export function centsToDollars(cents: number): number {
  if (cents === undefined || cents === null || isNaN(cents)) {
    return 0;
  }

  // Convert cents to dollars with 2 decimal places
  return parseFloat((cents / 100).toFixed(2));
}

/**
 * Formats a currency value for display
 * @param amount - The amount in dollars
 * @param currency - The currency code (default: USD)
 * @returns Formatted currency string (e.g., "$10.00")
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '$0.00';
  }

  // Format the amount with 2 decimal places
  const formattedAmount = amount.toFixed(2);

  // Add the appropriate currency symbol
  switch (currency) {
    case 'USD':
      return `$${formattedAmount}`;
    case 'EUR':
      return `€${formattedAmount}`;
    case 'GBP':
      return `£${formattedAmount}`;
    default:
      return `${formattedAmount} ${currency}`;
  }
}
