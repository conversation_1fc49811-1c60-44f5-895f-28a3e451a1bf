/**
 * API helper functions for making authenticated requests to the backend API
 * These functions work with Firebase Authentication
 */

/**
 * Fetch with authentication headers
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns Response from the fetch request
 */
export async function fetchWithAuth(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  // Make the fetch request with the provided options
  // The Authorization header should be passed in the options
  const response = await fetch(url, options);
  return response;
}

/**
 * Fetch JSON data with authentication headers
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns JSON data from the response
 */
export async function fetchJsonWithAuth<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await fetchWithAuth(url, {
    ...options,
    headers: {
      ...options.headers,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.detail || `API request failed with status ${response.status}`
    );
  }

  return response.json();
}
