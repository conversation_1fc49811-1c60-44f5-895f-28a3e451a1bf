import { toast } from "sonner";
import { User } from "firebase/auth";

/**
 * <PERSON><PERSON> test conversion for an offer
 * @param offerId - The ID of the offer to test
 * @param redirectUrl - The URL to redirect to after the test
 * @param user - The current user (from Firebase auth)
 * @param productId - Optional product ID
 * @param setIsTestingConversion - Optional state setter for loading state
 * @returns Promise<boolean> - Whether the test conversion was initiated successfully
 */
export async function handleTestConversion({
  offerId,
  redirectUrl,
  user,
  productId = "",
  setIsTestingConversion = () => {},
}: {
  offerId: string;
  redirectUrl: string;
  user: User | null;
  productId?: string;
  setIsTestingConversion?: (isLoading: boolean) => void;
}): Promise<boolean> {
  if (!user) {
    toast.error("You must be logged in to test conversions");
    return false;
  }

  try {
    setIsTestingConversion(true);

    // If the redirect URL is empty, show an error and return
    if (!redirectUrl) {
      toast.error("Please enter a redirect URL in the tracking section first");
      return false;
    }

    // Get the API base URL
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';

    // Test conversion parameters
    const rec_id = 'test_rec';
    const session_id = 'test_session';
    const agent_id = 'test_agent';
    const user_id = 'test_user';

    // Create the final URL that will record the click and redirect to the test conversion page
    const finalUrl = `${apiBaseUrl}/click/r/${offerId}?test=true&product_id=${encodeURIComponent(productId)}&rec_id=${encodeURIComponent(rec_id)}&session_id=${encodeURIComponent(session_id)}&agent_id=${encodeURIComponent(agent_id)}&redirect_url=${encodeURIComponent(redirectUrl)}&user_id=${encodeURIComponent(user_id)}`;

    // Open the URL in a new tab
    const newWindow = window.open(finalUrl, '_blank');

    // Check if the window was successfully opened
    if (!newWindow) {
      toast.error('Failed to open test conversion window. Please check if pop-up blockers are enabled.');
      return false;
    } else {
      toast.success('Test click initiated. Please complete the conversion in the new tab.');
      return true;
    }
  } catch (error) {
    console.error('Error in test conversion flow:', error);
    toast.error('Failed to start test conversion flow');
    return false;
  } finally {
    setIsTestingConversion(false);
  }
}

/**
 * Checks for test conversions for an offer
 * @param offerId - The ID of the offer to check
 * @param user - The current user (from Firebase auth)
 * @param setIsCheckingConversions - Optional state setter for loading state
 * @param setTestConversionExists - Optional state setter for test conversion existence
 * @param setTestConversionCount - Optional state setter for test conversion count
 * @returns Promise<{exists: boolean, count: number}> - Whether test conversions exist and how many
 */
export async function checkTestConversions({
  offerId,
  user,
  setIsCheckingConversions = () => {},
  setTestConversionExists = () => {},
  setTestConversionCount = () => {},
}: {
  offerId: string;
  user: User | null;
  setIsCheckingConversions?: (isLoading: boolean) => void;
  setTestConversionExists?: (exists: boolean) => void;
  setTestConversionCount?: (count: number) => void;
}): Promise<{exists: boolean, count: number}> {
  if (!user) {
    toast.error("You must be logged in to check conversions");
    return { exists: false, count: 0 };
  }

  try {
    setIsCheckingConversions(true);
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';
    const token = await user.getIdToken();

    const response = await fetch(`${apiBaseUrl}/conversion/offers/${offerId}/conversions/test`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.detail || "Failed to check for test conversions";
      throw new Error(errorMessage);
    }

    const data = await response.json();

    // Check if test conversions exist
    if (data.status === "success" && data.test_conversions && data.test_conversions > 0) {
      setTestConversionExists(true);
      setTestConversionCount(data.test_conversions);
      toast.success(`Found ${data.test_conversions} test conversion(s)!`, {
        duration: 5000,
      });
      return { exists: true, count: data.test_conversions };
    } else {
      setTestConversionExists(false);
      toast.error("No test conversions found. Please complete a test conversion first.");
      return { exists: false, count: 0 };
    }
  } catch (error) {
    console.error("Error checking test conversions:", error);
    toast.error(error instanceof Error ? error.message : "Failed to check test conversions");
    setTestConversionExists(false);
    return { exists: false, count: 0 };
  } finally {
    setIsCheckingConversions(false);
  }
}
