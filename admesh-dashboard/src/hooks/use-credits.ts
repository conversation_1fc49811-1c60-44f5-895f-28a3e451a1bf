import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';

interface UseCreditsReturn {
  credits: number | null;
  xp: number | null;
  lifetimeXp: number | null;
  loading: boolean;
  error: string | null;
  refreshCredits: () => Promise<void>;
  updateCredits: (newCredits: number) => Promise<boolean>;
}

export function useCredits(): UseCreditsReturn {
  const { user, role } = useAuth();
  const [credits, setCredits] = useState<number | null>(null);
  const [xp, setXp] = useState<number | null>(null);
  const [lifetimeXp, setLifetimeXp] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCredits = useCallback(async () => {
    if (!user || role !== 'user') {
      setCredits(null);
      setXp(null);
      setLifetimeXp(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credits`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch credits');
      }

      const data = await response.json();
      // Ensure credits is a number
      const creditsValue = data.credits !== undefined && data.credits !== null ? Number(data.credits) : null;
      setCredits(creditsValue);

      // Set XP data from the response
      const xpValue = data.available_xp !== undefined && data.available_xp !== null ? Number(data.available_xp) : null;
      setXp(xpValue);

      const lifetimeXpValue = data.lifetime_xp !== undefined && data.lifetime_xp !== null ? Number(data.lifetime_xp) : null;
      setLifetimeXp(lifetimeXpValue);
    } catch (err) {
      console.error('Error fetching credits:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [user, role]);

  const updateCredits = useCallback(async (newCredits: number): Promise<boolean> => {
    if (!user || role !== 'user') return false;

    try {
      setLoading(true);
      setError(null);

      const token = await user.getIdToken();
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user/credit-management/credits/update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ credits: newCredits }),
      });

      if (!response.ok) {
        throw new Error('Failed to update credits');
      }

      const data = await response.json();
      // Ensure credits is a number
      const creditsValue = data.credits !== undefined && data.credits !== null ? Number(data.credits) : null;
      setCredits(creditsValue);
      return true;
    } catch (err) {
      console.error('Error updating credits:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user, role]);

  // Initial fetch
  useEffect(() => {
    if (role === 'user') {
      fetchCredits();
    }
  }, [fetchCredits, role]);

  return {
    credits,
    xp,
    lifetimeXp,
    loading,
    error,
    refreshCredits: fetchCredits,
    updateCredits,
  };
}
