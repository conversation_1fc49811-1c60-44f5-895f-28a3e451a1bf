"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "./use-auth";

export interface Subscription {
  plan_id: string;
  billing_cycle: "monthly" | "annual" | "free";
  status: string;
  stripe_subscription_id?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  promo_credit_applied?: boolean;
  promo_credit_amount_cents?: number;
  product_listings_used?: number;
  usage?: {
    product_listings?: number;
  };
}

export interface Plan {
  id: string;
  name: string;
  price_monthly_cents: number;
  price_annual_cents?: number;
  billing_cycle: "monthly" | "annual" | "free";
  product_listings_limit: number;
  active_offers_per_product_limit: number;
  promo_credit_cents: number;
  visibility_boost: string;
  analytics_level: string;
  conversion_reports: string;
  agent_match_priority: string;
  badge_type?: string;
  support_level: string;
  agent_outreach_tools: boolean;
  multi_user_access: boolean;
  multi_user_limit: number;
  cpa_optimization: boolean;
  features: string[];
  keyword_limit: number; // Added keyword limit property
}

export function useSubscription() {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchSubscription = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/subscription`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (!response.ok) {
        // Check if this is a 404 (Not Found) error, which is expected during onboarding
        if (response.status === 404) {
          console.info(
            "No subscription found. User may be in onboarding process."
          );
          // Don't throw an error, just use the default free plan
          throw new Error("No subscription found");
        } else {
          console.warn(
            `Subscription API returned status ${response.status}: ${response.statusText}`
          );
          // Don't throw an error, just use the default free plan
          throw new Error("Failed to fetch subscription data");
        }
      }

      // Check if the response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error("Non-JSON response from subscription endpoint:", text);
        throw new Error("Server returned non-JSON response for subscription");
      }

      const data = await response.json();

      // Add keyword limits based on plan
      if (data.plan) {
        const planWithKeywordLimits = {
          ...data.plan,
          keyword_limit: getKeywordLimitByPlanId(data.plan.id),
        };
        setPlan(planWithKeywordLimits);
      }

      setSubscription(data.subscription);
    } catch (error) {
      // Check if this is a "No subscription found" error, which is expected during onboarding
      if (error instanceof Error && error.message === "No subscription found") {
        console.info("Using default free plan for user in onboarding process");
      } else {
        console.error("Error fetching subscription:", error);
      }

      // Set default free plan with keyword limit
      const defaultFreePlan: Plan = {
        id: "free",
        name: "Free",
        price_monthly_cents: 0,
        billing_cycle: "free",
        product_listings_limit: 1,
        active_offers_per_product_limit: 1,
        promo_credit_cents: 5000,
        visibility_boost: "Basic",
        analytics_level: "Basic",
        conversion_reports: "Basic",
        agent_match_priority: "Normal",
        badge_type: undefined,
        support_level: "Community",
        agent_outreach_tools: false,
        multi_user_access: false,
        multi_user_limit: 0,
        cpa_optimization: false,
        features: [],
        keyword_limit: 10, // Default free plan keyword limit
      };

      setPlan(defaultFreePlan);

      // Also set a default subscription with promo credit details
      const defaultSubscription: Subscription = {
        plan_id: "free",
        billing_cycle: "free",
        status: "active",
        promo_credit_applied: false,
        promo_credit_amount_cents: 5000, // $50 promo credit
        product_listings_used: 0,
        usage: {
          product_listings: 0,
        },
        // Add additional fields mentioned in the error message
        // current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
      };
      setSubscription(defaultSubscription);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Helper function to get keyword limit based on plan ID
  const getKeywordLimitByPlanId = (planId: string): number => {
    // Handle annual plans by removing _annual suffix
    const basePlanId = planId.replace("_annual", "");

    switch (basePlanId) {
      case "free":
        return 10;
      case "starter":
        return 20;
      case "growth":
        return 30;
      default:
        return 10; // Default to free plan limit
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  return {
    subscription,
    plan,
    loading,
    refreshSubscription: fetchSubscription,
    getKeywordLimit: () => plan?.keyword_limit || 10, // Default to 10 if plan not loaded
  };
}
