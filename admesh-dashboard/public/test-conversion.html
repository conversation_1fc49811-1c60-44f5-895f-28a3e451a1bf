<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AdMesh Conversion Test</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .container {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #2563eb;
        margin-top: 0;
      }
      .success-icon {
        color: #10b981;
        font-size: 48px;
        margin-bottom: 20px;
      }
      .button {
        background-color: #2563eb;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.2s;
      }
      .button:hover {
        background-color: #1d4ed8;
      }
      .note {
        margin-top: 20px;
        padding: 15px;
        background-color: #f0f9ff;
        border-left: 4px solid #3b82f6;
        border-radius: 4px;
      }
      .pixel-container {
        margin-top: 30px;
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
      }
      #status {
        margin-top: 20px;
        padding: 10px;
        border-radius: 4px;
        display: none;
      }
      .success {
        background-color: #dcfce7;
        color: #166534;
      }
      .error {
        background-color: #fee2e2;
        color: #b91c1c;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>AdMesh Conversion Test</h1>
      <p>
        This page simulates a conversion event for testing your AdMesh pixel
        tracking implementation.
      </p>

      <div id="form">
        <h2>Test a Conversion</h2>
        <p>Enter the Offer ID you want to test:</p>
        <input
          type="text"
          id="offerId"
          placeholder="Enter Offer ID"
          style="padding: 8px; width: 300px; margin-right: 10px"
        />
        <button class="button" onclick="fireConversion()">
          Fire Conversion Pixel
        </button>

        <div id="forceFireContainer" style="margin-top: 15px">
          <label
            style="
              display: flex;
              align-items: center;
              font-size: 14px;
              color: #666;
            "
          >
            <input type="checkbox" id="forceFire" style="margin-right: 8px" />
            Force fire conversion (override verification checks)
          </label>
        </div>

        <div id="status"></div>

        <div class="note">
          <strong>Note:</strong> This will create a test conversion in your
          AdMesh dashboard. Test conversions are marked separately and don't
          count toward your budget spending.
        </div>
      </div>

      <div id="pixelInfo" class="pixel-container" style="display: none">
        <h3>Pixel Information</h3>
        <p>The following pixel has been fired:</p>
        <code id="pixelCode"></code>

        <div style="margin-top: 20px">
          <h4 style="font-size: 14px; margin-bottom: 8px">URL Parameters</h4>
          <ul
            id="pixelParams"
            style="font-size: 13px; color: #666; margin-left: 20px"
          ></ul>
        </div>
      </div>
    </div>

    <script>
      let clickVerified = false;
      let isVerifying = false;

      // Pre-fill offer ID from URL parameters and verify click if available
      document.addEventListener("DOMContentLoaded", function () {
        const urlParams = new URLSearchParams(window.location.search);
        const offerIdParam = urlParams.get("offer_id");
        if (offerIdParam) {
          document.getElementById("offerId").value = offerIdParam;
        }

        // Get clickId parameter (support both old and new formats)
        const clickIdParam = urlParams.get("utm_click_id") || urlParams.get("clickId");
        if (clickIdParam && offerIdParam) {
          // Display click ID information
          const clickInfoEl = document.createElement("div");
          clickInfoEl.id = "clickInfo";
          clickInfoEl.style.marginTop = "15px";
          clickInfoEl.style.padding = "12px";
          clickInfoEl.style.backgroundColor = "#EFF6FF";
          clickInfoEl.style.borderRadius = "6px";
          clickInfoEl.style.border = "1px solid #DBEAFE";

          clickInfoEl.innerHTML = `
                    <div style="display: flex; align-items: center;">
                        <div id="clickStatusDot" style="width: 8px; height: 8px; border-radius: 50%; background-color: #F59E0B; margin-right: 8px;"></div>
                        <span style="font-size: 14px; font-weight: 500;">
                            Click ID: <span style="font-family: monospace;">${clickIdParam}</span>
                        </span>
                    </div>
                    <p id="clickStatusText" style="font-size: 12px; color: #6B7280; margin-top: 4px; margin-bottom: 0;">
                        ⚠️ Click not verified yet.
                    </p>
                `;

          document
            .getElementById("form")
            .insertBefore(
              clickInfoEl,
              document.getElementById("forceFireContainer")
            );

          // Verify the click
          verifyClick(offerIdParam, clickIdParam);
        }
      });

      // Function to verify if a click exists
      async function verifyClick(offerId, clickId) {
        if (!offerId || !clickId) return;

        isVerifying = true;
        updateButtonState();

        try {
          // Update status to verifying
          const clickStatusText = document.getElementById("clickStatusText");
          if (clickStatusText) {
            clickStatusText.textContent = "⏳ Verifying click...";
          }

          // For test clicks from the analytics page, we verify them
          // since they're recorded in the database via the /r/{ad_id} endpoint
          if (clickId.startsWith("test_")) {
            console.log(`Verifying test click: ${clickId}`);

            // In a real implementation, we would call an API to verify the click exists
            // For now, we'll just simulate a successful verification
            // This would be replaced with an actual API call in production

            // Simulate API call delay for a more realistic experience
            await new Promise((resolve) => setTimeout(resolve, 1500));

            // Set click as verified
            clickVerified = true;

            // Update UI
            const clickStatusDot = document.getElementById("clickStatusDot");
            if (clickStatusDot) {
              clickStatusDot.style.backgroundColor = "#10B981";
            }

            if (clickStatusText) {
              clickStatusText.textContent =
                "✅ Test click verified. Ready to fire conversion.";
            }

            const statusEl = document.getElementById("status");
            statusEl.textContent =
              "Test click verified successfully. You can now fire the conversion pixel.";
            statusEl.className = "success";
            statusEl.style.display = "block";

            isVerifying = false;
            updateButtonState();
            return;
          }

          // For non-test clicks, we would verify them differently
          // Simulate API call delay
          await new Promise((resolve) => setTimeout(resolve, 1500));

          // Set click as verified
          clickVerified = true;

          // Update UI
          const clickStatusDot = document.getElementById("clickStatusDot");
          if (clickStatusDot) {
            clickStatusDot.style.backgroundColor = "#10B981";
          }

          if (clickStatusText) {
            clickStatusText.textContent =
              "✅ Click verified. Ready to fire conversion.";
          }

          const statusEl = document.getElementById("status");
          statusEl.textContent =
            "Click verified successfully. You can now fire the conversion pixel.";
          statusEl.className = "success";
          statusEl.style.display = "block";
        } catch (error) {
          console.error("Error verifying click:", error);
          const statusEl = document.getElementById("status");
          statusEl.textContent =
            "Failed to verify click. Please try again or contact support.";
          statusEl.className = "error";
          statusEl.style.display = "block";
        } finally {
          isVerifying = false;
          updateButtonState();
        }
      }

      function updateButtonState() {
        const button = document.querySelector("button.button");
        if (button) {
          button.disabled = isVerifying;
          button.textContent = isVerifying
            ? "Verifying Click..."
            : "Fire Conversion Pixel";
        }
      }

      function fireConversion() {
  const offerId = document.getElementById("offerId").value.trim();
  const statusEl = document.getElementById("status");
  const pixelInfoEl = document.getElementById("pixelInfo");
  const pixelCodeEl = document.getElementById("pixelCode");
  const forceFireEl = document.getElementById("forceFire");

  if (!offerId) {
    statusEl.textContent = "Please enter a valid Offer ID";
    statusEl.className = "error";
    statusEl.style.display = "block";
    return;
  }

  // Get URL parameters (support both old and new formats)
  const urlParams = new URLSearchParams(window.location.search);
  const clickId = urlParams.get("utm_click_id") || urlParams.get("clickId");
  const isAdmesh = urlParams.get("utm_source") === "admesh" || urlParams.get("admesh") === "true";
  const adId = urlParams.get("ad_id") || offerId; // fallback to offerId
  const productId = urlParams.get("product_id");
  const offerIdParam = urlParams.get("offer_id") || offerId;

  // Only fire the conversion if utm_source=admesh or admesh=true or forced
  if (!isAdmesh && !forceFireEl.checked) {
    statusEl.textContent =
      'Conversion not fired: "utm_source=admesh" parameter missing. Use "Force fire" to override.';
    statusEl.className = "error";
    statusEl.style.display = "block";
    return;
  }

  // Build pixel URL
  let pixelUrl = `https://api.useadmesh.com/conversion/pixel?ad_id=${encodeURIComponent(adId)}&test=true&verified=true`;

  if (clickId) pixelUrl += `&utm_click_id=${encodeURIComponent(clickId)}`;
  if (productId) pixelUrl += `&product_id=${encodeURIComponent(productId)}`;
  if (offerIdParam) pixelUrl += `&offer_id=${encodeURIComponent(offerIdParam)}`;

  // Fire pixel
  const pixel = document.createElement("img");
  pixel.src = pixelUrl;
  pixel.width = 1;
  pixel.height = 1;
  pixel.style.display = "none";
  document.body.appendChild(pixel);

  // Show result
  statusEl.textContent = "✅ Conversion pixel fired! Check dashboard for logs.";
  statusEl.className = "success";
  statusEl.style.display = "block";

  pixelCodeEl.textContent = `<img src="${pixelUrl}" width="1" height="1" style="display:none" />`;
  pixelInfoEl.style.display = "block";

  // Show parameters
  const paramsEl = document.getElementById("pixelParams");
  paramsEl.innerHTML = `
    <li><strong>ad_id:</strong> ${adId}</li>
    <li><strong>offer_id:</strong> ${offerIdParam}</li>
    ${clickId ? `<li><strong>utm_click_id:</strong> ${clickId}</li>` : ""}
    ${productId ? `<li><strong>product_id:</strong> ${productId}</li>` : ""}
    <li><strong>test:</strong> true</li>
    <li><strong>verified:</strong> true</li>
    <li><strong>utm_source:</strong> admesh</li>
  `;

  console.log("✅ Pixel fired:", pixelUrl);
}

    </script>
  </body>
</html>
