/**
 * Frontend-Backend Integration Test Script
 * Tests the complete payout distribution system integration
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class IntegrationTester {
  constructor() {
    this.testResults = [];
    this.authToken = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, type };
    this.testResults.push(logEntry);
    
    const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    console.log(`${emoji} [${timestamp}] ${message}`);
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` }),
      ...options.headers
    };

    try {
      const response = await fetch(url, { ...options, headers });
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.message || 'Request failed'}`);
      }
      
      return data;
    } catch (error) {
      this.log(`Request failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async testAgentSubscriptionPlans() {
    this.log('Testing Agent Subscription Plans API...');
    
    try {
      const data = await this.makeRequest('/agent/subscription/plans');
      
      // Validate plans structure
      if (!data.plans || !Array.isArray(data.plans)) {
        throw new Error('Plans data is not an array');
      }

      const expectedPlans = ['free', 'pro', 'enterprise'];
      const actualPlans = data.plans.map(p => p.id);
      
      for (const expectedPlan of expectedPlans) {
        if (!actualPlans.includes(expectedPlan)) {
          throw new Error(`Missing plan: ${expectedPlan}`);
        }
      }

      // Validate revenue share percentages
      const revenueShares = {
        'free': 60,
        'pro': 70,
        'enterprise': 80
      };

      for (const plan of data.plans) {
        const expectedShare = revenueShares[plan.id];
        if (plan.revenue_share_percentage !== expectedShare) {
          throw new Error(`Incorrect revenue share for ${plan.id}: expected ${expectedShare}%, got ${plan.revenue_share_percentage}%`);
        }
      }

      this.log('Agent subscription plans API test passed', 'success');
      return data;
    } catch (error) {
      this.log(`Agent subscription plans test failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async testCurrentSubscription() {
    this.log('Testing Current Agent Subscription API...');
    
    try {
      const data = await this.makeRequest('/agent/subscription');
      
      // Validate subscription structure
      if (!data.subscription || !data.plan) {
        throw new Error('Missing subscription or plan data');
      }

      // Validate plan details
      const plan = data.plan;
      if (!plan.revenue_share_percentage || !plan.features) {
        throw new Error('Invalid plan structure');
      }

      this.log(`Current subscription: ${plan.name} (${plan.revenue_share_percentage}% revenue share)`, 'success');
      return data;
    } catch (error) {
      this.log(`Current subscription test failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async testRevenueDistributionCalculation() {
    this.log('Testing Revenue Distribution Calculation...');
    
    const testCases = [
      {
        name: 'Free Agent with User',
        agentShare: 60,
        userPresent: true,
        expected: { agent: 60, user: 10, admesh: 30 }
      },
      {
        name: 'Free Agent without User',
        agentShare: 60,
        userPresent: false,
        expected: { agent: 60, user: 0, admesh: 40 }
      },
      {
        name: 'Pro Agent with User',
        agentShare: 70,
        userPresent: true,
        expected: { agent: 70, user: 10, admesh: 20 }
      },
      {
        name: 'Enterprise Agent with User',
        agentShare: 80,
        userPresent: true,
        expected: { agent: 80, user: 10, admesh: 10 }
      }
    ];

    for (const testCase of testCases) {
      const { agent, user, admesh } = testCase.expected;
      const total = agent + user + admesh;
      
      if (total !== 100) {
        throw new Error(`${testCase.name}: Total percentage is ${total}%, expected 100%`);
      }
      
      this.log(`${testCase.name}: ${agent}%/${user}%/${admesh}% = ${total}%`, 'success');
    }

    this.log('Revenue distribution calculation tests passed', 'success');
  }

  async testPayoutObjectStructure() {
    this.log('Testing Payout Object Structure...');
    
    const mockPayout = {
      amount: 1000,  // $10.00 in cents
      currency: 'USD',
      model: 'CPA'
    };

    // Validate required fields
    const requiredFields = ['amount', 'currency', 'model'];
    for (const field of requiredFields) {
      if (!(field in mockPayout)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate data types
    if (typeof mockPayout.amount !== 'number' || mockPayout.amount <= 0) {
      throw new Error('Amount must be a positive number');
    }

    if (typeof mockPayout.currency !== 'string' || mockPayout.currency !== 'USD') {
      throw new Error('Currency must be USD string');
    }

    if (typeof mockPayout.model !== 'string' || mockPayout.model !== 'CPA') {
      throw new Error('Model must be CPA string');
    }

    this.log('Payout object structure validation passed', 'success');
  }

  async testBudgetTrackingStructure() {
    this.log('Testing Budget Tracking Structure...');
    
    const mockTotalSpent = {
      test: 500,        // $5.00 in test conversions
      production: 1500, // $15.00 in production conversions
      total: 2000       // $20.00 total
    };

    // Validate structure
    const requiredFields = ['test', 'production', 'total'];
    for (const field of requiredFields) {
      if (!(field in mockTotalSpent)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate calculations
    const calculatedTotal = mockTotalSpent.test + mockTotalSpent.production;
    if (calculatedTotal !== mockTotalSpent.total) {
      throw new Error(`Total mismatch: ${calculatedTotal} !== ${mockTotalSpent.total}`);
    }

    // Validate non-negative values
    for (const [field, value] of Object.entries(mockTotalSpent)) {
      if (value < 0) {
        throw new Error(`${field} cannot be negative: ${value}`);
      }
    }

    this.log('Budget tracking structure validation passed', 'success');
  }

  async testConversionFlow() {
    this.log('Testing Complete Conversion Flow...');
    
    // Mock conversion data
    const conversionData = {
      offer_id: 'test_offer_123',
      agent_id: 'test_agent_456',
      user_id: 'test_user_789',
      conversion_value: 1000, // $10.00
      currency: 'USD',
      is_test: true
    };

    // Test conversion creation (mock)
    this.log('Simulating conversion creation...', 'info');
    
    // Expected earnings calculation for free agent with user
    const expectedDistribution = {
      agent_earning: 600,  // 60% of $10.00
      user_earning: 100,   // 10% of $10.00
      protocol_fee: 300    // 30% of $10.00
    };

    // Validate distribution
    const total = expectedDistribution.agent_earning + expectedDistribution.user_earning + expectedDistribution.protocol_fee;
    if (total !== conversionData.conversion_value) {
      throw new Error(`Distribution total ${total} !== conversion value ${conversionData.conversion_value}`);
    }

    this.log(`Expected distribution: Agent $${expectedDistribution.agent_earning/100}, User $${expectedDistribution.user_earning/100}, Protocol $${expectedDistribution.protocol_fee/100}`, 'success');
  }

  async runAllTests() {
    this.log('🚀 Starting Frontend-Backend Integration Tests...');
    
    const tests = [
      () => this.testAgentSubscriptionPlans(),
      () => this.testCurrentSubscription(),
      () => this.testRevenueDistributionCalculation(),
      () => this.testPayoutObjectStructure(),
      () => this.testBudgetTrackingStructure(),
      () => this.testConversionFlow()
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        await test();
        passed++;
      } catch (error) {
        failed++;
        this.log(`Test failed: ${error.message}`, 'error');
      }
    }

    this.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`, failed === 0 ? 'success' : 'warning');
    
    if (failed === 0) {
      this.log('🎉 All integration tests passed! Frontend-backend synchronization is working correctly.', 'success');
    } else {
      this.log('⚠️ Some tests failed. Please review the errors above.', 'warning');
    }

    return { passed, failed, results: this.testResults };
  }
}

// Export for use in browser or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IntegrationTester;
} else if (typeof window !== 'undefined') {
  window.IntegrationTester = IntegrationTester;
}

// Auto-run if called directly
if (typeof require !== 'undefined' && require.main === module) {
  const tester = new IntegrationTester();
  tester.runAllTests().catch(console.error);
}
