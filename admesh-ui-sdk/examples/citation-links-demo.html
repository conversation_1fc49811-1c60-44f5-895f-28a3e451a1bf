<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdMesh Citation Links Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .demo-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .citation {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s;
        }
        .citation:hover {
            background: #bfdbfe;
            border-color: #60a5fa;
            transform: scale(1.1);
        }
        .references {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #d1d5db;
        }
        .reference-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .reference-link {
            color: #1d4ed8;
            text-decoration: none;
            font-weight: 600;
        }
        .reference-link:hover {
            text-decoration: underline;
        }
        .note {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔗 AdMesh Citation Links Demo</h1>
    
    <div class="note">
        <strong>Note:</strong> This demo shows how citation links work in the AdMesh UI SDK. In the actual React components, these links are automatically tracked and open the product pages.
    </div>

    <div class="demo-section">
        <h2>Citation-Based Product Recommendations</h2>
        <p>
            Based on your requirements for a CRM system for your remote team, I'd recommend considering several excellent options. 
            <a href="https://useadmesh.com/track?ad_id=hubspot-123" target="_blank" class="citation">1</a> 
            offers a comprehensive free tier that's perfect for getting started, with excellent collaboration features that work well for remote teams.
        </p>
        
        <p>
            For larger enterprises, 
            <a href="https://useadmesh.com/track?ad_id=salesforce-456" target="_blank" class="citation">2</a> 
            provides the most comprehensive feature set with extensive customization options. While it has a steeper learning curve, it's the industry standard for enterprise CRM solutions.
        </p>
        
        <p>
            If you prefer simplicity, 
            <a href="https://useadmesh.com/track?ad_id=pipedrive-789" target="_blank" class="citation">3</a> 
            focuses specifically on sales pipeline management with an intuitive interface that's easy for teams to adopt quickly.
        </p>

        <div class="references">
            <h3>📚 References</h3>
            
            <div class="reference-item">
                <span class="citation">1</span>
                <a href="https://useadmesh.com/track?ad_id=hubspot-123" target="_blank" class="reference-link">HubSpot CRM</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Perfect for remote teams with excellent collaboration features and robust automation capabilities
                </p>
            </div>
            
            <div class="reference-item">
                <span class="citation">2</span>
                <a href="https://useadmesh.com/track?ad_id=salesforce-456" target="_blank" class="reference-link">Salesforce</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Enterprise-grade CRM with extensive customization and integration capabilities
                </p>
            </div>
            
            <div class="reference-item">
                <span class="citation">3</span>
                <a href="https://useadmesh.com/track?ad_id=pipedrive-789" target="_blank" class="reference-link">Pipedrive</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Simple and intuitive CRM focused on sales pipeline management
                </p>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>🎯 How Citation Links Work</h2>
        <ul>
            <li><strong>Automatic Tracking:</strong> Every click is automatically tracked with AdMesh analytics</li>
            <li><strong>Smart Insertion:</strong> Citations are automatically inserted where products are mentioned</li>
            <li><strong>Interactive Tooltips:</strong> Hover over citations to see product details</li>
            <li><strong>Direct Navigation:</strong> Click citations or product names to visit product pages</li>
            <li><strong>Multiple Styles:</strong> Support for numbered (1), bracketed [1], and superscript¹ formats</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>💻 React Component Usage</h2>
        <pre style="background: #1f2937; color: #f3f4f6; padding: 15px; border-radius: 6px; overflow-x: auto;"><code>import { AdMeshCitationUnit } from 'admesh-ui-sdk';

&lt;AdMeshCitationUnit
  recommendations={recommendations}
  conversationText="Based on your needs, I recommend HubSpot CRM..."
  citationStyle="numbered"
  showCitationList={true}
  onRecommendationClick={(adId, link) => {
    // Automatic tracking and link opening
    console.log(`Opening: ${link}`);
  }}
/&gt;</code></pre>
    </div>

    <script>
        // Add click tracking for demo
        document.querySelectorAll('.citation, .reference-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = e.target.href;
                console.log(`Citation clicked: ${href}`);
                // In the actual SDK, this would be handled by AdMeshLinkTracker
            });
        });
    </script>
</body>
</html>
