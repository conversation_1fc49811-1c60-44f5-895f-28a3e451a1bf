<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AdMesh Citation - Clickable Product Names Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .demo-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .product-link {
            color: #1d4ed8;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
        }
        .product-link:hover {
            color: #1e40af;
            text-decoration: underline;
        }
        .citation {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.2s;
            margin-left: 2px;
        }
        .citation:hover {
            background: #bfdbfe;
            border-color: #60a5fa;
            transform: scale(1.1);
        }
        .references {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #d1d5db;
        }
        .reference-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .reference-link {
            color: #1d4ed8;
            text-decoration: none;
            font-weight: 600;
        }
        .reference-link:hover {
            text-decoration: underline;
        }
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }
        .note {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔗 AdMesh Citation - Clickable Product Names</h1>
    
    <div class="note">
        <strong>✅ Now Implemented:</strong> Product names in the conversational text are clickable hyperlinks, just like you requested!
    </div>

    <div class="demo-section">
        <h2>Citation Text with Clickable Product Names</h2>
        <p>
            Based on your requirements for a CRM system for your remote team, I'd recommend considering several excellent options. 
            <a href="https://useadmesh.com/track?ad_id=hubspot-123" target="_blank" class="product-link">HubSpot CRM</a><a href="https://useadmesh.com/track?ad_id=hubspot-123" target="_blank" class="citation">1</a> 
            offers a comprehensive free tier that's perfect for getting started, with excellent collaboration features that work well for remote teams. The platform includes robust automation capabilities and integrates seamlessly with other business tools.
        </p>
        
        <p>
            For larger enterprises, 
            <a href="https://useadmesh.com/track?ad_id=salesforce-456" target="_blank" class="product-link">Salesforce</a><a href="https://useadmesh.com/track?ad_id=salesforce-456" target="_blank" class="citation">2</a> 
            provides the most comprehensive feature set with extensive customization options. While it has a steeper learning curve, it's the industry standard for enterprise CRM solutions.
        </p>
        
        <p>
            If you prefer simplicity, 
            <a href="https://useadmesh.com/track?ad_id=pipedrive-789" target="_blank" class="product-link">Pipedrive</a><a href="https://useadmesh.com/track?ad_id=pipedrive-789" target="_blank" class="citation">3</a> 
            focuses specifically on sales pipeline management with an intuitive interface that's easy for teams to adopt quickly.
        </p>

        <p>
            All of these CRM solutions offer strong integration capabilities and can scale with your business needs.
        </p>

        <div class="references">
            <h3>📚 References</h3>
            
            <div class="reference-item">
                <span class="citation">1</span>
                <a href="https://useadmesh.com/track?ad_id=hubspot-123" target="_blank" class="reference-link">HubSpot CRM</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Perfect for remote teams with excellent collaboration features and robust automation capabilities
                </p>
            </div>
            
            <div class="reference-item">
                <span class="citation">2</span>
                <a href="https://useadmesh.com/track?ad_id=salesforce-456" target="_blank" class="reference-link">Salesforce</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Enterprise-grade CRM with extensive customization and integration capabilities
                </p>
            </div>
            
            <div class="reference-item">
                <span class="citation">3</span>
                <a href="https://useadmesh.com/track?ad_id=pipedrive-789" target="_blank" class="reference-link">Pipedrive</a>
                <p style="margin: 5px 0 0 25px; color: #666; font-size: 14px;">
                    Simple and intuitive CRM focused on sales pipeline management
                </p>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2>🎯 Key Features Implemented</h2>
        <ul>
            <li><strong class="highlight">Product Names are Hyperlinks:</strong> "HubSpot CRM", "Salesforce", "Pipedrive" are clickable blue links</li>
            <li><strong>Citation Numbers:</strong> Numbered references (1), (2), (3) provide additional citation points</li>
            <li><strong>Dual Clickability:</strong> Both product names AND citation numbers are clickable</li>
            <li><strong>Automatic Tracking:</strong> All clicks are tracked with AdMesh analytics</li>
            <li><strong>Reference List:</strong> Complete product details with clickable titles</li>
            <li><strong>Hover Effects:</strong> Visual feedback on hover for better UX</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>💻 React Component Implementation</h2>
        <pre style="background: #1f2937; color: #f3f4f6; padding: 15px; border-radius: 6px; overflow-x: auto;"><code>import { AdMeshCitationUnit } from 'admesh-ui-sdk';

const conversationText = `
Based on your requirements for a CRM system for your remote team, 
I'd recommend considering several excellent options. HubSpot CRM offers 
a comprehensive free tier that's perfect for getting started...
`;

&lt;AdMeshCitationUnit
  recommendations={recommendations}
  conversationText={conversationText}
  citationStyle="numbered"
  showCitationList={true}
  onRecommendationClick={(adId, link) => {
    // Both product names and citations trigger this
    console.log(`Opening: ${link}`);
  }}
/&gt;</code></pre>
    </div>

    <script>
        // Add click tracking for demo
        document.querySelectorAll('.product-link, .citation, .reference-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = e.target.href;
                const text = e.target.textContent;
                console.log(`Clicked: "${text}" -> ${href}`);
                // In the actual SDK, this would be handled by AdMeshLinkTracker
            });
        });
    </script>
</body>
</html>
