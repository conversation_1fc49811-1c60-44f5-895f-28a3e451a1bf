name: Publish AdMesh UI SDK

on:
  push:
    branches: [main]
    paths:
      - 'package.json'
      - 'src/**'
      - '.github/workflows/publish.yml'
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Build library
        run: npm run build
        
      - name: Build Storybook
        run: npm run build-storybook
        
      - name: Check if version changed
        id: version-check
        run: |
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          PUBLISHED_VERSION=$(npm view @admesh/ui-sdk version 2>/dev/null || echo "0.0.0")
          echo "current=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "published=$PUBLISHED_VERSION" >> $GITHUB_OUTPUT
          if [ "$CURRENT_VERSION" != "$PUBLISHED_VERSION" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Publish to NPM
        if: steps.version-check.outputs.changed == 'true'
        run: npm publish --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.ADMESH_NPM_TOKEN || secrets.NPM_TOKEN }}
          
      - name: Create GitHub Release
        if: steps.version-check.outputs.changed == 'true'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version-check.outputs.current }}
          release_name: Release v${{ steps.version-check.outputs.current }}
          body: |
            ## Changes in v${{ steps.version-check.outputs.current }}
            
            - Updated AdMesh UI SDK components
            - See [CHANGELOG.md](./CHANGELOG.md) for detailed changes
            
            ## Installation
            
            ```bash
            npm install @admesh/ui-sdk@${{ steps.version-check.outputs.current }}
            ```
          draft: false
          prerelease: false
          
      - name: Deploy Storybook to GitHub Pages
        if: steps.version-check.outputs.changed == 'true'
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./storybook-static
          destination_dir: storybook
