/* AdMeshProductCard - Premium Modern Design */

.admesh-product-card {
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  border-radius: var(--admesh-radius);
  background: rgb(var(--admesh-card));
  border: 1px solid rgb(var(--admesh-border));
  box-shadow: var(--admesh-shadow-sm);
  overflow: hidden;
}

.admesh-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--admesh-gradient-glass);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.admesh-product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--admesh-shadow-xl);
  border-color: rgb(var(--admesh-primary) / 0.2);
}

.admesh-product-card:hover::before {
  opacity: 1;
}

.admesh-product-card__container {
  position: relative;
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  z-index: 2;
}

/* Header */
.admesh-product-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.admesh-product-card__badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  flex: 1;
}

.admesh-product-card__match-score {
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--admesh-gradient-primary);
  color: rgb(var(--admesh-primary-foreground));
  border-radius: calc(var(--admesh-radius) - 2px);
  font-size: 0.875rem;
  font-weight: 600;
  border: none;
  box-shadow: var(--admesh-shadow-colored);
  position: relative;
  overflow: hidden;
}

.admesh-product-card__match-score::before {
  content: '';
  width: 8px;
  height: 8px;
  background-color: rgb(var(--admesh-primary-foreground));
  border-radius: 50%;
  box-shadow: 0 0 8px rgb(var(--admesh-primary-foreground) / 0.5);
}

.admesh-product-card__match-score::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.admesh-product-card:hover .admesh-product-card__match-score::after {
  left: 100%;
}

/* Content */
.admesh-product-card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.admesh-product-card__title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: rgb(var(--admesh-foreground));
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, rgb(var(--admesh-foreground)) 0%, rgb(var(--admesh-primary)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.admesh-product-card:hover .admesh-product-card__title {
  transform: translateX(4px);
}

.admesh-product-card__reason {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
  color: rgb(var(--admesh-muted-foreground));
  font-weight: 400;
}

/* Keywords */
.admesh-product-card__keywords {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admesh-spacing-xs);
  align-items: center;
}

.admesh-product-card__keyword {
  /* Inherits from admesh-badge styles */
}

.admesh-product-card__keyword-more {
  margin-left: var(--admesh-spacing-xs);
  font-style: italic;
}

/* Meta information */
.admesh-product-card__meta {
  display: flex;
  flex-direction: column;
  gap: var(--admesh-spacing-xs);
}

.admesh-product-card__pricing {
  margin: 0;
}

.admesh-product-card__trial {
  margin: 0;
  font-style: italic;
}

/* Footer */
.admesh-product-card__footer {
  margin-top: auto;
  padding-top: 1.5rem;
  border-top: 1px solid rgb(var(--admesh-border) / 0.5);
}

.admesh-product-card__cta {
  position: relative;
  width: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: var(--admesh-radius);
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 1rem 2rem;
  background: var(--admesh-gradient-primary);
  color: rgb(var(--admesh-primary-foreground));
  border: none;
  cursor: pointer;
  box-shadow: var(--admesh-shadow-md);
  overflow: hidden;
}

.admesh-product-card__cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.admesh-product-card__cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--admesh-shadow-xl);
}

.admesh-product-card__cta:hover::before {
  left: 100%;
}

.admesh-product-card__cta:active {
  transform: translateY(0);
}

.admesh-product-card__cta:focus-visible {
  outline: 2px solid rgb(var(--admesh-ring));
  outline-offset: 2px;
}

/* Badge icon spacing */
.admesh-badge__icon {
  margin-right: var(--admesh-spacing-xs);
}

.admesh-badge__text {
  /* No additional styles needed */
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .admesh-product-card__container {
    padding: var(--admesh-spacing-lg);
    gap: var(--admesh-spacing-md);
  }
  
  .admesh-product-card__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admesh-spacing-sm);
  }
  
  .admesh-product-card__match-score {
    align-self: flex-end;
  }
  
  .admesh-product-card__title {
    font-size: var(--admesh-font-size-base);
  }
}

/* Dark theme adjustments */
[data-admesh-theme="dark"] .admesh-product-card__match-score {
  background-color: var(--admesh-bg-tertiary);
  color: var(--admesh-text-secondary);
}

/* Focus styles for accessibility */
.admesh-product-card:focus-within {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

.admesh-product-card__cta:focus {
  outline: 2px solid var(--admesh-primary);
  outline-offset: 2px;
}

/* Loading state (for future use) */
.admesh-product-card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.admesh-product-card--loading .admesh-product-card__title,
.admesh-product-card--loading .admesh-product-card__reason {
  background: linear-gradient(90deg, var(--admesh-bg-tertiary) 25%, var(--admesh-bg-secondary) 50%, var(--admesh-bg-tertiary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--admesh-radius-sm);
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
