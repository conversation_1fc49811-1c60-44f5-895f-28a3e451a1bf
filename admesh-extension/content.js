// Define intent keywords directly instead of fetching them
const intentKeywords = [
  "best", "top", "compare", "find", "search", "recommend", "suggest",
  "tool", "software", "app", "platform", "service", "solution",
  "alternative", "review", "guide", "list"
];

function shouldAnalyzeQuery(query) {
  const lowered = query.toLowerCase();
  const hasIntent = intentKeywords.some(keyword => lowered.includes(keyword));
  const isShort = query.length < 4 || lowered.split(" ").length <= 1;
  const isJunk = !/[a-zA-Z]/.test(query);
  return hasIntent && !isShort && !isJunk;
}

// Update credits display in the sidebar
async function updateCreditsDisplay() {
  const confirmedElement = document.getElementById('admesh-confirmed-credits');
  const pendingElement = document.getElementById('admesh-pending-credits');

  if (!confirmedElement || !pendingElement) return;

  try {
    const credits = await fetchUserCredits();

    // Update confirmed credits
    confirmedElement.textContent = `Rewards: ${credits.confirmed}`;
    confirmedElement.style.transition = 'transform 0.3s ease, background-color 0.3s ease';
    confirmedElement.style.transform = 'scale(1.1)';
    setTimeout(() => {
      confirmedElement.style.transform = 'scale(1)';
    }, 300);

    // Update pending credits if any
    if (credits.pending > 0) {
      pendingElement.textContent = `Pending: ${credits.pending}`;
      pendingElement.style.display = 'block';
    } else {
      pendingElement.style.display = 'none';
    }
  } catch (error) {
    console.error('Error updating credits display:', error);
  }
}

function createSidebar() {
  if (document.getElementById('admesh-sidebar')) return;
  document.body.classList.add('admesh-active');

  const sidebar = document.createElement('div');
  sidebar.id = 'admesh-sidebar';
  sidebar.style.cssText = 'position: fixed; bottom: 20px; right: 20px; width: 400px; background: #ffffff; border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.15); z-index: 10000; font-family: -apple-system, BlinkMacSystemFont, sans-serif; border: 1px solid #e2e8f0; transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1); max-height: 85vh; display: flex; flex-direction: column; overflow: hidden;';

  // Create header
  const header = document.createElement('div');
  header.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; border-bottom: 1px solid #e2e8f0; background-color: #ffffff; flex-shrink: 0;';

  const logoDiv = document.createElement('div');
  logoDiv.style.cssText = 'display: flex; align-items: center; gap: 8px;';

  // Create logo icon
  const logoIcon = document.createElement('div');
  logoIcon.style.cssText = 'width: 32px; height: 32px; background-color: #e0e7ff; border-radius: 50%; display: flex; align-items: center; justify-content: center;';

  // Add logo text
  const logoText = document.createElement('span');
  logoText.textContent = 'AdMesh Assistant';
  logoText.style.cssText = 'font-size: 16px; color: #1e293b; font-weight: 600;';

  logoDiv.appendChild(logoIcon);
  logoDiv.appendChild(logoText);

  const buttonsDiv = document.createElement('div');
  buttonsDiv.style.cssText = 'display: flex; align-items: center; gap: 8px;';

  // Create new chat button
  const newChatBtn = document.createElement('button');
  newChatBtn.id = 'admesh-new-chat';
  newChatBtn.style.cssText = 'display: flex; align-items: center; gap: 4px; background-color: #4f46e5; color: white; border: none; padding: 6px 12px; border-radius: 9999px; font-size: 12px; font-weight: 500; cursor: pointer;';
  newChatBtn.innerHTML = '<svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path><line x1="12" y1="11" x2="12" y2="17"></line><line x1="9" y1="14" x2="15" y2="14"></line></svg>New Chat';

  // Create minimize button
  const minimizeBtn = document.createElement('button');
  minimizeBtn.id = 'admesh-minimize';
  minimizeBtn.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>';
  minimizeBtn.style.cssText = 'background: #f1f5f9; border: none; width: 28px; height: 28px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #64748b;';

  // Create close button
  const closeBtn = document.createElement('button');
  closeBtn.id = 'admesh-close';
  closeBtn.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
  closeBtn.style.cssText = 'background: #f1f5f9; border: none; width: 28px; height: 28px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #64748b;';

  buttonsDiv.appendChild(newChatBtn);
  buttonsDiv.appendChild(minimizeBtn);
  buttonsDiv.appendChild(closeBtn);

  header.appendChild(logoDiv);
  header.appendChild(buttonsDiv);

  // Create messages container
  const messagesContainer = document.createElement('div');
  messagesContainer.id = 'admesh-messages';
  messagesContainer.style.cssText = 'flex: 1; overflow-y: auto; padding: 16px; background-color: #f8fafc; height: calc(100% - 130px); max-height: calc(100% - 130px);';

  // Create content area (will be used for loading and messages)
  const content = document.createElement('div');
  content.id = 'admesh-content';
  content.style.cssText = 'text-align: center; padding: 30px 10px; color: #64748b;';

  const spinner = document.createElement('div');
  spinner.style.cssText = 'width: 48px; height: 48px; border: 4px solid #f0f4f8; border-top: 4px solid #4f46e5; border-radius: 50%; margin: 0 auto 20px; animation: admeshSpin 1s linear infinite;';

  const loadingText = document.createElement('div');
  loadingText.textContent = 'Finding the best tools for you...';
  loadingText.style.cssText = 'font-size: 15px; font-weight: 500;';

  content.appendChild(spinner);
  content.appendChild(loadingText);
  messagesContainer.appendChild(content);

  // Create footer with credits display
  const footer = document.createElement('div');
  footer.style.cssText = 'padding: 12px 16px; border-top: 1px solid #e2e8f0; background-color: #ffffff; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;';

  // Credits display
  const creditsDisplay = document.createElement('div');
  creditsDisplay.style.cssText = 'display: flex; flex-direction: column; gap: 2px;';

  const confirmedCredits = document.createElement('div');
  confirmedCredits.id = 'admesh-confirmed-credits';
  confirmedCredits.textContent = 'Rewards: 0';
  confirmedCredits.style.cssText = 'font-size: 13px; color: #4b5563; font-weight: 500;';

  const pendingCredits = document.createElement('div');
  pendingCredits.id = 'admesh-pending-credits';
  pendingCredits.textContent = 'Pending: 0';
  pendingCredits.style.cssText = 'font-size: 12px; color: #9ca3af; display: none;';

  creditsDisplay.appendChild(confirmedCredits);
  creditsDisplay.appendChild(pendingCredits);

  // Powered by text
  const poweredBy = document.createElement('div');
  poweredBy.textContent = 'Powered by AdMesh';
  poweredBy.style.cssText = 'font-size: 12px; color: #9ca3af;';

  footer.appendChild(creditsDisplay);
  footer.appendChild(poweredBy);

  // Add elements to sidebar
  sidebar.appendChild(header);
  sidebar.appendChild(messagesContainer);
  sidebar.appendChild(footer);

  // Add styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes admeshSpin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    #admesh-sidebar::-webkit-scrollbar { width: 6px; }
    #admesh-sidebar::-webkit-scrollbar-thumb { background-color: #cbd5e1; border-radius: 6px; }
    #admesh-messages::-webkit-scrollbar { width: 6px; }
    #admesh-messages::-webkit-scrollbar-thumb { background-color: #cbd5e1; border-radius: 6px; }
    #admesh-sidebar:hover { transform: translateY(-4px); box-shadow: 0 15px 35px rgba(0,0,0,0.12); }
    .admesh-tool-card { animation: admeshFadeIn 0.4s ease forwards; opacity: 0; }
    .admesh-minimized { height: 60px !important; overflow: hidden !important; }
    #admesh-minimize:hover, #admesh-close:hover, #admesh-new-chat:hover { opacity: 0.85; }
    .admesh-visit-btn:hover { transform: translateX(2px); }
    .admesh-message { animation: admeshFadeIn 0.4s ease forwards; opacity: 0; }
    .admesh-user-message { background-color: #4f46e5; color: white; border-radius: 12px; padding: 12px 16px; margin-bottom: 16px; max-width: 90%; margin-left: auto; }
    .admesh-assistant-message { background-color: white; color: #1e293b; border: 1px solid #e2e8f0; border-radius: 12px; padding: 12px 16px; margin-bottom: 16px; max-width: 90%; }
    @keyframes admeshFadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
  `;
  document.head.appendChild(style);
  document.body.appendChild(sidebar);

  document.getElementById('admesh-close').addEventListener('click', () => {
    sidebar.style.opacity = '0';
    sidebar.style.transform = 'translateY(20px)';
    setTimeout(() => {
      sidebar.remove();
      document.body.classList.remove('admesh-active');
    }, 300);
  });

  document.getElementById('admesh-minimize').addEventListener('click', () => {
    const button = document.getElementById('admesh-minimize');
    if (sidebar.classList.contains('admesh-minimized')) {
      sidebar.classList.remove('admesh-minimized');
      button.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>';
    } else {
      sidebar.classList.add('admesh-minimized');
      button.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>';
    }
  });

  document.getElementById('admesh-new-chat').addEventListener('click', () => {
    // Reset the chat
    const messagesContainer = document.getElementById('admesh-messages');
    messagesContainer.innerHTML = '';

    // Show empty state with suggested queries
    showEmptyState(messagesContainer);
  });
}

// Firebase auth variables
let auth = null;
let currentUser = null;

// Initialize Firebase auth from background script
function initFirebaseAuth() {
  chrome.runtime.sendMessage({ type: 'GET_AUTH_STATE' }, (response) => {
    if (response && response.isLoggedIn !== undefined) {
      currentUser = response.isLoggedIn ? { uid: response.uid } : null;
    }
  });
}

// Get user ID
async function getUid() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'GET_USER_ID' }, (response) => {
      resolve(response?.uid || 'anonymous');
    });
  });
}

// Get auth headers
async function getAuthHeaders() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'GET_AUTH_HEADERS' }, (response) => {
      resolve(response?.headers || {});
    });
  });
}

// Fetch user credits from the background script
async function fetchUserCredits() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'FETCH_CREDITS' }, (response) => {
      if (response && response.success) {
        resolve({
          confirmed: response.confirmed || 0,
          pending: response.pending || 0
        });
      } else {
        console.error('Error fetching credits:', response?.error || 'Unknown error');
        resolve({ confirmed: 0, pending: 0 });
      }
    });
  });
}

function renderToolCards(tools, isLoggedIn = false) {
  const content = document.getElementById('admesh-content');
  if (!content) return;

  if (!Array.isArray(tools) || tools.length === 0) {
    content.innerHTML = '<div style="padding: 20px; text-align: center;"><p style="color: #475569; font-size: 15px; font-weight: 500;">No matching tools found</p><p style="color: #64748b; font-size: 14px;">We couldn\'t find relevant tools for your search query.</p></div>';
    return;
  }

  // Create main container
  const container = document.createElement('div');
  container.style.cssText = 'text-align: left;';

  // Create header
  const header = document.createElement('h3');
  header.style.cssText = 'color: #1e293b; font-size: 16px; margin-bottom: 16px; font-weight: 600;';

  const sparkleSpan = document.createElement('span');
  sparkleSpan.style.cssText = 'color: #2563eb; margin-right: 6px;';
  sparkleSpan.textContent = '✨';

  header.appendChild(sparkleSpan);
  header.appendChild(document.createTextNode('Recommended Tools'));
  container.appendChild(header);

  // Create tools list
  const toolsList = document.createElement('ul');
  toolsList.style.cssText = 'list-style: none; padding: 0; margin: 0; display: flex; flex-direction: column; gap: 14px;';

  tools.forEach(tool => {
    const listItem = document.createElement('li');
    listItem.className = 'admesh-tool-card';

    const cardDiv = document.createElement('div');
    cardDiv.style.cssText = 'background: #fff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 16px; box-shadow: 0 2px 6px rgba(0,0,0,0.04); position: relative;';

    const categoryDiv = document.createElement('div');
    categoryDiv.style.cssText = 'position: absolute; top: -6px; left: 16px; background: #2563eb; color: white; font-size: 11px; padding: 3px 8px; border-radius: 6px; font-weight: 500;';
    categoryDiv.textContent = tool.category || 'Tool';

    const contentDiv = document.createElement('div');
    contentDiv.style.cssText = 'display: flex; justify-content: space-between; align-items: flex-start; margin-top: 8px;';

    const textDiv = document.createElement('div');

    const titleH4 = document.createElement('h4');
    titleH4.style.cssText = 'margin: 0 0 6px; font-size: 16px; font-weight: 600; color: #1e293b;';
    titleH4.textContent = tool.title;

    const reasonP = document.createElement('p');
    reasonP.style.cssText = 'margin: 0; font-size: 13.5px; color: #64748b; line-height: 1.5;';
    reasonP.textContent = tool.reason;

    textDiv.appendChild(titleH4);
    textDiv.appendChild(reasonP);

    if (tool.reward_note) {
      const rewardP = document.createElement('p');
      rewardP.style.cssText = 'margin: 5px 0 0; font-size: 12px; color: #059669; font-weight: 500;';
      rewardP.textContent = '🎁 ' + tool.reward_note;
      textDiv.appendChild(rewardP);
    }

    const visitLink = document.createElement('a');
    visitLink.href = tool.url;
    visitLink.target = '_blank';
    visitLink.className = 'admesh-visit-btn';
    visitLink.dataset.toolId = tool.toolId;
    visitLink.dataset.sessionId = tool.session_id;
    visitLink.dataset.recommendationId = tool.recommendation_id;
    visitLink.style.cssText = 'background: #2563eb; color: white; padding: 8px 14px; border-radius: 8px; font-size: 13px; font-weight: 500; text-decoration: none; margin-left: 15px; box-shadow: 0 2px 5px rgba(37, 99, 235, 0.2); display: flex; align-items: center;';
    visitLink.textContent = 'Visit';

    contentDiv.appendChild(textDiv);
    contentDiv.appendChild(visitLink);

    cardDiv.appendChild(categoryDiv);
    cardDiv.appendChild(contentDiv);

    listItem.appendChild(cardDiv);
    toolsList.appendChild(listItem);
  });

  container.appendChild(toolsList);

  // Add login message for non-logged in users
  if (!isLoggedIn) {
    const loginMessage = document.createElement('div');
    loginMessage.style.cssText = 'margin-top: 20px; padding: 12px; background-color: #f8fafc; border-radius: 8px; text-align: center; border: 1px dashed #cbd5e1;';

    const loginText = document.createElement('p');
    loginText.style.cssText = 'margin: 0 0 8px; font-size: 14px; color: #64748b;';
    loginText.textContent = 'Sign in to track your rewards';

    const loginButton = document.createElement('button');
    loginButton.id = 'admesh-sidebar-login-btn';
    loginButton.style.cssText = 'display: inline-block; background-color: #2563eb; color: white; padding: 6px 12px; border-radius: 6px; font-size: 13px; font-weight: 500; border: none; cursor: pointer;';
    loginButton.textContent = 'Login / Sign up';

    // Add event listener to show login form
    loginButton.addEventListener('click', () => {
      showLoginForm(content);
    });

    loginMessage.appendChild(loginText);
    loginMessage.appendChild(loginButton);
    container.appendChild(loginMessage);
  }

  // Clear and set content
  content.innerHTML = '';
  content.appendChild(container);

  // Add click event listeners to all visit buttons
  document.querySelectorAll('.admesh-visit-btn').forEach(btn => {
    btn.addEventListener('click', async () => {
      const toolId = btn.getAttribute('data-tool-id');
      const sessionId = btn.getAttribute('data-session-id');
      const recommendationId = btn.getAttribute('data-recommendation-id');

      if (toolId && sessionId) {
        try {
          // Get user ID and auth headers
          const userId = await getUid();
          const authHeaders = await getAuthHeaders();

          // Check if this is a test click (for development/testing)
          const isTestClick = new URLSearchParams(location.search).get('test_click') === 'true';

          // Track the click with the protocol API
          fetch('http://localhost:8000/click', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...authHeaders
            },
            body: JSON.stringify({
              product_id: toolId,
              agent_id: userId !== 'anonymous' ? userId : 'agent_admesh_extension',
              session_id: sessionId,
              user_id: userId,
              query: new URLSearchParams(location.search).get('q'),
              recommendation_id: recommendationId,
              is_test: isTestClick
            })
          })
          .then(res => res.json())
          .then(() => {
            // Update credits display after click is tracked
            setTimeout(() => updateCreditsDisplay(), 1000);
          })
          .catch(err => console.error('Error tracking click:', err));
        } catch (error) {
          console.error('Error in click tracking:', error);
        }
      }
    });
  });
}

// Show login form in the sidebar
function showLoginForm(contentElement) {
  // Save the original content to restore later if needed
  const originalContent = contentElement.innerHTML;
  contentElement.setAttribute('data-original-content', originalContent);

  // Clear the content
  contentElement.innerHTML = '';

  // Create login form container
  const loginContainer = document.createElement('div');
  loginContainer.style.cssText = 'padding: 20px; text-align: center;';

  // Create header
  const header = document.createElement('h3');
  header.style.cssText = 'color: #1e293b; font-size: 18px; margin-bottom: 16px; font-weight: 600;';
  header.textContent = 'Login to AdMesh';

  // Create form
  const form = document.createElement('form');
  form.id = 'admesh-login-form';
  form.style.cssText = 'display: flex; flex-direction: column; gap: 16px;';

  // Email input
  const emailGroup = document.createElement('div');
  emailGroup.style.cssText = 'text-align: left;';

  const emailLabel = document.createElement('label');
  emailLabel.htmlFor = 'admesh-email';
  emailLabel.style.cssText = 'display: block; margin-bottom: 6px; font-size: 14px; color: #4b5563; font-weight: 500;';
  emailLabel.textContent = 'Email';

  const emailInput = document.createElement('input');
  emailInput.type = 'email';
  emailInput.id = 'admesh-email';
  emailInput.required = true;
  emailInput.style.cssText = 'width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;';

  emailGroup.appendChild(emailLabel);
  emailGroup.appendChild(emailInput);

  // Password input
  const passwordGroup = document.createElement('div');
  passwordGroup.style.cssText = 'text-align: left;';

  const passwordLabel = document.createElement('label');
  passwordLabel.htmlFor = 'admesh-password';
  passwordLabel.style.cssText = 'display: block; margin-bottom: 6px; font-size: 14px; color: #4b5563; font-weight: 500;';
  passwordLabel.textContent = 'Password';

  const passwordInput = document.createElement('input');
  passwordInput.type = 'password';
  passwordInput.id = 'admesh-password';
  passwordInput.required = true;
  passwordInput.style.cssText = 'width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;';

  passwordGroup.appendChild(passwordLabel);
  passwordGroup.appendChild(passwordInput);

  // Error message
  const errorDiv = document.createElement('div');
  errorDiv.id = 'admesh-login-error';
  errorDiv.style.cssText = 'color: #dc2626; font-size: 14px; display: none;';

  // Login button
  const loginButton = document.createElement('button');
  loginButton.type = 'submit';
  loginButton.style.cssText = 'background-color: #2563eb; color: white; padding: 10px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; border: none; cursor: pointer;';
  loginButton.textContent = 'Login';

  // Google Sign-In button
  const googleButton = document.createElement('button');
  googleButton.type = 'button';
  googleButton.id = 'admesh-google-signin';
  googleButton.style.cssText = 'display: flex; align-items: center; justify-content: center; gap: 8px; background-color: white; color: #4285F4; border: 1px solid #dadce0; padding: 10px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; width: 100%; margin-top: 12px;';

  // Google icon
  const googleIcon = document.createElement('img');
  googleIcon.src = 'https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg';
  googleIcon.alt = 'Google';
  googleIcon.style.cssText = 'width: 18px; height: 18px;';

  googleButton.appendChild(googleIcon);
  googleButton.appendChild(document.createTextNode('Sign in with Google'));

  // Divider
  const divider = document.createElement('div');
  divider.style.cssText = 'display: flex; align-items: center; margin: 16px 0;';

  const dividerLine1 = document.createElement('div');
  dividerLine1.style.cssText = 'flex-grow: 1; height: 1px; background-color: #e5e7eb;';

  const dividerText = document.createElement('span');
  dividerText.style.cssText = 'margin: 0 10px; color: #6b7280; font-size: 14px;';
  dividerText.textContent = 'or';

  const dividerLine2 = document.createElement('div');
  dividerLine2.style.cssText = 'flex-grow: 1; height: 1px; background-color: #e5e7eb;';

  divider.appendChild(dividerLine1);
  divider.appendChild(dividerText);
  divider.appendChild(dividerLine2);

  // Sign up section
  const signupText = document.createElement('p');
  signupText.style.cssText = 'margin: 0 0 8px; font-size: 14px; color: #4b5563;';
  signupText.textContent = 'Don\'t have an account?';

  const signupButton = document.createElement('button');
  signupButton.type = 'button';
  signupButton.id = 'admesh-signup-btn';
  signupButton.style.cssText = 'background-color: white; color: #2563eb; border: 1px solid #2563eb; padding: 8px 14px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer;';
  signupButton.textContent = 'Sign up';

  // Back button
  const backButton = document.createElement('button');
  backButton.type = 'button';
  backButton.id = 'admesh-back-btn';
  backButton.style.cssText = 'background: none; border: none; color: #6b7280; font-size: 14px; cursor: pointer; margin-top: 16px;';
  backButton.textContent = 'Back to results';

  // Add elements to form
  form.appendChild(emailGroup);
  form.appendChild(passwordGroup);
  form.appendChild(errorDiv);
  form.appendChild(loginButton);
  form.appendChild(googleButton);

  // Add elements to container
  loginContainer.appendChild(header);
  loginContainer.appendChild(form);
  loginContainer.appendChild(divider);
  loginContainer.appendChild(signupText);
  loginContainer.appendChild(signupButton);
  loginContainer.appendChild(backButton);

  // Add container to content
  contentElement.appendChild(loginContainer);

  // Add event listeners
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    errorDiv.style.display = 'none';

    const email = emailInput.value;
    const password = passwordInput.value;

    // Show loading state
    loginButton.disabled = true;
    loginButton.textContent = 'Logging in...';

    // Send login request to background script
    chrome.runtime.sendMessage(
      { type: 'LOGIN', email, password },
      (response) => {
        if (response && response.success) {
          // Success - restore original content and refresh
          const query = new URLSearchParams(location.search).get('q');
          if (query) {
            chrome.runtime.sendMessage({ query, newSearch: true });
          }
        } else {
          // Show error message
          errorDiv.textContent = response?.error || 'Login failed. Please try again.';
          errorDiv.style.display = 'block';
          loginButton.disabled = false;
          loginButton.textContent = 'Login';
        }
      }
    );
  });

  // Google Sign-In button event listener
  googleButton.addEventListener('click', async () => {
    // Show loading state
    googleButton.disabled = true;
    googleButton.innerHTML = '';
    const loadingSpinner = document.createElement('div');
    loadingSpinner.style.cssText = 'width: 20px; height: 20px; border: 2px solid rgba(66, 133, 244, 0.2); border-top: 2px solid #4285F4; border-radius: 50%; animation: admeshSpin 1s linear infinite;';
    googleButton.appendChild(loadingSpinner);
    googleButton.appendChild(document.createTextNode(' Opening Google sign-in...'));

    try {
      // Send Google sign-in request to background script
      chrome.runtime.sendMessage(
        { type: 'GOOGLE_SIGNIN', role: 'user' },
        (response) => {
          // The background script will open a new tab for Google sign-in
          // We'll just reset the button state after a short delay
          setTimeout(() => {
            googleButton.disabled = false;
            googleButton.innerHTML = '';
            googleButton.appendChild(googleIcon.cloneNode(true));
            googleButton.appendChild(document.createTextNode('Sign in with Google'));
          }, 2000);
        }
      );
    } catch (error) {
      // Show error message
      errorDiv.textContent = error.message || 'Google sign-in failed. Please try again.';
      errorDiv.style.display = 'block';
      googleButton.disabled = false;
      googleButton.innerHTML = '';
      googleButton.appendChild(googleIcon.cloneNode(true));
      googleButton.appendChild(document.createTextNode('Sign in with Google'));
    }
  });

  // Sign up button event listener
  signupButton.addEventListener('click', () => {
    showSignupForm(contentElement);
  });

  // Back button event listener
  backButton.addEventListener('click', () => {
    // Restore original content
    const originalContent = contentElement.getAttribute('data-original-content');
    if (originalContent) {
      contentElement.innerHTML = originalContent;
    }
  });
}

// Show signup form in the sidebar
function showSignupForm(contentElement) {
  // Clear the content
  contentElement.innerHTML = '';

  // Create signup form container
  const signupContainer = document.createElement('div');
  signupContainer.style.cssText = 'padding: 20px; text-align: center;';

  // Create header
  const header = document.createElement('h3');
  header.style.cssText = 'color: #1e293b; font-size: 18px; margin-bottom: 16px; font-weight: 600;';
  header.textContent = 'Sign up for AdMesh';

  // Create form
  const form = document.createElement('form');
  form.id = 'admesh-signup-form';
  form.style.cssText = 'display: flex; flex-direction: column; gap: 16px;';

  // Email input
  const emailGroup = document.createElement('div');
  emailGroup.style.cssText = 'text-align: left;';

  const emailLabel = document.createElement('label');
  emailLabel.htmlFor = 'admesh-signup-email';
  emailLabel.style.cssText = 'display: block; margin-bottom: 6px; font-size: 14px; color: #4b5563; font-weight: 500;';
  emailLabel.textContent = 'Email';

  const emailInput = document.createElement('input');
  emailInput.type = 'email';
  emailInput.id = 'admesh-signup-email';
  emailInput.required = true;
  emailInput.style.cssText = 'width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;';

  emailGroup.appendChild(emailLabel);
  emailGroup.appendChild(emailInput);

  // Password input
  const passwordGroup = document.createElement('div');
  passwordGroup.style.cssText = 'text-align: left;';

  const passwordLabel = document.createElement('label');
  passwordLabel.htmlFor = 'admesh-signup-password';
  passwordLabel.style.cssText = 'display: block; margin-bottom: 6px; font-size: 14px; color: #4b5563; font-weight: 500;';
  passwordLabel.textContent = 'Password';

  const passwordInput = document.createElement('input');
  passwordInput.type = 'password';
  passwordInput.id = 'admesh-signup-password';
  passwordInput.required = true;
  passwordInput.style.cssText = 'width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;';

  passwordGroup.appendChild(passwordLabel);
  passwordGroup.appendChild(passwordInput);

  // Error message
  const errorDiv = document.createElement('div');
  errorDiv.id = 'admesh-signup-error';
  errorDiv.style.cssText = 'color: #dc2626; font-size: 14px; display: none;';

  // Signup button
  const signupButton = document.createElement('button');
  signupButton.type = 'submit';
  signupButton.style.cssText = 'background-color: #2563eb; color: white; padding: 10px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; border: none; cursor: pointer;';
  signupButton.textContent = 'Sign up';

  // Google Sign-In button
  const googleButton = document.createElement('button');
  googleButton.type = 'button';
  googleButton.id = 'admesh-google-signin-signup';
  googleButton.style.cssText = 'display: flex; align-items: center; justify-content: center; gap: 8px; background-color: white; color: #4285F4; border: 1px solid #dadce0; padding: 10px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; width: 100%; margin-top: 12px;';

  // Google icon
  const googleIcon = document.createElement('img');
  googleIcon.src = 'https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg';
  googleIcon.alt = 'Google';
  googleIcon.style.cssText = 'width: 18px; height: 18px;';

  googleButton.appendChild(googleIcon);
  googleButton.appendChild(document.createTextNode('Sign up with Google'));

  // Divider
  const divider = document.createElement('div');
  divider.style.cssText = 'display: flex; align-items: center; margin: 16px 0;';

  const dividerLine1 = document.createElement('div');
  dividerLine1.style.cssText = 'flex-grow: 1; height: 1px; background-color: #e5e7eb;';

  const dividerText = document.createElement('span');
  dividerText.style.cssText = 'margin: 0 10px; color: #6b7280; font-size: 14px;';
  dividerText.textContent = 'or';

  const dividerLine2 = document.createElement('div');
  dividerLine2.style.cssText = 'flex-grow: 1; height: 1px; background-color: #e5e7eb;';

  divider.appendChild(dividerLine1);
  divider.appendChild(dividerText);
  divider.appendChild(dividerLine2);

  // Login section
  const loginText = document.createElement('p');
  loginText.style.cssText = 'margin: 0 0 8px; font-size: 14px; color: #4b5563;';
  loginText.textContent = 'Already have an account?';

  const loginButton = document.createElement('button');
  loginButton.type = 'button';
  loginButton.id = 'admesh-login-btn';
  loginButton.style.cssText = 'background-color: white; color: #2563eb; border: 1px solid #2563eb; padding: 8px 14px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer;';
  loginButton.textContent = 'Login';

  // Back button
  const backButton = document.createElement('button');
  backButton.type = 'button';
  backButton.id = 'admesh-back-btn';
  backButton.style.cssText = 'background: none; border: none; color: #6b7280; font-size: 14px; cursor: pointer; margin-top: 16px;';
  backButton.textContent = 'Back to results';

  // Add elements to form
  form.appendChild(emailGroup);
  form.appendChild(passwordGroup);
  form.appendChild(errorDiv);
  form.appendChild(signupButton);
  form.appendChild(googleButton);

  // Add elements to container
  signupContainer.appendChild(header);
  signupContainer.appendChild(form);
  signupContainer.appendChild(divider);
  signupContainer.appendChild(loginText);
  signupContainer.appendChild(loginButton);
  signupContainer.appendChild(backButton);

  // Add container to content
  contentElement.appendChild(signupContainer);

  // Add event listeners
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    errorDiv.style.display = 'none';

    const email = emailInput.value;
    const password = passwordInput.value;

    // Show loading state
    signupButton.disabled = true;
    signupButton.textContent = 'Signing up...';

    // Send signup request to background script
    chrome.runtime.sendMessage(
      { type: 'SIGNUP', email, password, role: 'user' },
      (response) => {
        if (response && response.success) {
          // Success - restore original content and refresh
          const query = new URLSearchParams(location.search).get('q');
          if (query) {
            chrome.runtime.sendMessage({ query, newSearch: true });
          }
        } else {
          // Show error message
          errorDiv.textContent = response?.error || 'Signup failed. Please try again.';
          errorDiv.style.display = 'block';
          signupButton.disabled = false;
          signupButton.textContent = 'Sign up';
        }
      }
    );
  });

  // Google Sign-In button event listener for signup form
  googleButton.addEventListener('click', async () => {
    // Show loading state
    googleButton.disabled = true;
    googleButton.innerHTML = '';
    const loadingSpinner = document.createElement('div');
    loadingSpinner.style.cssText = 'width: 20px; height: 20px; border: 2px solid rgba(66, 133, 244, 0.2); border-top: 2px solid #4285F4; border-radius: 50%; animation: admeshSpin 1s linear infinite;';
    googleButton.appendChild(loadingSpinner);
    googleButton.appendChild(document.createTextNode(' Opening Google sign-in...'));

    try {
      // Send Google sign-in request to background script
      chrome.runtime.sendMessage(
        { type: 'GOOGLE_SIGNIN', role: 'user' },
        () => {
          // The background script will open a new tab for Google sign-in
          // We'll just reset the button state after a short delay
          setTimeout(() => {
            googleButton.disabled = false;
            googleButton.innerHTML = '';
            googleButton.appendChild(googleIcon.cloneNode(true));
            googleButton.appendChild(document.createTextNode('Sign up with Google'));
          }, 2000);
        }
      );
    } catch (error) {
      // Show error message
      errorDiv.textContent = error.message || 'Google sign-up failed. Please try again.';
      errorDiv.style.display = 'block';
      googleButton.disabled = false;
      googleButton.innerHTML = '';
      googleButton.appendChild(googleIcon.cloneNode(true));
      googleButton.appendChild(document.createTextNode('Sign up with Google'));
    }
  });

  // Login button event listener
  loginButton.addEventListener('click', () => {
    showLoginForm(contentElement);
  });

  // Back button event listener
  backButton.addEventListener('click', () => {
    // Restore original content
    const originalContent = contentElement.getAttribute('data-original-content');
    if (originalContent) {
      contentElement.innerHTML = originalContent;
    }
  });
}

// Check if user is logged in
async function isUserLoggedIn() {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'IS_USER_LOGGED_IN' }, (response) => {
      resolve(response?.isLoggedIn || false);
    });
  });
}

chrome.runtime.onMessage.addListener(async (msg) => {
  if (msg.type === 'GOOGLE_SIGNIN_SUCCESS') {
    // Handle successful Google sign-in
    console.log('Google sign-in successful');

    // Refresh the content if we're on a search page
    const query = new URLSearchParams(location.search).get('q');
    if (query) {
      chrome.runtime.sendMessage({ query, newSearch: true });
    }

    // Update credits display
    updateCreditsDisplay();

    // Reset any Google sign-in buttons that might be in loading state
    const googleButtons = document.querySelectorAll('[id^="admesh-google-signin"]');
    googleButtons.forEach(button => {
      if (button.disabled) {
        button.disabled = false;
        button.innerHTML = '';
        const googleIcon = document.createElement('img');
        googleIcon.src = 'https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg';
        googleIcon.alt = 'Google';
        googleIcon.style.cssText = 'width: 18px; height: 18px;';
        button.appendChild(googleIcon);
        button.appendChild(document.createTextNode(' Sign in with Google'));
      }
    });
  } else if (msg.type === 'GOOGLE_SIGNIN_FAILURE') {
    // Handle failed Google sign-in
    console.error('Google sign-in failed:', msg.error);

    // Reset any Google sign-in buttons that might be in loading state
    const googleButtons = document.querySelectorAll('[id^="admesh-google-signin"]');
    googleButtons.forEach(button => {
      if (button.disabled) {
        button.disabled = false;
        button.innerHTML = '';
        const googleIcon = document.createElement('img');
        googleIcon.src = 'https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg';
        googleIcon.alt = 'Google';
        googleIcon.style.cssText = 'width: 18px; height: 18px;';
        button.appendChild(googleIcon);
        button.appendChild(document.createTextNode(' Sign in with Google'));

        // Show error message if there's an error div nearby
        const form = button.closest('form');
        if (form) {
          const errorDiv = form.querySelector('[id$="-error"]');
          if (errorDiv) {
            errorDiv.textContent = msg.error || 'Google sign-in failed. Please try again.';
            errorDiv.style.display = 'block';
          }
        }
      }
    });
  } else if (msg.type === 'TOOL_RECOMMENDATIONS') {
    // Always show recommendations, regardless of login status
    createSidebar();

    // Check if user is logged in
    const loggedIn = await isUserLoggedIn();

    // Update credits display if logged in
    if (loggedIn) {
      updateCreditsDisplay();
    }

    // Add recommendation_id to each tool
    if (msg.recommendation_id) {
      msg.tools.forEach(tool => {
        tool.recommendation_id = msg.recommendation_id;
      });
    }

    setTimeout(() => renderToolCards(msg.tools, loggedIn), 800);
  } else if (msg.type === 'INTENT_ERROR') {
    console.error('[AdMesh] LLM error:', msg.error);
    createSidebar();
    setTimeout(() => renderToolCards([]), 800);
  } else if (msg.type === 'UPDATE_CREDITS') {
    // Update credits display with the values from the message
    const confirmedElement = document.getElementById('admesh-confirmed-credits');
    const pendingElement = document.getElementById('admesh-pending-credits');

    if (confirmedElement && pendingElement) {
      // Update confirmed credits
      confirmedElement.textContent = `Rewards: ${msg.confirmed || 0}`;
      confirmedElement.style.transition = 'transform 0.3s ease, background-color 0.3s ease';
      confirmedElement.style.transform = 'scale(1.1)';
      setTimeout(() => {
        confirmedElement.style.transform = 'scale(1)';
      }, 300);

      // Update pending credits if any
      if (msg.pending > 0) {
        pendingElement.textContent = `Pending: ${msg.pending}`;
        pendingElement.style.display = 'block';
      } else {
        pendingElement.style.display = 'none';
      }
    }
  }
});

async function init() {
  // Initialize Firebase auth
  initFirebaseAuth();

  if (location.hostname.includes('google.com')) {
    const query = new URLSearchParams(location.search).get('q');
    if (query && shouldAnalyzeQuery(query)) {
      // Show notification
      const notification = document.createElement('div');
      notification.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background: rgba(37, 99, 235, 0.1); color: #2563eb; padding: 10px 15px; border-radius: 8px; font-family: sans-serif; font-size: 13px; z-index: 9999; animation: fadeOut 3s forwards;';
      notification.textContent = 'AdMesh is finding tools for you...';
      document.body.appendChild(notification);

      const fadeStyle = document.createElement('style');
      fadeStyle.textContent = '@keyframes fadeOut { 0% { opacity: 1; } 70% { opacity: 1; } 100% { opacity: 0; visibility: hidden; } }';
      document.head.appendChild(fadeStyle);

      // Send the query to get recommendations
      // The login check will happen when recommendations are received
      chrome.runtime.sendMessage({ query });
    }
  }
}

document.readyState === 'loading' ? document.addEventListener('DOMContentLoaded', init) : init();
