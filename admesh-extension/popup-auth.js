// Popup authentication script for AdMesh extension
import {
  auth,
  googleProvider,
  signInWithPopup
} from './firebase.js';

// Handle Google sign-in in the popup context
async function handleGoogleSignIn() {
  try {
    // Show loading state
    document.body.innerHTML = `
      <div style="font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f5f5f5;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; margin: 0 auto 20px; animation: spin 2s linear infinite;"></div>
          <h2>Connecting to Google...</h2>
          <p>Please wait while we connect to your Google account.</p>
          <p id="status-message" style="color: #666; font-size: 14px;">Initializing authentication...</p>
          <button id="manual-signin" style="display: none; background-color: #4285F4; color: white; border: none; padding: 10px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-top: 20px;">Sign in with Google</button>
          <style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>
        </div>
      </div>
    `;

    // Set up a timeout to show a manual button if it takes too long
    const timeoutId = setTimeout(() => {
      const statusMessage = document.getElementById('status-message');
      const manualButton = document.getElementById('manual-signin');

      if (statusMessage) {
        statusMessage.textContent = 'Taking longer than expected. You can try the manual sign-in button below.';
      }

      if (manualButton) {
        manualButton.style.display = 'inline-block';
        manualButton.addEventListener('click', () => {
          statusMessage.textContent = 'Opening Google sign-in...';

          // Open Google sign-in in a new window
          const googleSigninUrl = chrome.runtime.getURL('google-signin.html');
          const authWindow = window.open(googleSigninUrl, '_blank', 'width=500,height=600,left=' + (window.screen.width/2 - 250) + ',top=' + (window.screen.height/2 - 300));

          if (!authWindow) {
            statusMessage.textContent = 'Popup blocked. Please allow popups for this site.';
            return;
          }

          // Try signing in again when the button is clicked
          signInWithPopup(auth, googleProvider)
            .then(handleSuccess)
            .catch(handleError);
        });
      }
    }, 10000); // Show after 10 seconds

    // Update status
    const statusMessage = document.getElementById('status-message');
    if (statusMessage) {
      statusMessage.textContent = 'Opening Google sign-in...';
    }

    // Sign in with Google popup
    console.log('Attempting to sign in with Google...');
    updateDebugInfo('Attempting sign-in', 'Google', 'Starting popup');

    try {
      // Configure Google provider with specific settings
      googleProvider.setCustomParameters({
        prompt: 'select_account',
        login_hint: '',  // Clear any previous login hints
        display: 'popup' // Explicitly request popup display
      });

      updateDebugInfo('Provider configured', 'Google', 'Opening popup');

      // Update status
      const statusMessage = document.getElementById('status-message');
      if (statusMessage) {
        statusMessage.textContent = 'Google sign-in window opening...';
      }

      // Open a new window for Google sign-in
      const googleSigninUrl = chrome.runtime.getURL('google-signin.html');
      const authWindow = window.open(googleSigninUrl, '_blank', 'width=500,height=600,left=' + (window.screen.width/2 - 250) + ',top=' + (window.screen.height/2 - 300));

      if (!authWindow) {
        updateDebugInfo('Popup blocked', 'Error', 'Browser blocked popup');
        throw new Error('Popup window was blocked. Please allow popups for this site.');
      }

      updateDebugInfo('Popup opened', 'Google', 'Waiting for sign-in');

      // We'll wait for a message from the popup window
      // The actual sign-in is handled in the google-signin.html file
      // Set up a listener for the GOOGLE_SIGNIN_SUCCESS message
      const messagePromise = new Promise((resolve, reject) => {
        const messageHandler = (event) => {
          if (event.data && event.data.type === 'GOOGLE_SIGNIN_SUCCESS') {
            window.removeEventListener('message', messageHandler);
            resolve(event.data.user);
          } else if (event.data && event.data.type === 'GOOGLE_SIGNIN_FAILURE') {
            window.removeEventListener('message', messageHandler);
            reject(new Error(event.data.error || 'Google sign-in failed'));
          }
        };

        window.addEventListener('message', messageHandler);

        // Set up a timeout to reject the promise if we don't get a response
        setTimeout(() => {
          window.removeEventListener('message', messageHandler);
          reject(new Error('Google sign-in timed out'));
        }, 120000); // 2 minutes timeout
      });

      // Wait for the message
      try {
        const user = await messagePromise;

        // Clear the timeout since we succeeded
        clearTimeout(timeoutId);

        updateDebugInfo('Sign-in successful', 'Google', 'Popup completed');

        // Handle success
        return { success: true, user };
      } catch (error) {
        // Clear the timeout
        clearTimeout(timeoutId);

        updateDebugInfo('Sign-in failed', 'Google', `Error: ${error.message}`);
        throw error;
      }
    } catch (error) {
      // Clear the timeout
      clearTimeout(timeoutId);

      updateDebugInfo('Sign-in failed', 'Google', `Error: ${error.code || error.message}`);
      console.error('Google sign-in error:', error);

      // Handle error
      return handleError(error);
    }
  } catch (error) {
    console.error('Error signing in with Google:', error);

    // Show error message
    document.body.innerHTML = `
      <div style="font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f5f5f5;">
        <div style="text-align: center;">
          <div style="width: 60px; height: 60px; background-color: #F44336; border-radius: 50%; margin: 0 auto 20px; display: flex; justify-content: center; align-items: center;">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <h2>Sign-in failed</h2>
          <p>${error.message || 'An error occurred during sign-in.'}</p>
          <button id="retry-button" style="background-color: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-top: 20px;">Try Again</button>
          <button id="close-button" style="background-color: #f5f5f5; color: #333; border: 1px solid #ccc; padding: 10px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-top: 10px; margin-left: 10px;">Close</button>
        </div>
      </div>
    `;

    // Add event listeners to buttons
    document.getElementById('retry-button').addEventListener('click', () => {
      handleGoogleSignIn();
    });

    document.getElementById('close-button').addEventListener('click', () => {
      window.close();
    });

    // Notify the extension that sign-in failed
    chrome.runtime.sendMessage({ type: 'GOOGLE_SIGNIN_FAILURE', error: error.message });

    return { success: false, error: error.message || 'Google sign-in failed' };
  }
}

// Handle successful sign-in
async function handleSuccess(result) {
  const user = result.user;
  updateDebugInfo('User authenticated', 'Google', `User: ${user.email}`);
  console.log('User authenticated:', user.email);

  // Get ID token for API call
  const idToken = await user.getIdToken();
  updateDebugInfo('Token obtained', 'Google', 'Registering with backend');

  // Register with backend to set role
  try {
    updateDebugInfo('Connecting to backend', 'API', 'Sending request');
    const response = await fetch('http://localhost:8000/auth/google-onboard', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${idToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ role: 'user' })
  });

    if (!response.ok) {
      const errorData = await response.json();
      updateDebugInfo('Backend error', 'API', `Error: ${errorData.detail || 'Unknown error'}`);
      throw new Error(errorData.detail || 'Failed to register with Google');
    }

    updateDebugInfo('Registration successful', 'API', 'User registered');
  } catch (error) {
    updateDebugInfo('Backend failed', 'API', `Error: ${error.message}`);
    console.error('Backend registration error:', error);
    throw error;
  }

  // Show success message
  updateDebugInfo('Complete', 'Success', 'Showing success message');
  document.body.innerHTML = `
    <div style="font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f5f5f5;">
      <div style="text-align: center;">
        <div style="width: 60px; height: 60px; background-color: #4CAF50; border-radius: 50%; margin: 0 auto 20px; display: flex; justify-content: center; align-items: center;">
          <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
        <h2>Successfully signed in!</h2>
        <p>You can close this window and return to AdMesh.</p>
      </div>
    </div>
  `;

  // Notify the extension that sign-in was successful
  chrome.runtime.sendMessage({ type: 'GOOGLE_SIGNIN_SUCCESS', user: { uid: user.uid } });

  // Close the window after a short delay
  setTimeout(() => {
    window.close();
  }, 2000);

  return { success: true, user };
}

// Handle sign-in error
function handleError(error) {
  console.error('Error signing in with Google:', error);
  updateDebugInfo('Error handling', 'Error', `Code: ${error.code || 'unknown'}, Message: ${error.message}`);

  // Show error message with more details
  document.body.innerHTML = `
    <div style="font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; background-color: #f5f5f5;">
      <div style="text-align: center;">
        <div style="width: 60px; height: 60px; background-color: #F44336; border-radius: 50%; margin: 0 auto 20px; display: flex; justify-content: center; align-items: center;">
          <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
        <h2>Sign-in failed</h2>
        <p>${error.message || 'An error occurred during sign-in.'}</p>
        <p style="font-size: 12px; color: #666;">Error code: ${error.code || 'unknown'}</p>
        <button id="retry-button" style="background-color: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-top: 20px;">Try Again</button>
        <button id="close-button" style="background-color: #f5f5f5; color: #333; border: 1px solid #ccc; padding: 10px 20px; border-radius: 4px; font-size: 14px; cursor: pointer; margin-top: 10px; margin-left: 10px;">Close</button>
      </div>
    </div>
  `;

  // Add event listeners to buttons
  document.getElementById('retry-button').addEventListener('click', () => {
    handleGoogleSignIn();
  });

  document.getElementById('close-button').addEventListener('click', () => {
    window.close();
  });

  // Notify the extension that sign-in failed
  chrome.runtime.sendMessage({ type: 'GOOGLE_SIGNIN_FAILURE', error: error.message });

  return { success: false, error: error.message || 'Google sign-in failed' };
}

// Update debug info
function updateDebugInfo(status, provider, popup) {
  const firebaseStatus = document.getElementById('firebase-status');
  const authProvider = document.getElementById('auth-provider');
  const popupStatus = document.getElementById('popup-status');

  if (firebaseStatus && status) {
    firebaseStatus.textContent = status;
  }

  if (authProvider && provider) {
    authProvider.textContent = provider;
  }

  if (popupStatus && popup) {
    popupStatus.textContent = popup;
  }
}

// Start the Google sign-in process when the page loads
document.addEventListener('DOMContentLoaded', () => {
  // Connect manual sign-in button
  const manualButton = document.getElementById('manual-signin-btn');
  if (manualButton) {
    manualButton.addEventListener('click', () => {
      const statusMessage = document.getElementById('status-message');
      if (statusMessage) {
        statusMessage.textContent = 'Attempting to sign in...';
      }
      updateDebugInfo('Active', 'Google', 'Manual button clicked');
      handleGoogleSignIn();
    });
  }

  // Update debug info
  updateDebugInfo('Initialized', 'Google', 'Starting automatic sign-in');

  // Start the sign-in process
  handleGoogleSignIn();
});
