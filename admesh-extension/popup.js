import { auth, signOutUser, getUserRole, redirectToUseadmesh } from './firebase.js';
import { onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// DOM Elements
const loggedOutView = document.getElementById('logged-out-view');
const loggedInView = document.getElementById('logged-in-view');
const userEmail = document.getElementById('user-email');
const userRole = document.getElementById('user-role');
const userConfirmedCredits = document.getElementById('user-confirmed-credits');
const userPendingCredits = document.getElementById('user-pending-credits');
const logoutBtn = document.getElementById('logout-btn');
const loginBtn = document.getElementById('login-btn');
const signupBtn = document.getElementById('signup-btn');
const offersDiv = document.getElementById('offers');

// Login button click
loginBtn.addEventListener('click', () => {
  redirectToUseadmesh('/login');
});

// Signup button click
signupBtn.addEventListener('click', () => {
  redirectToUseadmesh('/signup');
});

// Logout
logoutBtn.addEventListener('click', async () => {
  try {
    await signOutUser();
    // Auth state listener will handle UI updates
  } catch (error) {
    console.error('Error signing out:', error);
  }
});

// Auth state listener
onAuthStateChanged(auth, async (user) => {
  if (user) {
    // User is signed in
    loggedOutView.style.display = 'none';
    loggedInView.style.display = 'block';

    // Update user info
    userEmail.textContent = user.email;

    // Get user role
    const role = await getUserRole();
    userRole.textContent = role ? `Role: ${role}` : 'Role: Not set';

    // Fetch user credits
    fetchUserCredits(user);

    // Load offers
    loadOffers();
  } else {
    // User is signed out
    loggedOutView.style.display = 'block';
    loggedInView.style.display = 'none';
  }
});

// Fetch user credits
async function fetchUserCredits(user) {
  try {
    const idToken = await user.getIdToken();
    const response = await fetch('http://localhost:8000/user/payments/pending_credits', {
      headers: {
        'Authorization': `Bearer ${idToken}`
      }
    });

    if (response.ok) {
      const data = await response.json();

      // Update confirmed credits
      const confirmed = data.confirmed_credits || 0;
      userConfirmedCredits.textContent = `Rewards: ${confirmed}`;

      // Update pending credits if any
      const pending = data.pending_credits || 0;
      if (pending > 0) {
        userPendingCredits.textContent = `Pending: ${pending}`;
        userPendingCredits.style.display = 'block';
      } else {
        userPendingCredits.style.display = 'none';
      }
    } else {
      userConfirmedCredits.textContent = 'Rewards: 0';
      userPendingCredits.style.display = 'none';
    }
  } catch (error) {
    console.error('Error fetching credits:', error);
    userConfirmedCredits.textContent = 'Rewards: 0';
    userPendingCredits.style.display = 'none';
  }
}

// Load offers
function loadOffers() {
  chrome.storage.local.get(['latestIntent'], (result) => {
    const intentData = result.latestIntent;

    if (!intentData || !intentData.product_category) {
      offersDiv.textContent = "No intent detected yet.";
      return;
    }

    fetch(chrome.runtime.getURL('offers.json'))
      .then(response => response.json())
      .then(data => {
        const category = intentData.product_category.toLowerCase();
        const matchedOffers = data.filter(offer =>
          offer.keywords.map(k => k.toLowerCase()).includes(category)
        );

        if (matchedOffers.length === 0) {
          offersDiv.textContent = "No offers available for this intent.";
          return;
        }

        offersDiv.innerHTML = '';
        matchedOffers.forEach(offer => {
          const offerDiv = document.createElement('div');
          offerDiv.className = 'offer';
          offerDiv.innerHTML = `
            <a href="${offer.url}" target="_blank"><strong>${offer.title}</strong></a>
            <p>${offer.description}</p>
            <p style="font-size: 13px; color: #388e3c;">${offer.user_benefit || offer.reward_note || ''}</p>
          `;
          offersDiv.appendChild(offerDiv);
        });
      })
      .catch(err => {
        offersDiv.textContent = "Failed to load offers.";
        console.error(err);
      });
  });
}