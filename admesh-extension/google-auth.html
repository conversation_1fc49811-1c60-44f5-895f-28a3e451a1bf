<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AdMesh - Google Sign-In</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f5f5;
    }
    .container {
      text-align: center;
      max-width: 500px;
      padding: 20px;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      margin: 0 auto 20px;
      animation: spin 2s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #debug-info {
      margin-top: 30px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
      text-align: left;
      display: none;
    }
    #show-debug {
      background: none;
      border: none;
      color: #666;
      text-decoration: underline;
      cursor: pointer;
      font-size: 12px;
      margin-top: 20px;
    }
    .manual-signin {
      display: none;
      margin-top: 20px;
    }
    .manual-signin button {
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="spinner"></div>
    <h2>Connecting to Google...</h2>
    <p>Please wait while we connect to your Google account.</p>
    <p id="status-message" style="color: #666; font-size: 14px;">Initializing authentication...</p>

    <div class="manual-signin" id="manual-signin-container">
      <p>If the Google sign-in window doesn't appear automatically, please click the button below:</p>
      <button id="manual-signin-btn">Sign in with Google</button>
    </div>

    <button id="show-debug">Show Debug Info</button>

    <div id="debug-info">
      <h4>Debug Information:</h4>
      <p>Extension ID: <span id="extension-id"></span></p>
      <p>Firebase Status: <span id="firebase-status">Initializing...</span></p>
      <p>Auth Provider: <span id="auth-provider">Checking...</span></p>
      <p>Popup Status: <span id="popup-status">Not started</span></p>
    </div>
  </div>

  <script>
    // Show debug info when button is clicked
    document.getElementById('show-debug').addEventListener('click', function() {
      const debugInfo = document.getElementById('debug-info');
      debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
      this.textContent = debugInfo.style.display === 'none' ? 'Show Debug Info' : 'Hide Debug Info';
    });

    // Show manual sign-in button after 15 seconds
    setTimeout(function() {
      document.getElementById('manual-signin-container').style.display = 'block';
      document.getElementById('status-message').textContent = 'Taking longer than expected. You can try the manual sign-in button.';
    }, 15000);

    // Set extension ID in debug info
    document.getElementById('extension-id').textContent = chrome.runtime.id;
  </script>

  <script type="module" src="popup-auth.js"></script>
</body>
</html>
