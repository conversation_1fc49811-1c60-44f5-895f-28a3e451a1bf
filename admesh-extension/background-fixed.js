// Background script for AdMesh extension
import {
  auth,
  getIdToken,
  signInWithEmail,
  signUpWithEmail
} from './firebase.js';
import { onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Listen for auth state changes
onAuthStateChanged(auth, (user) => {
  console.log('Auth state changed:', user ? 'logged in' : 'logged out');

  // If user is logged in, fetch credits
  if (user) {
    fetchUserCredits();
  }
});

// Fetch user credits from the API
async function fetchUserCredits() {
  try {
    const authHeaders = await getAuthHeader();
    const response = await fetch('http://localhost:8000/user/payments/pending_credits', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error: ${response.status}`);
    }

    const data = await response.json();
    console.log('User credits fetched:', data);

    // Send message to all tabs to update credits display
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          type: 'UPDATE_CREDITS',
          confirmed: data.confirmed_credits || 0,
          pending: data.pending_credits || 0
        }).catch(() => {});
      });
    });

    return data;
  } catch (error) {
    console.error('Error fetching user credits:', error);
    return { confirmed_credits: 0, pending_credits: 0 };
  }
}

// Generate a unique session ID for tracking
function generateSessionId() {
  return 'sess_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Store the current session ID
let currentSessionId = generateSessionId();

// Store pending Google sign-in responses
let pendingGoogleSignInResponses = [];

// Reset session ID when a new search is performed
function resetSessionId() {
  currentSessionId = generateSessionId();
  return currentSessionId;
}

// Get agent ID (use Firebase user ID if available)
async function getAgentId() {
  const user = auth.currentUser;
  return user ? user.uid : 'agent_admesh_extension';
}

// Get authentication token if user is logged in
async function getAuthHeader() {
  const token = await getIdToken();
  return token ? { 'Authorization': `Bearer ${token}` } : {};
}

// Import additional Firebase auth methods
import { GoogleAuthProvider, signInWithCredential } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handle search queries
  if (request.query && sender.tab?.id) {
    // Reset session ID for new search
    if (request.newSearch) {
      resetSessionId();
    }

    // Use the protocol's recommend endpoint with authentication
    handleRecommendRequest(request, sender);
  }

  // Handle auth state requests
  else if (request.type === 'GET_AUTH_STATE') {
    const user = auth.currentUser;
    sendResponse({
      isLoggedIn: !!user,
      uid: user?.uid || null
    });
  }

  // Handle user ID requests
  else if (request.type === 'GET_USER_ID') {
    const user = auth.currentUser;
    sendResponse({
      uid: user?.uid || null
    });
  }

  // Handle auth headers requests
  else if (request.type === 'GET_AUTH_HEADERS') {
    getAuthHeader().then(headers => {
      sendResponse({ headers });
    });
    return true; // Required for async response
  }

  // Handle login status check
  else if (request.type === 'IS_USER_LOGGED_IN') {
    sendResponse({
      isLoggedIn: !!auth.currentUser
    });
  }

  // Handle redirect to useadmesh.com
  else if (request.type === 'REDIRECT_TO_USEADMESH') {
    chrome.tabs.create({ url: 'https://useadmesh.com' + (request.path || '') });
    sendResponse({ success: true });
  }

  // Handle login requests
  else if (request.type === 'LOGIN') {
    const { email, password } = request;

    // Use the imported function directly
    signInWithEmail(email, password)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({
          success: false,
          error: error.message || 'Login failed'
        });
      });

    return true; // Required for async response
  }

  // Handle Google sign-in requests
  else if (request.type === 'GOOGLE_SIGNIN') {
    try {
      // Create a Google auth provider
      const provider = new GoogleAuthProvider();
      provider.addScope('email');
      provider.addScope('profile');

      // Open a new tab for Google sign-in
      chrome.tabs.create({
        url: 'https://accounts.google.com/signin/v2/identifier?service=accountsettings',
        active: true
      }, (tab) => {
        console.log('Google sign-in tab created:', tab);
      });

      // We'll handle the actual sign-in in the content script
      sendResponse({ success: true, message: 'Google sign-in tab opened' });
    } catch (error) {
      console.error('Failed to initiate Google sign-in:', error);
      sendResponse({
        success: false,
        error: error.message || 'Failed to initiate Google sign-in'
      });
    }

    return true; // Required for async response
  }

  // Handle signup requests
  else if (request.type === 'SIGNUP') {
    const { email, password, role } = request;

    // Use the imported function directly
    signUpWithEmail(email, password, role)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({
          success: false,
          error: error.message || 'Signup failed'
        });
      });

    return true; // Required for async response
  }

  // Handle fetch credits request
  else if (request.type === 'FETCH_CREDITS') {
    fetchUserCredits()
      .then(data => {
        sendResponse({
          success: true,
          confirmed: data.confirmed_credits || 0,
          pending: data.pending_credits || 0
        });
      })
      .catch(error => {
        sendResponse({
          success: false,
          error: error.message
        });
      });
    return true; // Required for async response
  }

  return true;
});

// Handle recommend request with authentication
async function handleRecommendRequest(request, sender) {
  try {
    // Get agent ID and auth headers
    const agentId = await getAgentId();
    const authHeaders = await getAuthHeader();

    // Make API request
    fetch('http://localhost:8000/openrouter/recommend', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...authHeaders
      },
      body: JSON.stringify({
        query: request.query,
        model: "mistralai/mistral-7b-instruct",
        agent_id: agentId,
        session_id: currentSessionId
      })
    })
    .then(res => {
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      return res.json();
    })
    .then(response => {
      // Transform the protocol response to match the extension's expected format
      const recommendations = response.response?.recommendations || [];

      const transformedOffers = recommendations.map(rec => ({
        title: rec.title,
        reason: rec.reason || rec.description,
        category: rec.category || 'Tool',
        toolId: rec.id || rec.product_id,
        url: rec.admesh_link || rec.url,
        reward_note: rec.reward_note,
        trust_score: rec.trust_score || 80,
        session_id: currentSessionId,
        agent_id: agentId,
        recommendation_id: response.recommendation_id
      }));

      chrome.tabs.sendMessage(sender.tab.id, {
        type: 'TOOL_RECOMMENDATIONS',
        tools: transformedOffers,
        session_id: currentSessionId,
        recommendation_id: response.recommendation_id
      });
    })
    .catch(error => {
      console.error('AdMesh Protocol API Error:', error);
      chrome.tabs.sendMessage(sender.tab.id, {
        type: 'INTENT_ERROR',
        error: error.message
      });
    });
  } catch (error) {
    console.error('Error in handleRecommendRequest:', error);
    chrome.tabs.sendMessage(sender.tab.id, {
      type: 'INTENT_ERROR',
      error: error.message
    });
  }
}
