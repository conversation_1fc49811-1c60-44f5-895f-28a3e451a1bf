<!DOCTYPE html>
<html>
<head>
  <title>AdMesh</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      width: 320px;
      margin: 0;
      padding: 0;
      background-color: #f9fafb;
    }
    .container {
      padding: 16px;
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
    }
    .logo {
      font-size: 20px;
      font-weight: 700;
      color: #2563eb;
    }
    .tabs {
      display: flex;
      background-color: #e5e7eb;
      border-radius: 8px;
      padding: 4px;
      margin-bottom: 16px;
    }
    .tab {
      flex: 1;
      text-align: center;
      padding: 8px 0;
      cursor: pointer;
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s;
    }
    .tab.active {
      background-color: white;
      color: #2563eb;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .form-group {
      margin-bottom: 12px;
    }
    label {
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
    }
    input, select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      box-sizing: border-box;
    }
    input:focus, select:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }
    .btn {
      width: 100%;
      padding: 10px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    .btn-primary {
      background-color: #2563eb;
      color: white;
    }
    .btn-primary:hover {
      background-color: #1d4ed8;
    }
    .btn-outline {
      background-color: white;
      color: #4b5563;
      border: 1px solid #d1d5db;
    }
    .btn-outline:hover {
      background-color: #f3f4f6;
    }
    .divider {
      display: flex;
      align-items: center;
      margin: 16px 0;
      color: #6b7280;
      font-size: 12px;
    }
    .divider::before, .divider::after {
      content: '';
      flex: 1;
      border-top: 1px solid #e5e7eb;
    }
    .divider span {
      padding: 0 10px;
    }
    .error {
      color: #dc2626;
      font-size: 12px;
      margin-top: 4px;
    }
    .google-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .google-icon {
      width: 18px;
      height: 18px;
    }
    .user-info {
      background-color: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .user-email {
      font-weight: 500;
      margin-bottom: 4px;
    }
    .user-role {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 12px;
    }
    .credits {
      display: inline-block;
      background-color: #2563eb;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    .pending-credits {
      display: inline-block;
      background-color: #f0f4f8;
      color: #64748b;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      margin-top: 4px;
    }
    .rewards-container {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-top: 8px;
    }
    .offers-container {
      margin-top: 16px;
    }
    .offer {
      background-color: white;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .offer a {
      text-decoration: none;
      color: #2563eb;
      font-weight: 500;
    }
    .offer p {
      margin: 5px 0;
      font-size: 13px;
      color: #4b5563;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">AdMesh</div>
    </div>

    <!-- Logged Out View -->
    <div id="logged-out-view">
      <div style="text-align: center; margin-bottom: 20px;">
        <h2 style="color: #2563eb; font-size: 24px; margin: 0 0 8px;">AdMesh</h2>
        <p style="color: #4b5563; font-size: 16px; margin: 0 0 20px;">Please log in to view recommendations</p>
        <div style="width: 80px; height: 80px; margin: 0 auto 20px;">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#2563eb">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
          </svg>
        </div>
        <p style="color: #6b7280; font-size: 14px; margin: 0 0 20px;">You need to be logged in to see personalized tool recommendations.</p>
      </div>

      <div style="display: flex; flex-direction: column; gap: 12px;">
        <button id="login-btn" class="btn btn-primary">Log in on useadmesh.com</button>
        <button id="signup-btn" class="btn btn-outline">Sign up on useadmesh.com</button>
      </div>
    </div>

    <!-- Logged In View -->
    <div id="logged-in-view" style="display: none;">
      <div class="user-info">
        <div class="user-email" id="user-email"></div>
        <div class="user-role" id="user-role"></div>
        <div class="rewards-container">
          <div class="credits" id="user-confirmed-credits">Rewards: 0</div>
          <div class="pending-credits" id="user-pending-credits" style="display: none;">Pending: 0</div>
        </div>
      </div>

      <button id="logout-btn" class="btn btn-outline">Sign Out</button>

      <div class="offers-container">
        <h3>Recommended Tools</h3>
        <div id="offers">Detecting intent...</div>
      </div>
    </div>
  </div>

  <script type="module" src="popup.js"></script>
</body>
</html>
