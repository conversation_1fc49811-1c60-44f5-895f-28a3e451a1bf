
// 🧠 Create and inject the AdMesh sidebar
function createSidebar() {
  if (document.getElementById('admesh-sidebar')) return;

  const sidebar = document.createElement('div');
  sidebar.id = 'admesh-sidebar';
  sidebar.style.cssText = `
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 320px;
    background: linear-gradient(135deg, #ffffff 0%, #f0f4f8 100%);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    padding: 15px;
    z-index: 10000;
    font-family: 'Inter', sans-serif;
    border: 1px solid #e1e8ed;
    transition: all 0.3s ease;
  `;

  sidebar.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
      <div><strong style="font-size: 20px; color: #3498db;">AdMesh</strong></div>
      <div style="display: flex; align-items: center;">
        <div id="admesh-credits" style="background-color: #3498db; color: white; padding: 5px 10px; border-radius: 20px; margin-right: 10px; font-size: 12px;">
          Credits: 25
        </div>
        <button id="admesh-close" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
      </div>
    </div>
    <div id="admesh-content" style="text-align: center; padding: 20px; color: #7f8c8d;">
      <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; margin: 0 auto 15px; animation: spin 1s linear infinite;"></div>
      <div>Finding the best tools for you...</div>
    </div>
  `;

  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    #admesh-sidebar:hover {
      transform: scale(1.02);
      box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }
  `;
  document.head.appendChild(style);
  document.body.appendChild(sidebar);

  document.getElementById('admesh-close').addEventListener('click', () => {
    sidebar.remove();
  });
}

// 🧩 Render LLM-recommended tool cards
function renderToolCards(tools) {
  const content = document.getElementById('admesh-content');
  if (!content) return;

  if (!Array.isArray(tools) || tools.length === 0) {
    content.innerHTML = `
      <div style="padding: 15px;">
        <p style="color: #2c3e50; font-size: 15px;">
          Sorry, we couldn't find useful tools for your search.
        </p>
      </div>
    `;
    return;
  }

  content.innerHTML = `
    <h3 style="color: #3498db; margin-bottom: 10px;">✨ Recommended Tools</h3>
    <ul style="list-style: none; padding: 0;">
      ${tools.map(tool => `
        <li style="margin-bottom: 15px;">
          <a href="${tool.url}" target="_blank" style="display: block; padding: 15px; background: white; border-radius: 8px; color: #2c3e50; text-decoration: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e1e8ed; transition: transform 0.2s;">
            <h4 style="margin: 0; font-size: 17px; color: #3498db;">${tool.title}</h4>
            <p style="margin-top: 8px; font-size: 14px; color: #2c3e50;">${tool.reason}</p>
          </a>
        </li>
      `).join('')}
    </ul>
  `;
}

// 🔥 Main logic: fetch from /recommend
function runAdMesh(query) {
  createSidebar();

  fetch('http://localhost:3000/recommend', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query })
  })
    .then(res => {
      if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
      return res.json();
    })
    .then(renderToolCards)
    .catch(err => {
      console.error('[AdMesh] LLM request failed:', err);
      renderToolCards([]); // fallback message
    });
}

// 🚀 Trigger on Google search page
function init() {
  if (location.hostname.includes('google.com')) {
    const query = new URLSearchParams(location.search).get('q');
    if (query) {
      runAdMesh(query);
    }
  }
}

document.readyState === 'loading'
  ? document.addEventListener('DOMContentLoaded', init)
  : init();













// let offers = [];

// // Load offers.json from extension assets
// fetch(chrome.runtime.getURL('offers.json'))
//   .then((response) => response.json())
//   .then((data) => {
//     offers = data;
//   })
//   .catch((err) => {
//     console.error('Failed to load offers:', err);
//   });

// // Create and inject the sidebar
// function createSidebar() {
//   if (document.getElementById('admesh-sidebar')) return;

//   const sidebar = document.createElement('div');
//   sidebar.id = 'admesh-sidebar';
//   sidebar.style.cssText = `
//     position: fixed;
//     bottom: 20px;
//     right: 20px;
//     width: 320px;
//     background: linear-gradient(135deg, #ffffff 0%, #f0f4f8 100%);
//     border-radius: 12px;
//     box-shadow: 0 10px 25px rgba(0,0,0,0.1);
//     padding: 15px;
//     z-index: 10000;
//     font-family: 'Inter', sans-serif;
//     border: 1px solid #e1e8ed;
//     transition: all 0.3s ease;
//   `;

//   sidebar.innerHTML = `
//     <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
//       <div><strong style="font-size: 20px; color: #3498db;">AdMesh</strong></div>
//       <div style="display: flex; align-items: center;">
//         <div id="admesh-credits" style="background-color: #3498db; color: white; padding: 5px 10px; border-radius: 20px; margin-right: 10px; font-size: 12px;">
//           Credits: 25
//         </div>
//         <button id="admesh-close" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
//       </div>
//     </div>
//     <div id="admesh-content" style="text-align: center; padding: 20px; color: #7f8c8d;">
//       <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; margin: 0 auto 15px; animation: spin 1s linear infinite;"></div>
//       <div>Analyzing intent...</div>
//     </div>
//     <div style="text-align: center; margin-top: 15px;">
//       <button id="admesh-dashboard" style="background-color: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">Go to Dashboard</button>
//     </div>
//   `;

//   const style = document.createElement('style');
//   style.textContent = `
//     @keyframes spin {
//       0% { transform: rotate(0deg); }
//       100% { transform: rotate(360deg); }
//     }
//     #admesh-sidebar:hover {
//       transform: scale(1.02);
//       box-shadow: 0 15px 30px rgba(0,0,0,0.15);
//     }
//     #admesh-dashboard:hover {
//       background-color: #2980b9;
//     }
//   `;
//   document.head.appendChild(style);
//   document.body.appendChild(sidebar);

//   document.getElementById('admesh-close').addEventListener('click', () => {
//     sidebar.remove();
//   });

//   document.getElementById('admesh-dashboard').addEventListener('click', () => {
//     chrome.runtime.sendMessage({ action: 'openDashboard' });
//   });
// }

// // Fuzzy match offers using product_category against offer.tags
// function findMatchingOffers(productCategory) {
//   if (!productCategory) return [];

//   const query = productCategory.toLowerCase();
//   return offers.filter(offer =>
//     offer.tags.some(tag => query.includes(tag.toLowerCase()))
//   );
// }

// function updateSidebar(intentData, error = null) {
//   if (error) return;

//   const matchedOffers = findMatchingOffers(intentData.product_category);

//   createSidebar();
//   const content = document.getElementById('admesh-content');
//   if (!content) return;

//   if (matchedOffers.length === 0) {
//     content.innerHTML = `
//       <div style="text-align: center; padding: 20px; background-color: #f1f5f9; border-radius: 8px;">
//         <p style="color: #2c3e50; font-size: 16px;">
//           We couldn't find perfect tools for <strong>${intentData.product_category}</strong> this time.
//         </p>
//         <p style="color: #7f8c8d; margin-top: 10px;">
//           Keep exploring – your ideal solution might be just around the corner! 🌟
//         </p>
//       </div>
//     `;
//     return;
//   }

//   content.innerHTML = `
//     <div style="margin-bottom: 20px; background-color: #f1f5f9; padding: 15px; border-radius: 8px; text-align: left;">
//       <h3 style="color: #3498db; margin-bottom: 10px;">Your Creative Journey</h3>
//       <div><strong>Focus:</strong> <span style="color: #2c3e50;">${intentData.intent_type}</span></div>
//       <div><strong>Category:</strong> <span style="color: #2c3e50;">${intentData.product_category}</span></div>
//       <div><strong>Goals:</strong> <span style="color: #2c3e50;">${intentData.user_goal.join(', ')}</span></div>
//     </div>
//     <div>
//       <h3 style="color: #2c3e50; margin-bottom: 15px;">✨ Recommended Tools for You</h3>
//       <ul style="list-style: none; padding: 0;">
//         ${matchedOffers.map(offer => {
//           const trustColor = offer.trust_score >= 90 ? '#4CAF50' : '#FFA726';
//           const userEarnings = offer.payout?.amount && offer.user_share_percent
//             ? ((offer.payout.amount * offer.user_share_percent) / 100).toFixed(2)
//             : null;
//           return `
//             <li style="margin-bottom: 15px;">
//               <a href="${offer.url}" target="_blank" style="display: block; padding: 15px; background: white; border-radius: 8px; color: #2c3e50; text-decoration: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e1e8ed; transition: transform 0.2s;">
//                 <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
//                   <h4 style="margin: 0; font-size: 18px; color: #3498db;">${offer.title}</h4>
//                   <span style="background: ${trustColor}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 12px;">
//                     ${offer.trust_score || 90}% Trusted
//                   </span>
//                 </div>
//                 <p style="margin: 8px 0; font-size: 15px; color: #2c3e50; font-weight: bold;">${offer.suggestion_reason}</p>
//                 <p style="margin: 8px 0; font-size: 13px; color: #7f8c8d;">${offer.description}</p>
//                 <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 13px;">
//                   <span style="color: #2196F3; background: #E3F2FD; padding: 4px 8px; border-radius: 4px;">${offer.reward_note}</span>
//                   <span style="color: #4CAF50; background: #E8F5E9; padding: 4px 8px; border-radius: 4px;">
//                     Learn More →
//                   </span>
//                 </div>
//               </a>
//             </li>
//           `;
//         }).join('')}
//       </ul>
//     </div>
//   `;
// }


// // Listen for background script messages
// chrome.runtime.onMessage.addListener((msg) => {
//   console.log('[AdMesh] Received message:', msg);

//   if (msg.type === 'INTENT_ANALYSIS') {
//     updateSidebar(msg.intentData);
//   } else if (msg.type === 'INTENT_ERROR') {
//     console.error('[AdMesh] Intent error:', msg.error);
//   } else if (msg.type === 'UPDATE_CREDITS') {
//     const creditsElement = document.getElementById('admesh-credits');
//     if (creditsElement) {
//       creditsElement.textContent = `Credits: ${msg.credits}`;
//     }
//   }
// });

// // Trigger on Google search page
// function init() {
//   if (location.hostname.includes('google.com')) {
//     const query = new URLSearchParams(location.search).get('q');
//     if (query) {
//       chrome.runtime.sendMessage({ query });
//     }
//   }
// }

// document.readyState === 'loading'
//   ? document.addEventListener('DOMContentLoaded', init)
//   : init();
