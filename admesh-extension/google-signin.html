<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AdMesh - Sign in with Google</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    #signin-container {
      width: 100%;
      max-width: 500px;
      margin: 40px auto;
      padding: 20px;
      text-align: center;
    }
    .logo {
      margin-bottom: 20px;
    }
    .logo img {
      height: 40px;
    }
    h1 {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
    }
    .card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 30px;
      margin-bottom: 20px;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      margin: 0 auto 20px;
      animation: spin 2s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .btn {
      display: inline-block;
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      margin-top: 20px;
      text-decoration: none;
    }
    .btn-secondary {
      background-color: #f5f5f5;
      color: #333;
      border: 1px solid #ccc;
    }
    #debug-log {
      margin-top: 30px;
      padding: 10px;
      background-color: #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
      text-align: left;
      display: none;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div id="signin-container">
    <div class="logo">
      <img src="https://useadmesh.com/logo.png" alt="AdMesh Logo">
    </div>
    <h1>Sign in to AdMesh</h1>

    <div class="card">
      <!-- Loading spinner -->
      <div id="loading">
        <div class="spinner"></div>
        <h2>Connecting to Google...</h2>
        <p>Please wait while we connect to your Google account.</p>
        <p id="status-message" style="color: #666; font-size: 14px;">Initializing authentication...</p>
      </div>

      <div id="manual-signin-container" style="display: none; margin-top: 20px;">
        <p>If the Google sign-in doesn't start automatically, please click the button below:</p>
        <button id="manual-signin-btn" class="btn">Sign in with Google</button>
      </div>
    </div>

    <button id="show-debug" class="btn btn-secondary" style="font-size: 12px;">Show Debug Info</button>

    <script type="module">
      import { auth, googleProvider, signInWithPopup, signInWithRedirect } from './firebase.js';

      // Configure Google provider
      googleProvider.setCustomParameters({
        prompt: 'select_account'
      });

      // Add debug logging
      function logDebug(message, data) {
        console.log(`[Google Sign-In Debug] ${message}`, data || '');
        const debugElement = document.getElementById('debug-log');
        if (debugElement) {
          const logItem = document.createElement('div');
          logItem.textContent = `${new Date().toISOString().substr(11, 8)} - ${message}`;
          debugElement.appendChild(logItem);
          debugElement.scrollTop = debugElement.scrollHeight;
        }
      }

      // Send message to all tabs
      function broadcastToTabs(message) {
        chrome.tabs.query({}, (tabs) => {
          tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, message).catch(() => {});
          });
        });
      }

      // Attempt to sign in with Google
      document.addEventListener('DOMContentLoaded', async () => {
        // Add debug log element
        const debugLog = document.createElement('div');
        debugLog.id = 'debug-log';
        debugLog.style.cssText = 'margin-top: 30px; padding: 10px; background-color: #f0f0f0; border-radius: 4px; font-size: 12px; text-align: left; display: none; max-height: 200px; overflow-y: auto;';
        document.getElementById('signin-container').appendChild(debugLog);

        // Add debug toggle button functionality
        document.getElementById('show-debug').addEventListener('click', (e) => {
          e.preventDefault();
          const log = document.getElementById('debug-log');
          const button = document.getElementById('show-debug');
          if (log.style.display === 'none') {
            log.style.display = 'block';
            button.textContent = 'Hide Debug Log';
          } else {
            log.style.display = 'none';
            button.textContent = 'Show Debug Log';
          }
        });

        // Show manual sign-in button after 10 seconds
        setTimeout(() => {
          document.getElementById('manual-signin-container').style.display = 'block';
          document.getElementById('status-message').textContent = 'Taking longer than expected. You can try the manual sign-in button.';
        }, 10000);

        // Add manual sign-in button functionality
        document.getElementById('manual-signin-btn').addEventListener('click', (e) => {
          e.preventDefault();
          document.getElementById('status-message').textContent = 'Attempting to sign in...';
          startGoogleSignIn();
        });

        logDebug('Page loaded, starting Google sign-in process');

        // Define the Google sign-in function
        async function startGoogleSignIn() {
          try {
            logDebug('Configuring Google provider');
            // Add additional configuration to the Google provider
            googleProvider.addScope('email');
            googleProvider.addScope('profile');

            // Try redirect first if popup fails
            const useRedirect = new URLSearchParams(window.location.search).get('redirect') === 'true';

            if (useRedirect) {
              logDebug('Starting Google sign-in with redirect');
              // Sign in with Google redirect
              await signInWithRedirect(auth, googleProvider);
              // This line won't be reached as the page will redirect
              return;
            } else {
              logDebug('Starting Google sign-in popup');
              // Sign in with Google popup
              const result = await signInWithPopup(auth, googleProvider);
              logDebug('Google sign-in successful', { email: result.user.email });
              const user = result.user;

              logDebug('Getting ID token');
              // Get ID token for API call
              const idToken = await user.getIdToken();
              logDebug('ID token obtained');

              // Register with backend to set role
              logDebug('Registering with backend');
              try {
                const response = await fetch('http://localhost:8000/auth/google-onboard', {
                  method: 'POST',
                  headers: {
                    'Authorization': `Bearer ${idToken}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({ role: 'user' })
                });

                logDebug(`Backend response status: ${response.status}`);

                if (!response.ok) {
                  const errorData = await response.json();
                  logDebug('Backend error', errorData);
                  throw new Error(errorData.detail || 'Failed to register with Google');
                }

                logDebug('Backend registration successful');
              } catch (apiError) {
                logDebug('Backend API error', apiError);
                // Continue even if backend registration fails
                console.warn('Backend registration failed, but continuing with sign-in:', apiError);
              }

              // Show success message
              document.getElementById('signin-container').innerHTML = `
              <div class="logo">
                <img src="https://useadmesh.com/logo.png" alt="AdMesh Logo">
              </div>
              <h1>Sign in Successful</h1>

              <div class="card">
                <div style="width: 60px; height: 60px; background-color: #4CAF50; border-radius: 50%; margin: 0 auto 20px; display: flex; justify-content: center; align-items: center;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <h2>Successfully signed in!</h2>
                <p>You have successfully signed in to AdMesh with your Google account.</p>
                <p>You can now close this tab and return to your search results.</p>
                <a href="#" id="close-tab-btn" class="btn">Close Tab & Return to AdMesh</a>
              </div>
            `;

            // Notify all tabs that sign-in was successful
            broadcastToTabs({ type: 'GOOGLE_SIGNIN_SUCCESS', user: { uid: user.uid, email: user.email } });

            // Also notify the extension
            chrome.runtime.sendMessage({ type: 'GOOGLE_SIGNIN_SUCCESS', user: { uid: user.uid } });

            // Notify the parent window if it exists
            if (window.opener) {
              try {
                window.opener.postMessage({ type: 'GOOGLE_SIGNIN_SUCCESS', user: { uid: user.uid, email: user.email } }, '*');
              } catch (e) {
                logDebug('Error posting message to opener:', e);
              }
            }

              // Add event listener to close button
              document.getElementById('close-tab-btn').addEventListener('click', (e) => {
                e.preventDefault();
                window.close();
              });
            }
          } catch (error) {
            console.error('Error signing in with Google:', error);
            logDebug(`Sign-in error: ${error.code || 'unknown'} - ${error.message}`);

            // Log additional details if available
            if (error.email) logDebug(`Email: ${error.email}`);
            if (error.credential) logDebug(`Credential: ${error.credential}`);

            // Show error message with detailed information
            document.getElementById('signin-container').innerHTML = `
              <div class="logo">
                <img src="https://useadmesh.com/logo.png" alt="AdMesh Logo">
              </div>
              <h1>Sign in Failed</h1>

              <div class="card">
                <div style="width: 60px; height: 60px; background-color: #F44336; border-radius: 50%; margin: 0 auto 20px; display: flex; justify-content: center; align-items: center;">
                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </div>
                <h2>Sign-in failed</h2>
                <p>${error.message || 'An error occurred during sign-in.'}</p>
                <p style="font-size: 12px; color: #666;">Error code: ${error.code || 'unknown'}</p>

                <div style="margin-top: 20px;">
                  <button id="retry-button" class="btn">Try Again</button>
                  <button id="close-button" class="btn btn-secondary" style="margin-left: 10px;">Close Tab</button>
                </div>
              </div>

              <div style="margin-top: 20px; text-align: left; background: #f5f5f5; padding: 15px; border-radius: 4px; max-height: 150px; overflow-y: auto;">
                <p style="font-size: 12px; margin: 0 0 5px 0;"><strong>Debug Information:</strong></p>
                <p style="font-size: 12px; margin: 0 0 5px 0;">Time: ${new Date().toISOString()}</p>
                <p style="font-size: 12px; margin: 0 0 5px 0;">Browser: ${navigator.userAgent}</p>
                <p style="font-size: 12px; margin: 0 0 5px 0;">Extension ID: ${chrome.runtime.id}</p>
                ${error.email ? `<p style="font-size: 12px; margin: 0 0 5px 0;">Email: ${error.email}</p>` : ''}
                ${error.credential ? `<p style="font-size: 12px; margin: 0 0 5px 0;">Credential: ${error.credential}</p>` : ''}
              </div>
            `;

            // Add event listeners to buttons
            document.getElementById('retry-button').addEventListener('click', () => {
              location.reload();
            });

            document.getElementById('close-button').addEventListener('click', (e) => {
              e.preventDefault();
              window.close();
            });

            // Notify all tabs that sign-in failed
            broadcastToTabs({ type: 'GOOGLE_SIGNIN_FAILURE', error: error.message });

            // Also notify the extension
            chrome.runtime.sendMessage({ type: 'GOOGLE_SIGNIN_FAILURE', error: error.message });

            // Notify the parent window if it exists
            if (window.opener) {
              try {
                window.opener.postMessage({ type: 'GOOGLE_SIGNIN_FAILURE', error: error.message }, '*');
              } catch (e) {
                logDebug('Error posting message to opener:', e);
              }
            }
          }
        // Start the Google sign-in process
        startGoogleSignIn();
      });
    </script>
  </div>

  <script>
    // Listen for messages from the parent window
    window.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'GOOGLE_SIGNIN_SUCCESS') {
        // Close this window
        window.close();
      }
    });
  </script>
</body>
</html>
